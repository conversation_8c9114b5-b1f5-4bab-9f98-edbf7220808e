-- Self-activation log table migration script
-- This table records the lifecycle of delayed activation tasks

CREATE TABLE `gv_self_activation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `customer_order_code` varchar(100) NOT NULL COMMENT 'Customer order code - core business association key',
  `activation_token` varchar(255) NOT NULL COMMENT 'Unique activation token in the activation URL',
  `token_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT 'Token status: PENDING, ACTIVATED, EXPIRED',
  `token_expiry_at` datetime NOT NULL COMMENT 'Token expiry time (e.g., 30 days)',
  `customer_email` varchar(255) NOT NULL COMMENT 'Customer email receiving activation email',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
  PRIMARY KEY (`id`),
  <PERSON>IQUE KEY `uk_activation_token` (`activation_token`),
  <PERSON><PERSON><PERSON> `idx_customer_order_code` (`customer_order_code`),
  KEY `idx_token_status` (`token_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Self-activation task log table';

-- Add indexes for performance optimization
CREATE INDEX `idx_customer_email` ON `gv_self_activation_log` (`customer_email`);
CREATE INDEX `idx_token_expiry_at` ON `gv_self_activation_log` (`token_expiry_at`);
