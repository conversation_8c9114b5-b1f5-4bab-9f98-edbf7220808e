package com.example.datapermission;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class IssuerPermissionResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4209319348509515810L;

	private String userCode;

	private Boolean isLeaf;

	private Integer type;

	private String issuerCode;

	private List<DataPermissionResponse> dataPermissionList;
}
