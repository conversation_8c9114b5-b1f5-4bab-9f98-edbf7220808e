package com.example.sql;

import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/30 15:08
 */
public final class StringUtil{

    /** 空字符串 */
    public static final String EMPTY = "";

    /**
     * 默认字符串拼接间隔符
     */
    public static final String DEFAULT_SPLIT = ",";

    /**
     * 判断传入的字符串是否为空（含trim处理）
     *
     * @param str 字符串
     * @return 为空时true，否则false
     * <AUTHOR>
     */
    public static boolean isBlank(String str){
        int strLen;
        if (str == null || (strLen =str.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断传入的字符串是否不为空（含trim处理）
     *
     * @param str 字符串
     * @return 不为空时true，否则false
     * <AUTHOR>
     */
    public static boolean isNotBlank(String str){
        return !isBlank(str);
    }

    /**
     * 判断传入的字符串是否为空（不含trim处理）
     *
     * @param str 字符串
     * @return 为空时true，否则false
     * <AUTHOR>
     */
    public static boolean isEmpty(String str){
        return StringUtils.isEmpty(str);
    }

    /**
     * 判断传入的字符串是否不为空（不含trim处理）
     *
     * @param str 字符串
     * @return 不为空时true，否则false
     * <AUTHOR>
     */
    public static boolean isNotEmpty(String str){
        return !isEmpty(str);
    }

    /**
     * List<String>转换成拼接字符串
     *
     * @param strList 字符串List
     * @param splitMark 分隔字符标准
     * @return 转换后的拼接字符串
     * <AUTHOR>
     */
    public static String list2String(List<String> strList, String splitMark){

        if (strList == null){
            return EMPTY;
        }

        String result = "";
        for (String s : strList){
            result += s + splitMark;
        }

        return result.substring(0, result.length() - splitMark.length());
    }

    /**
     * List<String>转换成拼接字符串（使用默认分隔符号）
     *
     * @param strList 字符串List
     * @return 转换后的拼接字符串
     * <AUTHOR>
     */
    public static String list2String(List<String> strList){

        return list2String(strList, DEFAULT_SPLIT);
    }

    /**
     * 判断某字符（串）是否存在于源字符串中，
     * 任一参数值为null时返回false
     *
     * @param str
     * @param s
     * @return
     * <AUTHOR>
     */
    public static boolean contains(String source,String s){
        if (source == null || s == null){
            return false;
        }
        return source.contains(s);
    }

    public static void main(String[] args){

    }

}
