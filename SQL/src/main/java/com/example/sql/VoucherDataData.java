package com.example.sql;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.alibaba.druid.pool.DruidDataSource;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class VoucherDataData {
    static Map<String, Entity> cpgMap = new HashMap<>();
    static Map<String, Entity> articleMopCode = new HashMap<>();
    static Map<String, String> outletMap = new HashMap<>();
    static List<Entity> gvCpg = new ArrayList<>();
    static List<Entity> gvArticleMop = new ArrayList<>();
    static List<Entity> gvOutlet = new ArrayList<>();
    static List<Entity> gvCustomer = new ArrayList<>();
    static List<Entity> gvVoucherBatch = new ArrayList<>();
    static List<Entity> gvVoucherBooklet = new ArrayList<>();
    static Db use;
    private static final ConcurrentHashMap<String, String> processedRowsMap = new ConcurrentHashMap<>();


    static Set<String> errorSet = new HashSet<String>();


    static ConcurrentHashSet<String> batchCode = new ConcurrentHashSet<>();
    static ConcurrentHashSet<String> bookletCode = new ConcurrentHashSet<>();


    //开启一个线程池
    static ExecutorService executor = Executors.newFixedThreadPool(20);


    public static void writeLog(String filePath,String field,String code, String fileName) {
        executor.submit(()->{
            String message = filePath + " " + field + " " + code + " 不存在";

            String property = System.getProperty("user.dir");
            //如果errorSet存在就直接退出，如果不存在就存入并继续
            if (errorSet.contains(message)) {
                return;
            }else {
                errorSet.add(message);
            }


            try (FileWriter fw = new FileWriter(property+"/SQL/src/main/resources/sqlFile/error/"+fileName+".txt", true);
                 BufferedWriter bw = new BufferedWriter(fw);
                 PrintWriter out = new PrintWriter(bw)) {
                out.println(message);
            } catch (IOException e) {
                System.out.println("Unable to write to log file: " + e.getMessage());
            }
        });

    }

   Map<String,String> bar =  new HashMap<String,String>();




    public void progressBar(String file,Integer total){

        for (int i = 0; i <= total; i++) {
            int progress = (int) ((double)i/total * 100);
            System.out.print("\rProgress: [" + repeat("#", progress) + repeat(" ", 100-progress) + "] " + progress + "%");
            try {
                Thread.sleep(100); // 模拟任务执行时间
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public static String repeat(String str, int times) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < times; i++) {
            sb.append(str);
        }
        return sb.toString();
    }



    public static class CustomStopWatch extends StopWatch {
        @Override
        public String prettyPrint() {
            StringBuilder sb = new StringBuilder();
            sb.append("StopWatch '").append(getId()).append("': running time (seconds) = ").append(getTotalTimeSeconds()).append("\n");
            sb.append("-----------------------------------------\n");
            sb.append("ms     %     Task name\n");
            sb.append("-----------------------------------------\n");

            for (TaskInfo task : getTaskInfo()) {
                sb.append(String.format("%-5d  %-5.2f%%  %s\n",
                        task.getTimeMillis(),
                        (100.0 * task.getTimeMillis() / getTotalTimeMillis()),
                        task.getTaskName()));
            }

            return sb.toString();
        }
    }


    public List<String> getPathList(String url) throws URISyntaxException {
        // 获取 csv/voucher/physical 文件夹的路径
        Path path = Paths.get(ClassLoader.getSystemResource(url).toURI());

        List<String> fileNames = new ArrayList<>();

        // 获取并添加所有文件的名称到 list 中
        try (Stream<Path> paths = Files.walk(path)) {
            paths.filter(Files::isRegularFile)
                    .forEach(file -> fileNames.add(file.getFileName().toString()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileNames;
    }


        static {
            DruidDataSource dataSource = new DruidDataSource();
            dataSource.setUrl("*************************************************************************************************************************************************************************");
            dataSource.setUsername("gvcoreadmin");
            dataSource.setPassword("Gtech@123");

            use = Db.use(dataSource);
            gvCpg = queryMysql("gv_cpg");
            gvArticleMop = queryMysql("gv_article_mop");
            gvOutlet = queryMysql("gv_outlet");
            gvCustomer = queryMysql("gv_customer");

            gvVoucherBatch = queryMysql("gv_voucher_batch");
            gvVoucherBooklet = queryMysql("gv_voucher_booklet");

            Map<String, Entity> dbBatchCode = gvVoucherBatch.stream().collect(Collectors.toMap(x -> x.getStr("issuer_code") + x.getStr("voucher_batch_code"), x -> x));
            Map<String, Entity> dbBookletCode = gvVoucherBooklet.stream().collect(Collectors.toMap(x -> x.getStr("booklet_code"), x -> x));
            batchCode.addAll(dbBatchCode.keySet());
            bookletCode.addAll(dbBookletCode.keySet());



            cpgMap = gvCpg.stream().collect(Collectors.toMap(x -> x.getStr("cpg_name").toLowerCase(), x -> x));
            articleMopCode = gvArticleMop.stream().collect(Collectors.toMap(x -> x.getStr("article_mop_code").toLowerCase(), x -> x));
            outletMap = gvOutlet.stream().collect(Collectors.toMap(x -> x.getStr("business_outlet_code").toLowerCase(), x -> x.getStr("outlet_code"), (x1, x2) -> x2));
            dataSource.close();
        }

    public static void main(String[] args) throws IOException, ExecutionException, InterruptedException, URISyntaxException {
        VoucherDataData voucherData = new VoucherDataData();
        CustomStopWatch stopWatch = new CustomStopWatch();

        stopWatch.start("M19");
        log.info("开始执行M19");
        voucherData.importM19PhysicalCoupon();
        stopWatch.stop();

        stopWatch.start("Digital");
        log.info("开始执行电子券");
        voucherData.importDigitalVoucher();
        stopWatch.stop();

        stopWatch.start("Physical");
        log.info("开始执行实体券");
        voucherData.importPhysicalCoupon();
        stopWatch.stop();

        stopWatch.start("08Physical");
        log.info("开始执行实体券08");
        voucherData.importPhysicalCoupon08();
        stopWatch.stop();

        log.info("执行结束，耗时: " + stopWatch.prettyPrint());
    }

    public static List<Entity> queryMysql(String sql) {
        List<Entity> query = null;
        try {
            query = use.findAll(sql);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return query;
    }

    private static int countNum(String start, String end) {
        return (int) (Long.parseLong(end) - Long.parseLong(start) + 1);
    }

    private void importM19PhysicalCoupon() throws IOException, ExecutionException, InterruptedException, URISyntaxException {
        List<String> fileNameList = Lists.newArrayList(
                "7-Voucher Active Data for Paper Voucher M19 series-output.csv",
                "9-Voucher Active Data for Paper Voucher SOGO Gift Voucher-output.csv");
        processFiles(getPathList("csv/voucher/M19/"), "csv/voucher/M19/", this::readDigitalVoucherUtil);

    }




    private void importDigitalVoucher() throws IOException, ExecutionException, InterruptedException, URISyntaxException {
        List<String> fileNameList = Lists.newArrayList(
                "11-Voucher Active Data Digital- NOT in MAPCLUB-output.csv",
                "12-Voucher Active Data Digital- IN MAPCLUB-output.csv",
                "13-Voucher Active Data Digital-Barcode (Ultra Voucher)-output.csv",
                "14-Voucher Active Data for Starbucks Digital Voucher-output.csv",
                "15-Voucher Deactivated Data (Digital)-output.csv");
        processFiles(getPathList("csv/voucher/digital/"), "csv/voucher/digital/", this::readDigitalVoucherUtil);
    }

    private void importPhysicalCoupon08() throws IOException, ExecutionException, InterruptedException, URISyntaxException {
        //获取resource某个文件夹所有文件名称


        List<String> fileNameList = Lists.newArrayList(
                "2-Voucher Inventory Data stock at WH01-output.csv",
                "3-Voucher Inventory Data stock at HO01-output.csv",
                "4-Voucher Inventory Data stock at MV01-output.csv",
                "5-Voucher Inventory Data stock at MV04-output.csv",
                "6-Voucher Inventory Data stock at MAP stores-output.csv",
                "10-Voucher Deactivated Data (Paper)-output.csv ",
                "8-Voucher Active Data for Paper Voucher QC Series-output_000.csv",
                        "8-Voucher Active Data for Paper Voucher QC Series-output_001.csv",
                        "8-Voucher Active Data for Paper Voucher QC Series-output_002.csv",
                        "8-Voucher Active Data for Paper Voucher QC Series-output_003.csv",
                        "8-Voucher Active Data for Paper Voucher QC Series-output_004.csv"
                );
        processFiles(getPathList("csv/voucher/08Voucher/"), "csv/voucher/08Voucher/", this::readVoucherUtil);
    }

    private void importPhysicalCoupon() throws IOException, ExecutionException, InterruptedException, URISyntaxException {
        //获取resource某个文件夹所有文件名称


        List<String> fileNameList = Lists.newArrayList(
                "2-Voucher Inventory Data stock at WH01-output.csv",
                "3-Voucher Inventory Data stock at HO01-output.csv",
                "4-Voucher Inventory Data stock at MV01-output.csv",
                "5-Voucher Inventory Data stock at MV04-output.csv",
                "6-Voucher Inventory Data stock at MAP stores-output.csv",
                "10-Voucher Deactivated Data (Paper)-output.csv ",
                "8-Voucher Active Data for Paper Voucher QC Series-output_000.csv",
                "8-Voucher Active Data for Paper Voucher QC Series-output_001.csv",
                "8-Voucher Active Data for Paper Voucher QC Series-output_002.csv",
                "8-Voucher Active Data for Paper Voucher QC Series-output_003.csv",
                "8-Voucher Active Data for Paper Voucher QC Series-output_004.csv"
        );
        processFiles(getPathList("csv/voucher/physical/"), "csv/voucher/physical/", this::readVoucherUtil);
    }

    public void readVoucherUtil(String path) throws IOException {
        processedRowsMap.put(path,"开始读取");
        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);

        processedRowsMap.put(path,"批次进行中");
        genVoucherBatchSql(path,voucherList);

        processedRowsMap.put(path,"包进行中");
        genBookletSql(path,voucherList);

        processedRowsMap.put(path,"券进行");
        genVoucherSql(path,voucherList);

        processedRowsMap.put(path,"完成");
    }

    public void readDigitalVoucherUtil(String path) throws IOException {
        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);
        genDigitalVoucherBatchSql(path,voucherList);
        genDigitalVoucherSql(path,voucherList);
    }

    private void processFiles(List<String> fileNameList, String basePath, FileProcessor processor) throws IOException, ExecutionException, InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(fileNameList.size());
        AtomicInteger completedFiles = new AtomicInteger();
        List<Future<?>> futures = fileNameList.stream()
                .map(file -> executorService.submit(() -> {
                    try {
                        log.info("开始处理{" + file + "}");
                        String path = basePath + file;
                        processor.process(path);
                        int completed = completedFiles.incrementAndGet();
                        int total = fileNameList.size();
                        displayProgress(completed, total);
                        log.info("已完成{" + completed + "} / {" + total + "} 个文件，当前文件: {" + file + "}");
                    } catch (IOException e) {
                        log.error("处理文件 " + file + " 时发生IO异常", e);
                    } catch (ExecutionException e) {
                        throw new RuntimeException(e);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }))
                .collect(Collectors.toList());

        // 创建一个定时任务，定期打印出进度
        ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        scheduledExecutorService.scheduleAtFixedRate(() -> {
            log.info("-------------------------------------进度条开始-----------------------------------------------");
            processedRowsMap.forEach((x,y)->{
                log.info("| File path: " + x + " | Current step: " + processedRowsMap.get(x) + " |" + "\n");
            });
            log.info("-------------------------------------进度条结束------------------------------------------------");
        }, 0, 1, TimeUnit.MINUTES);


        for (Future<?> future : futures) {
            try {

                future.get();

            } catch (InterruptedException e) {

                log.error("Task was interrupted");
                e.printStackTrace();
            } catch (ExecutionException e) {

                log.error("Task threw an exception");
                e.printStackTrace();
                Throwable cause = e.getCause();
                if (cause != null) {
                    log.error("Cause: " + cause);
                    cause.printStackTrace();
                }
            }
        }
        executorService.shutdown();
        scheduledExecutorService.shutdown();
        System.gc();
    }

    @FunctionalInterface
    private interface FileProcessor {
        void process(String path) throws IOException, ExecutionException, InterruptedException;
    }

    private void importDigitalVoucherBatch(String path) throws IOException {

        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);


        genDigitalVoucherBatchSql(path, voucherList);

    }

    private static void genDigitalVoucherBatchSql(String path, List<csvToSql.Voucher> voucherList) throws IOException {
        List<csvToSql.Voucher> collect = voucherList.stream().filter(x -> StringUtils.isEmpty(x.getVoucherBatchCode())).collect(Collectors.toList());
        log.error("voucherBatch为空的数据：" + collect.size());

        Map<String, List<csvToSql.Voucher>> batchMap = voucherList.stream().filter(x -> StringUtils.hasText(x.getVoucherBatchCode())).collect(Collectors.groupingBy(csvToSql.Voucher::getVoucherBatchCode));

        String filePath = getFilePath(path, "voucherBatch");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            StringBuilder sb = new StringBuilder();
            batchMap.forEach((k, v) -> {
                if (!batchCode.add(v.get(0).getIssuerCode()+k)) {
                    return;
                }
                String cpgCode = v.get(0).getCpgCode();
                Entity articleCode = null;
                Entity entity = cpgMap.get(v.get(0).getCpgCode().toLowerCase());
                if (entity == null) {
                    writeLog(filePath,"CPG",v.get(0).getCpgCode(),"CpgError");
                } else {
                    articleCode = articleMopCode.get(entity.getStr("article_mop_code"));
                    cpgCode = entity.getStr("cpg_code");
                }

                String poNo = UUID.fastUUID().toString();
                sb.append("INSERT INTO gv_voucher_batch (purchase_order_no, issuer_code, voucher_batch_code, article_code" +
                        ", mop_code,voucher_num,denomination,voucher_effective_date,cpg_code,status,create_user) VALUES (" +
                        "'" + poNo + "'," +
                        "'" + v.get(0).getIssuerCode() + "'," +
                        "'" + v.get(0).getVoucherBatchCode() + "'," +
                        "'" + v.get(0).getArticleCodeName() + "'," +
                        "'" + (articleCode != null ? articleCode.getStr("mop_code") : "") + "'," +
                        /*"'" + v.get(0).getBookletStartNo() + "'," +
                        "'" + v.get(0).getBookletEndNo() + "'," +*/
                        "'" + String.valueOf(v.size()) + "'," +
//                        "'" + v.get(0).getBookletPerNum() + "'," +
                        "'" + v.get(0).getDenomination() + "'," +
                        "'" + effDate(v.get(0).getVoucherEffectiveDate()) + "'," +
                        "'" + cpgCode + "'," +
                        "'3'," +
                        "'DataMigration');\n");
            });
            writer.write(sb.toString());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private void voucherBatch(String path) throws IOException {
        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);

        genVoucherBatchSql(path, voucherList);


    }

    private static void genVoucherBatchSql(String path, List<csvToSql.Voucher> voucherList) throws IOException {
        Map<String, List<csvToSql.Voucher>> batchMap = voucherList.stream().collect(Collectors.groupingBy(csvToSql.Voucher::getVoucherBatchCode));

        String filePath = getFilePath(path, "voucherBatch");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            batchMap.forEach((k, v) -> {
                //if
                if (!batchCode.add(v.get(0).getIssuerCode()+v.get(0).getVoucherBatchCode())) {
                    return;
                }


                String cpgCode = v.get(0).getCpgCode();
                Entity entity = cpgMap.get(v.get(0).getCpgCode().toLowerCase());
                Entity articleCode = null;
                if (entity == null) {
                    writeLog(filePath,"CPG",v.get(0).getCpgCode(),"CpgError");
                } else {
                    articleCode = articleMopCode.get(entity.getStr("article_mop_code"));
                    cpgCode = entity.getStr("cpg_code");
                }

                String poNo = UUID.fastUUID().toString();
                String insertQuery = "INSERT INTO gv_voucher_batch (purchase_order_no, issuer_code, voucher_batch_code, article_code" +
                        ", mop_code, booklet_start_no, booklet_end_no, booklet_per_num, booklet_num, voucher_start_no,voucher_end_no,voucher_num,denomination,voucher_effective_date,cpg_code,status,create_user) VALUES (" +
                        "'" + poNo + "'," +
                        "'" + v.get(0).getIssuerCode() + "'," +
                        "'" + v.get(0).getVoucherBatchCode() + "'," +
                        "'" + v.get(0).getArticleCodeName() + "'," +
                        "'" + (articleCode != null ? articleCode.getStr("mop_code") : "") + "'," +
                        "'" + v.get(0).getBookletStartNo() + "'," +
                        "'" + v.get(0).getBookletEndNo() + "'," +
                        "'" + v.get(0).getBookletPerNum() + "'," +
                        "'" + String.valueOf(countNum(v.get(0).getBookletStartNo(), v.get(0).getBookletEndNo())) + "'," +
                        "'" + v.get(0).getBatchVoucherStartNo() + "'," +
                        "'" + v.get(0).getBatchVoucherEndNo() + "'," +
                        "'" + String.valueOf(countNum(v.get(0).getBatchVoucherStartNo(), v.get(0).getBatchVoucherEndNo())) + "'," +
                        "'" + v.get(0).getDenomination() + "'," +
                        "'" + effDate(v.get(0).getVoucherEffectiveDate()) + "'," +
                        "'" + cpgCode + "'," +
                        "'3'," +
                        "'DataMigration');";
                try {
                    writer.write(insertQuery);
                    writer.newLine();


                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }


    private void booklet(String path) throws IOException {
        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);


        genBookletSql(path, voucherList);
    }

    private static void genBookletSql(String path, List<csvToSql.Voucher> voucherList) throws IOException {
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());

        List<csvToSql.Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        filterList.addAll(voucherList.stream().filter(x -> gvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));

        String filePath = getFilePath(path, "booklet");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            Map<String, List<csvToSql.Voucher>> bookletMap = filterList.stream().collect(Collectors.groupingBy(csvToSql.Voucher::getBookletCode));

            for (Map.Entry<String, List<csvToSql.Voucher>> entry : bookletMap.entrySet()) {


                List<csvToSql.Voucher> vouchers = entry.getValue();
                csvToSql.Voucher v = vouchers.get(0); // 取第一个作为代表


                if (!bookletCode.add(v.getBookletCode())) {
                    return;
                }

                String insertQuery = "INSERT INTO gv_voucher_booklet (issuer_code, booklet_code, booklet_barcode, voucher_batch_code, voucher_start_no, voucher_end_no, booklet_per_num, status, create_user) VALUES (" +
                        "'" + v.getIssuerCode() + "'," +
                        "'" + v.getBookletCode() + "'," +
                        "'" + v.getBookletBarcode() + "'," +
                        "'" + v.getVoucherBatchCode() + "'," +
                        "'" + v.getBookletVoucherStartNo() + "'," +
                        "'" + v.getBookletVoucherEndNo() + "'," +
                        "'" + v.getBookletPerNum() + "'," +
                        "'1'," +
                        "'DataMigration');";

                try {
                    writer.write(insertQuery);
                    writer.newLine();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("插入成功");
    }

    private void voucher(String path) throws IOException {
        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);

        genVoucherSql(path, voucherList);
    }

    private static void genVoucherSql(String path, List<csvToSql.Voucher> voucherList) throws IOException {
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());

        List<csvToSql.Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        filterList.addAll(voucherList.stream().filter(x -> gvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));

        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));
        List<Entity> entities = customerByIssuerCode.get("MAP");
        Map<String, String> customerMap = entities.stream()
                .filter(x -> StringUtils.hasText(x.getStr("customer_name")) && StringUtils.hasText(x.getStr("customer_code")))
                .collect(Collectors.toMap(
                        x -> x.getStr("customer_name").toLowerCase(),
                        x -> x.getStr("customer_code"),
                        (oldValue, newValue) -> oldValue
                ));
        Map<String, String> customerCompanyMap = entities.stream()
                .filter(x -> StringUtils.hasText(x.getStr("company_name")) && StringUtils.hasText(x.getStr("customer_code")))
                .collect(Collectors.toMap(
                        x -> x.getStr("company_name").toLowerCase(),
                        x -> x.getStr("customer_code"),
                        (oldValue, newValue) -> oldValue
                ));
        String filePath = getFilePath(path, "voucher");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (csvToSql.Voucher v : filterList) {
                String originalVoucherNumber = v.getVoucherNumber();
                v.setVoucherCode(originalVoucherNumber);
                v.setVoucherNumber(originalVoucherNumber.replaceAll("[a-zA-Z]", ""));

                long index = Long.parseLong(v.getVoucherNumber()) % 64;
                Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
                if (entity != null) {
                    String articleCode = entity.getStr("article_mop_code");
                    String mopCode = articleMopCode.get(articleCode).getStr("mop_code");
                    if (cpgMap.containsKey(v.getCpgCode().toLowerCase())) {
                        v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                        v.setMopCode(mopCode);
                    } else {
                        writeLog(filePath,"CPG",v.getCpgCode(),"CpgError");                    }
                } else {
                    writeLog(filePath,"CPG",v.getCpgCode(),"CpgError");                }

                if ("customer".equalsIgnoreCase(v.getVoucherOwnerType())) {
                    if (customerByIssuerCode.containsKey(v.getIssuerCode())) {

                        if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                            v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                        } else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                            v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                        } else {

                            writeLog(filePath,"CUSTOMER",v.getVoucherOwnerCode(),"CustomerError");
                        }
                    }else {
                        writeLog(filePath,"CUSTOMER",v.getVoucherOwnerCode(),"CustomerError");
                    }
                } else if ("outlet".equalsIgnoreCase(v.getVoucherOwnerType())) {
                    if (v.getVoucherOwnerCode().equals("MV01")){
                        v.setVoucherOwnerCode("OU10240528141811000013");
                    }else if (v.getVoucherOwnerCode().equals("MV03")){
                        v.setVoucherOwnerCode("OU10240528143201000015");
                    } else if (v.getVoucherOwnerCode().equals("MV04")) {
                        v.setVoucherOwnerCode("OU10240723094409000081");
                    }else if (v.getVoucherOwnerCode().equals("HO01")) {
                        v.setVoucherOwnerCode("OU10240528141309000011");
                        v.setVoucherOwnerType("warehouse");
                    }else if (v.getVoucherOwnerCode().equals("WH01")) {
                        v.setVoucherOwnerCode("OU10240528141628000012");
                        v.setVoucherOwnerType("warehouse");
                    }else if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                        v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else {
                        writeLog(filePath,"OUTLET",v.getVoucherOwnerCode(),"OutletError");
                    }
                }
                v.setVoucherOwnerType(v.getVoucherOwnerType().toLowerCase());

                String insertQuery = "INSERT INTO gv_voucher_" + index + " ( issuer_code, voucher_batch_code, booklet_code,booklet_code_num, voucher_code,voucher_code_num" +
                        ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type,create_user) VALUES (" +
                        "'" + v.getIssuerCode() + "'," +
                        "'" + v.getVoucherBatchCode() + "'," +
                        "'" + v.getBookletCode() + "'," +
                        "'" + v.getBookletCode() + "'," +
                        "'" + v.getVoucherCode() + "'," +
                        "'" + v.getVoucherNumber() + "'," +
                        "'" + v.getCpgCode() + "'," +
                        "'" + v.getMopCode() + "'," +
                        "'" + v.getDenomination() + "'," +
                        "'" + effDate(v.getVoucherEffectiveDate()) + "'," +
                        "'" + v.getStatus() + "'," +
                        "'" + v.getVoucherStatus() + "'," +
                        "'3'," +
                        "'" + v.getVoucherOwnerCode() + "'," +
                        "'" + v.getVoucherOwnerType() + "'," +
                        "'SYSTEM_IMPORT');";
                try {
                    writer.write(insertQuery);
                    writer.newLine();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("插入成功");
    }


    private static void genVoucherSql2(String path, List<csvToSql.Voucher> voucherList) throws IOException {
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());

        List<csvToSql.Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        filterList.addAll(voucherList.stream().filter(x -> gvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));

        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));

        String filePath = getFilePath(path, "voucher");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (csvToSql.Voucher v : filterList) {
                String originalVoucherNumber = v.getVoucherNumber();
                v.setVoucherCode(originalVoucherNumber);
                v.setVoucherNumber(originalVoucherNumber.replaceAll("[a-zA-Z]", ""));

                long index = Long.parseLong(v.getVoucherNumber()) % 64;
                Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
                if (entity != null) {
                    String articleCode = entity.getStr("article_mop_code");
                    String mopCode = articleMopCode.get(articleCode).getStr("mop_code");
                    if (cpgMap.containsKey(v.getCpgCode().toLowerCase())) {
                        v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                        v.setMopCode(mopCode);
                    } else {
                        writeLog(filePath,"CPG",v.getCpgCode(),"CpgError");                    }
                } else {
                    writeLog(filePath,"CPG",v.getCpgCode(),"CpgError");                }

                if ("customer".equalsIgnoreCase(v.getVoucherOwnerType())) {
                    if (customerByIssuerCode.containsKey(v.getIssuerCode())) {
                        List<Entity> entities = customerByIssuerCode.get(v.getIssuerCode());
                        Map<String, String> customerMap = entities.stream()
                                .filter(x -> StringUtils.hasText(x.getStr("customer_name")) && StringUtils.hasText(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("customer_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                        Map<String, String> customerCompanyMap = entities.stream()
                                .filter(x -> StringUtils.hasText(x.getStr("company_name")) && StringUtils.hasText(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("company_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                        if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                            v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                        } else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                            v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                        } else {

                            writeLog(filePath,"CUSTOMER",v.getVoucherOwnerCode(),"CustomerError");
                        }
                    }
                } else if ("outlet".equalsIgnoreCase(v.getVoucherOwnerType())) {
                    if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                        v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }else {
                        writeLog(filePath,"OUTLET",v.getVoucherOwnerCode(),"OutletError");
                    }
                }

                String insertQuery = "INSERT INTO gv_voucher_" + index + " ( issuer_code, voucher_batch_code, booklet_code,booklet_code_num, voucher_code,voucher_code_num" +
                        ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type,create_user) VALUES (" +
                        "'" + v.getIssuerCode() + "'," +
                        "'" + v.getVoucherBatchCode() + "'," +
                        "'" + v.getBookletCode() + "'," +
                        "'" + v.getBookletCode() + "'," +
                        "'" + v.getVoucherCode() + "'," +
                        "'" + v.getVoucherNumber() + "'," +
                        "'" + v.getCpgCode() + "'," +
                        "'" + v.getMopCode() + "'," +
                        "'" + v.getDenomination() + "'," +
                        "'" + effDate(v.getVoucherEffectiveDate()) + "'," +
                        "'" + v.getStatus() + "'," +
                        "'" + v.getVoucherStatus() + "'," +
                        "'3'," +
                        "'" + v.getVoucherOwnerCode() + "'," +
                        "'" + v.getVoucherOwnerType() + "'," +
                        "'SYSTEM_IMPORT');";
                try {
                    writer.write(insertQuery);
                    writer.newLine();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("插入成功");
    }



    private void DigitalVoucher(String path) throws IOException {


        CsvReader reader = CsvUtil.getReader();
        reader.setFieldSeparator(';');
        List<csvToSql.Voucher> voucherList = reader.read(ResourceUtil.getUtf8Reader(path), csvToSql.Voucher.class);


        genDigitalVoucherSql(path, voucherList);
    }

    private static void genDigitalVoucherSql(String path, List<csvToSql.Voucher> voucherList) throws IOException {
        voucherList = voucherList.stream().filter(x -> !StringUtils.isEmpty(x.getVoucherNumber())).collect(Collectors.toList());

        List<csvToSql.Voucher> filterList = new ArrayList<>();
        List<Entity> gvVoucher = new CopyOnWriteArrayList<>();

        filterList.addAll(voucherList.stream().filter(x -> gvVoucher.stream().noneMatch(entity -> entity.getStr("voucher_code").equals(x.getVoucherNumber()))).collect(Collectors.toList()));

        Map<String, List<Entity>> customerByIssuerCode = gvCustomer.stream().collect(Collectors.groupingBy(x -> x.getStr("issuer_code"), Collectors.toList()));

        String filePath = getFilePath(path, "voucher");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (csvToSql.Voucher v : filterList) {
                String originalVoucherNumber = v.getVoucherNumber();
                v.setVoucherCode(originalVoucherNumber);
                v.setVoucherNumber(v.getVoucherNumber().replaceAll("[a-zA-Z]", ""));

                long index = Long.parseLong(v.getVoucherNumber()) % 64;
                Entity entity = cpgMap.get(v.getCpgCode().toLowerCase());
                if (entity != null) {
                    String articleCode = entity.getStr("article_mop_code");
                    String mopCode = articleMopCode.get(articleCode).getStr("mop_code");
                    if (cpgMap.containsKey(v.getCpgCode().toLowerCase())) {
                        v.setCpgCode(cpgMap.get(v.getCpgCode().toLowerCase()).getStr("cpg_code"));
                        v.setMopCode(mopCode);
                    } else {
                        writeLog(filePath,"CPG",v.getCpgCode(),"CpgError");

                    }
                } else {
                    writeLog(filePath,"CPG",v.getCpgCode(),"CpgError");
                }

                if ("customer".equalsIgnoreCase(v.getVoucherOwnerType())) {
                    if (customerByIssuerCode.containsKey(v.getIssuerCode())) {
                        List<Entity> entities = customerByIssuerCode.get(v.getIssuerCode());
                        Map<String, String> customerMap = entities.stream()
                                .filter(x -> StringUtils.hasText(x.getStr("customer_name")) && StringUtils.hasText(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("customer_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                        Map<String, String> customerCompanyMap = entities.stream()
                                .filter(x -> StringUtils.hasText(x.getStr("company_name")) && StringUtils.hasText(x.getStr("customer_code")))
                                .collect(Collectors.toMap(
                                        x -> x.getStr("company_name").toLowerCase(),
                                        x -> x.getStr("customer_code"),
                                        (oldValue, newValue) -> oldValue
                                ));
                        if (customerMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                            v.setVoucherOwnerCode(customerMap.get(v.getVoucherOwnerCode().toLowerCase()));
                        } else if (customerCompanyMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                            v.setVoucherOwnerCode(customerCompanyMap.get(v.getVoucherOwnerCode().toLowerCase()));
                        } else {
                            writeLog(filePath,"CUSTOMER",v.getVoucherOwnerCode(),"CustomerError");
                        }
                    }else {
                        writeLog(filePath,"CUSTOMER",v.getVoucherOwnerCode(),"CustomerError");
                    }
                } else if ("outlet".equalsIgnoreCase(v.getVoucherOwnerType())) {
                    if (outletMap.containsKey(v.getVoucherOwnerCode().toLowerCase())) {
                        v.setVoucherOwnerCode(outletMap.get(v.getVoucherOwnerCode().toLowerCase()));
                    }
                }
                v.setVoucherOwnerType(v.getVoucherOwnerType().toLowerCase());
                String insertQuery = "INSERT INTO gv_voucher_" + index + " ( issuer_code, voucher_batch_code, voucher_code,voucher_code_num" +
                        ",cpg_code, mop_code,denomination,voucher_effective_date,status,voucher_status,circulation_status,voucher_owner_code,voucher_owner_type,create_user) VALUES (" +
                        "'" + v.getIssuerCode() + "'," +
                        "'" + v.getVoucherBatchCode() + "'," +
                        /*"'" + v.getBookletCode() + "'," +
                        "'" + v.getBookletCode() + "'," +*/
                        "'" + v.getVoucherCode() + "'," +
                        "'" + v.getVoucherNumber() + "'," +
                        "'" + v.getCpgCode() + "'," +
                        "'" + v.getMopCode() + "'," +
                        "'" + v.getDenomination() + "'," +
                        "'" + effDate(v.getVoucherEffectiveDate()) + "'," +
                        "'" + v.getStatus() + "'," +
                        "'" + v.getVoucherStatus() + "'," +
                        "'3'," +
                        "'" + v.getVoucherOwnerCode() + "'," +
                        "'" + v.getVoucherOwnerType() + "'," +
                        "'SYSTEM_IMPORT');";
                try {
                    writer.write(insertQuery);
                    writer.newLine();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("插入成功");
    }

    private static String effDate(String date){

        return StringUtil.isBlank(date)? date: date + " 23:59:59";
    }



    private static String getFilePath(String path, String suffix) {
        String property = System.getProperty("user.dir");

        String filePath = path.replace(".csv", "_" + suffix + ".sql")
                .replace("csv/",property+"/SQL/src/main/resources/sqlFile/");
        File file = new File(filePath);
        if (!file.exists()) {
            try {
                if (file.createNewFile()) {
                    log.info("创建文件成功: " + filePath);
                } else {
                    log.info("文件已存在: " + filePath);
                }
            } catch (IOException e) {
                log.error("创建文件失败: " + filePath, e);
            }
        }
        return filePath;
    }

    private static void displayProgress(int completed, int total) {
        int progress = (int) (((double) completed / total) * 100);
        System.out.print("\r处理进度: " + progress + "% [" + completed + "/" + total + "]");
    }
}

