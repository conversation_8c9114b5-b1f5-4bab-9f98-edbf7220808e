package com.example.sql;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

public class LiabilityCpg {


    final static String property = System.getProperty("user.dir")+ "/SQL/";
    final static String salesDataFile = property + "src/main/resources/processed/salesData.txt";
    final static String usageDataFile = property + "src/main/resources/processed/usageData.txt";
    final static String dbTransactionData = property + "src/main/resources/dbTransactionData/dbTransactionData.txt";
    final static String deleteDataFile = property + "src/main/resources/processed/deleteData.txt";
    final static String addDataFile = property + "src/main/resources/processed/addData.txt";
    final static String addDataByCpg = property + "src/main/resources/addDataByCpg/";
    final static String deleteDataByCpg = property + "src/main/resources/deleteDataByCpg/";

    static ForkJoinPool forkJoinPool = new ForkJoinPool(100);

    private static final ReentrantLock fileLock = new ReentrantLock();



    public static void main(String[] args) {

        //readAndStoreFilteredDataUsingPOI(property + "src/main/resources/sales", salesDataFile);
        //readAndStoreFilteredDataUsingPOI(property + "src/main/resources/redeem", usageDataFile);

        Set<LiabilityScript.TransactionData> salesData = readTransactionDataFromFile(salesDataFile);
        Set<LiabilityScript.TransactionData> usageData = readTransactionDataFromFile(usageDataFile);
        Set<LiabilityScript.TransactionData> addData = readTransactionDataFromFile(addDataFile);
        Set<LiabilityScript.TransactionData> deleteData = readTransactionDataFromFile(deleteDataFile);


        Map<String, String> salesDataByCpg = salesData.stream().collect(Collectors.toMap(x -> x.getVoucherCode(), x -> x.getCpgName()));
        Map<String, String> usageDataByCpg = usageData.stream().collect(Collectors.toMap(x -> x.getVoucherCode(), x -> x.getCpgName()));

        Map<String,Set<String>> result = new HashMap<String,Set<String>>();
        HashMap<String, String> dataCpg = new HashMap<>();
        dataCpg.putAll(salesDataByCpg);
        dataCpg.putAll(usageDataByCpg);
        System.out.println("----------------------ADD----------------------");
        addData.forEach(x->{

            String getOrDefault = dataCpg.getOrDefault(x.getVoucherCode(), "");
            if (result.containsKey(getOrDefault)){
                result.get(getOrDefault).add(x.getVoucherCode());
            }else {
                result.put(getOrDefault, new HashSet<>(Arrays.asList(x.getVoucherCode())));
            }
        });


        result.forEach((k,v)->{
            System.out.println(k +":"+ v.size());
            writeDataToFile(addDataByCpg+k+".txt", v);
        });

        result.clear();
        System.out.println("---------------------DELETE-----------------------");

        deleteData.forEach(x->{

            String getOrDefault = dataCpg.getOrDefault(x.getVoucherCode(), "");
            if (result.containsKey(getOrDefault)){
                result.get(getOrDefault).add(x.getVoucherCode());
            }else {
                result.put(getOrDefault, new HashSet<>(Arrays.asList(x.getVoucherCode())));
            }
        });


        result.forEach((k,v)->{
            System.out.println(k +":"+ v.size());
            writeDataToFile(deleteDataByCpg+k+".txt", v);
        });

    }

    private static void writeDataToFile(String filePath, Set<String> data) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (String line : data) {
                if (StringUtil.isBlank(line)){
                    System.out.println("空字符串");
                    continue;
                }
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private static Map<String, List<LiabilityScript.TransactionData>> querySalesTransactionData(List<String> voucherCodes, Integer index,String outPath) {
        HashMap<String, List<LiabilityScript.TransactionData>> resultMap = new HashMap<>();
        for (String voucherCode : voucherCodes) {
            try (Connection conn = DbUtil.use().getConnection()) {
                List<Entity> entityList = SqlExecutor.query(conn, "select * from " + "gv_voucher_" + index + " where voucher_code = ?", new EntityListHandler(), voucherCode);
                List<LiabilityScript.TransactionData> voucherTransactionData = entityList.stream().map(x -> {
                    LiabilityScript.TransactionData transactionData = new LiabilityScript.TransactionData();
                    transactionData.setVoucherCode(x.getStr("voucher_code"));
                    transactionData.setTransactionDate(x.getDate("transaction_date"));
                    transactionData.setTransactionType(TransactionTypeEnum.getTypeDesc(x.getStr("transaction_type")));
                    return transactionData;
                }).collect(Collectors.toList());


                resultMap.put(voucherCode, voucherTransactionData);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return resultMap;
    }



    private static void writerTransactionData(String outputFile, List<LiabilityScript.TransactionData> transactionData) {
        fileLock.lock();
        try {
            List<String> lines = transactionData.stream()
                    .map(td -> td.getVoucherCode() + "," + td.getCpgName())
                    .collect(Collectors.toList());
            Files.write(Paths.get(outputFile), lines, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            fileLock.unlock();
        }
    }


    private static void readAndStoreFilteredDataUsingPOI(String folderPath, String outputFile) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".xlsx") || name.toLowerCase().endsWith(".xls"));

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            if (files != null) {
                for (File file : files) {
                    System.out.println("Processing file: " + file.getAbsolutePath());
                    try (InputStream inputStream = new FileInputStream(file);
                         Workbook workbook = WorkbookFactory.create(inputStream)) {

                        Sheet sheet = workbook.getSheetAt(0);
                        Map<String, Integer> columnIndices = getColumnIndices(sheet);
                        for (Row row : sheet) {
                            handleRow(row, writer, columnIndices);
                        }
                    } catch (Exception e) {
                        System.out.println("Error reading file: " + file.getAbsolutePath());
                        e.printStackTrace();
                    }
                }
            } else {
                System.out.println("No files found in folder: " + folderPath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private static Map<String, Integer> getColumnIndices(Sheet sheet) {
        Map<String, Integer> columnIndices = new HashMap<>();
        Row headerRow = sheet.getRow(0);
        for (Cell cell : headerRow) {
            columnIndices.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }
        return columnIndices;
    }


    private static void handleRow(Row row, BufferedWriter writer,  Map<String, Integer> columnIndices) throws IOException {
        if (row.getRowNum() == 0) {
            System.out.println("Header row: " + row);
            return; // 忽略标题行
        }

        String voucherCode = getCellValue(row.getCell(columnIndices.get("Voucher Number")));
        String dateStr = getCellValue(row.getCell(columnIndices.get("Transaction Date")));
        String transactionType = getCellValue(row.getCell(columnIndices.get("Transaction Type")));
        String cpgName = getCellValue(row.getCell(columnIndices.get("Voucher Program Group")));

        System.out.println("Row index: " + row.getRowNum() + ", Voucher Code: " + voucherCode + ", Date: " + dateStr + ", Transaction Type: " + transactionType+ ", CPG: " + cpgName);

        try {
                String line = voucherCode + "," + dateStr+ "," + transactionType + "," + cpgName;
                writer.write(line);
                writer.newLine();
        } catch (Exception e) {
            System.out.println("Error processing row: " + e.getMessage());
            e.printStackTrace();
        }
    }


    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    private static Set<LiabilityScript.TransactionData> readTransactionDataFromFile(String filePath) {
        Set<LiabilityScript.TransactionData> data = new HashSet<>();
        System.out.println("Reading data from file: " + filePath);
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {

                String[] fields = line.split(",");
                LiabilityScript.TransactionData transactionData = new LiabilityScript.TransactionData();
                transactionData.setVoucherCode(fields[0]);

                if (filePath.equals(salesDataFile)
                    || filePath.equals(usageDataFile)
                    ) {
                    transactionData.setCpgName(fields[3]);
                }

                data.add(transactionData);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("Finished reading data from file: " + filePath + ", total records: " + data.size());
        return data;
    }


}
