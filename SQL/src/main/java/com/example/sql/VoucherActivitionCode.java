package com.example.sql;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.poi.excel.ExcelReader;
import com.alibaba.druid.pool.DruidDataSource;
import lombok.Data;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class VoucherActivitionCode {

    static Map<String, Entity> cpgMap = new HashMap<>();
    static Map<String, Entity> articleMopCode = new HashMap<>();
    static Map<String, String> outletMap = new HashMap<>();
    static List<Entity> gvCpg = new ArrayList<>();
    static List<Entity> gvArticleMop = new ArrayList<>();
    static List<Entity> gvOutlet = new ArrayList<>();
    static List<Entity> gvCustomer = new ArrayList<>();
    static List<Entity> gvVoucherBatch = new ArrayList<>();
    static List<Entity> gvVoucherBooklet = new ArrayList<>();
    static Db use;
    private static final ConcurrentHashMap<String, String> processedRowsMap = new ConcurrentHashMap<>();


    static Set<String> errorSet = new HashSet<String>();


    static ConcurrentHashSet<String> batchCode = new ConcurrentHashSet<>();
    static ConcurrentHashSet<String> bookletCode = new ConcurrentHashSet<>();
    static {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl("*************************************************************************************************************************************************************************");
        dataSource.setUsername("gvcoreadmin");
        dataSource.setPassword("Gtech@123");


        dataSource.close();
    }

    public static List<Entity> queryMysql(String sql) {
        List<Entity> query = null;
        try {
            query = use.findAll(sql);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return query;
    }


    public static void main(String[] args) {
        String sql = "UPDATE `gv_voucher_%%%` SET `voucher_active_code` = '***' WHERE `voucher_code` = '$$$' AND `status` = '0' AND mop_code = 'VCE' AND (voucher_active_code = NULL or voucher_active_code = '');";
        String sql2 = "UPDATE `gv_voucher_%%%` SET `voucher_active_code` = '***' WHERE `voucher_code` = '$$$' ;";

        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader("/Users/<USER>/IdeaProjects/gvcore/SQL/src/main/resources/MobilePulsa_EGV_Detail_Not_Activated_210824.xlsx");

        List<VoucherActivitionCodeClass> voucherActivitionCodeClasses = reader.readAll(VoucherActivitionCodeClass.class);
        voucherActivitionCodeClasses.forEach(x->{
            System.out.println(sql.replace("%%%",String.valueOf(Long.valueOf(x.getCardNumber())%64)).replace("***",x.getActivationCode()).replace("$$$",x.getCardNumber()));
        });

    }





    @Data
    static class VoucherActivitionCodeClass{
        String CardNumber;
        String ActivationCode;
    }
    @Data
    static class VoucherActivitionCodeOldClass{
        String CardNumber;
        String ActivationCode;
    }




}
