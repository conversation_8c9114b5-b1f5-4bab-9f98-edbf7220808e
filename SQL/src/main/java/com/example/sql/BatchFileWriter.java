package com.example.sql;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;

public class BatchFileWriter {


    public static void main(String[] args) {


        /*String sql = "ALTER TABLE `gv_voucher_$$$` " +
                "MODIFY COLUMN `voucher_owner_code` varchar(255)   DEFAULT NULL COMMENT 'voucher owner code' ;";*/
        String sql = "UPDATE gv_voucher_$$$ SET article_code = 'warehouse' WHERE voucher_owner_code = 'OU10240528141309000011';";
        //String sql = "Delete from gv_voucher_$$$ where issuer_code = 'IS102205191134000001';";
        for (int i = 0; i < 64; i++) {

            System.out.println(sql.replace("$$$",String.valueOf(i)));

        }


    }


}