package com.example.sql;

import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import org.apache.commons.compress.utils.Lists;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

public class demo {
    final static String property = System.getProperty("user.dir")+ "/SQL/";
    final static String salesDataFile = property + "src/main/resources/processed/salesData.txt";
    final static String usageDataFile = property + "src/main/resources/processed/usageData.txt";
    final static String dbTransactionData = property + "src/main/resources/dbTransactionData/testDb.txt";
    private static final ReentrantLock fileLock = new ReentrantLock();

    public static void main(String[] args) throws SQLException {
        ExecutorService executorService = Executors.newFixedThreadPool(64);
        CompletionService<Map<String, List<LiabilityScript.TransactionData>>> completionService = new ExecutorCompletionService<>(executorService);


        List<String> dbTransactionData = LiabilityScript.getTxtFilesContentFromFolder(property + "src/main/resources/dbTransactionData");
        List<LiabilityScript.TransactionData> dbTransactionDataList = LiabilityScript.parseDbTransactionData(dbTransactionData);
        Set<LiabilityScript.TransactionData> salesData = LiabilityScript.readTransactionDataFromFile(salesDataFile);
        Set<LiabilityScript.TransactionData> usageData = LiabilityScript.readTransactionDataFromFile(usageDataFile);
        List<String> salesCode = salesData.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList());
        List<String> usageCode = usageData.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList());
        List<String> transactionData = dbTransactionDataList.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList());

        //检查dbTransactionData中不存在，salesCode或者usageCode存在的
        Set<String> transactionDataSet = new HashSet<>(transactionData);

        List<String> salesNotEx = salesCode.stream()
                .filter(x -> !transactionDataSet.contains(x))
                .collect(Collectors.toList());

        List<String> usageNotEx = usageCode.stream()
                .filter(x -> !transactionDataSet.contains(x))
                .collect(Collectors.toList());
        //合并list
        List<String> list = new ArrayList<>();
        list.addAll(salesNotEx);
        list.addAll(usageNotEx);
        System.out.println("salesNotEx:" + salesNotEx.size());
        System.out.println("usageNotEx:" + usageNotEx.size());

        Map<Integer, List<String>> integerListMap = groupByMod(list, 64);

        int numTasksRedeem = 0;
        for (Map.Entry<Integer, List<String>> entry : integerListMap.entrySet()) {
            completionService.submit(() -> querySalesTransactionData(entry.getValue(), entry.getKey()));
            numTasksRedeem++;
        }

        ConcurrentHashMap<String, List<LiabilityScript.TransactionData>> usageMaps = new ConcurrentHashMap<>();
        LiabilityScript.getResult(numTasksRedeem, completionService, usageMaps);
        System.out.println(usageMaps.size());
        System.out.println("完成-------------------------");


    }
    private static Map<Integer, List<String>> groupByMod(List<String> vouchers, int mod) {
        return vouchers.stream()
                .collect(Collectors.groupingBy(voucher -> {
                    String numericPart = voucher.replaceAll("[a-zA-Z]", "");
                    return Integer.valueOf((int) (Long.valueOf(numericPart) % mod));
                }));
    }


    private static List<List<String>> splitList(List<String> list, int size) {
        List<List<String>> parts = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            parts.add(new ArrayList<>(list.subList(i, Math.min(list.size(), i + size))));
        }
        return parts;
    }

    private static Map<String, List<LiabilityScript.TransactionData>> querySalesTransactionData(List<String> voucherCodes, Integer index) {
        HashMap<String, List<LiabilityScript.TransactionData>> resultMap = new HashMap<>();
        List<List<String>> lists = splitList(voucherCodes, 1000);

        for (List<String> batch : lists) {


            try (Connection conn = DbUtil.use().getConnection()) {
                String placeholders = batch.stream().map(code -> "?").collect(Collectors.joining(","));
                String query = "select * from gv_transaction_data_" + index + " where voucher_code IN (" + placeholders + ")" +
                        " and transaction_type in ('10','20','1','11')";
                List<Entity> entityList = SqlExecutor.query(conn, query, new EntityListHandler(), batch.toArray());
                System.out.println("数据库查询数据"+entityList.size());

                //List<Entity> entityList = SqlExecutor.query(conn, "select * from " + "gv_transaction_data_" + index + " where voucher_code = ? and transaction_type in ('10','20','1','11')", new EntityListHandler(), voucherCode);
                List<LiabilityScript.TransactionData> voucherTransactionData = entityList.stream().map(x -> {
                    LiabilityScript.TransactionData transactionData = new LiabilityScript.TransactionData();
                    transactionData.setVoucherCode(x.getStr("voucher_code"));
                    transactionData.setTransactionDate(x.getDate("transaction_date"));
                    transactionData.setTransactionType(TransactionTypeEnum.getTypeDesc(x.getStr("transaction_type")));
                    return transactionData;
                }).collect(Collectors.toList());

                writerTransactionData(dbTransactionData, voucherTransactionData);
                Map<String, List<LiabilityScript.TransactionData>> voucherGroupBy = voucherTransactionData.stream().collect(Collectors.groupingBy(x -> x.getVoucherCode()));
                resultMap.putAll(voucherGroupBy);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return resultMap;
    }
    private static void writerTransactionData(String outputFile, List<LiabilityScript.TransactionData> transactionData) {
        fileLock.lock();
        try {
            List<String> lines = transactionData.stream()
                    .map(td -> td.getVoucherCode() + "," + td.getTransactionDate() + "," + td.getTransactionType())
                    .collect(Collectors.toList());
            System.out.println("写入文件：" + lines.size());
            Files.write(Paths.get(outputFile), lines, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            fileLock.unlock();
        }
    }



}
