package com.example.sql;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
public class LiabilityScript {

    final static String property = System.getProperty("user.dir")+ "/SQL/";
    final static String salesDataFile = property + "src/main/resources/processed/salesData.txt";
    final static String usageDataFile = property + "src/main/resources/processed/usageData.txt";
    final static String dbTransactionData = property + "src/main/resources/dbTransactionData/dbTransactionData.txt";
    final static String deleteDataFile = property + "src/main/resources/processed/deleteData.txt";
    final static String addDataFile = property + "src/main/resources/processed/addData.txt";
    final static DateTime start = DateUtil.parse("2024-09-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    final static DateTime end = DateUtil.parse("2024-09-08 05:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

    static ForkJoinPool forkJoinPool = new ForkJoinPool(100);

    public static final int BATCH_SIZE = 1000;

    public static final ReentrantLock fileLock = new ReentrantLock();
    public static void main(String[] args) {

        /**
         *
         * 券号来源:销售报表从9.1-9.8的销售数据和使用数量
         *  交易类型：TransactionType：
         *  GIFT_CARD_REDEEM("1", "GIFT VOUCHER REDEEM")
         *  GIFT_CARD_SELL("10","GIFT VOUCHER SELL")
         *  GIFT_CARD_CANCEL_SELL("20", "GIFT VOUCHER CANCEL SELL")
         */

        /**
         * 1. 有销售数据，也有使用数据，L98不存在， 不需要补数据
         * 	1.1如果是先取消，再销售，再使用的，需要补数据
         * 2. 有销售数据，没有使用数据，L98存在，是需要删除的。
         * 	2.2如果是先取消，再销售的，L98存在，不需要补数据
         * 3. 无销售数据，有使用数据的，L98不存在，需要补数据。
         */

        ExecutorService executorService = Executors.newFixedThreadPool(64);
        CompletionService<Map<String, List<TransactionData>>> completionService = new ExecutorCompletionService<>(executorService);




        createDirectoryIfNotExists(property + "src/main/resources/processed");


        // 流式读取并存储数据
        readAndStoreFilteredDataUsingPOI(property + "src/main/resources/sales", salesDataFile, start, end);
        readAndStoreFilteredDataUsingPOI(property + "src/main/resources/redeem", usageDataFile, start, end);

        // 从文件中读取数据
        Set<TransactionData> salesData = readTransactionDataFromFile(salesDataFile);
        Set<TransactionData> usageData = readTransactionDataFromFile(usageDataFile);
        log.info("读取sales和redeem数据完毕");
        // 获取resources下L98文件夹中的所有txt文件
        List<String> l98Data = getTxtFilesContentFromFolder(property + "src/main/resources/L98");
        log.info("读取L98数据完毕");
        // 将数据存储到Set中
        Set<TransactionData> l98DataSet = new HashSet<>(parseL98Data(l98Data));

        // 获取所有券号
        List<String> salesVoucherCodes = salesData.stream()
                .map(TransactionData::getVoucherCode)
                .collect(Collectors.toList());

        List<String> usageVoucherCodes = usageData.stream()
                .map(TransactionData::getVoucherCode)
                .collect(Collectors.toList());

        //salesVoucher 根据取模64分组
        Map<Integer, List<String>> salesGroupedByMod = groupByMod(salesVoucherCodes, 64);
        Map<Integer, List<String>> usageGroupedByMod = groupByMod(usageVoucherCodes, 64);

        int numTasksSales = 0;
        int numTasksRedeem = 0;


        // 收集销售和使用结果
        Map<String, List<TransactionData>> salesResults = new ConcurrentHashMap<>();
        Map<String, List<TransactionData>> usageResults = new ConcurrentHashMap<>();



        // 查询销售数据
//        for (Map.Entry<Integer, List<String>> entry : salesGroupedByMod.entrySet()) {
//            completionService.submit(() -> querySalesTransactionData(entry.getValue(), entry.getKey()));
//            numTasksSales++;
//        }
//
//        getResult(numTasksSales, completionService, salesResults);


        // 查询使用数据
//        for (Map.Entry<Integer, List<String>> entry : usageGroupedByMod.entrySet()) {
//            completionService.submit(() -> querySalesTransactionData(entry.getValue(), entry.getKey()));
//            numTasksRedeem++;
//        }
//
//        getResult(numTasksRedeem, completionService, usageResults);

        List<String> dbTransactionData = getTxtFilesContentFromFolder(property + "src/main/resources/dbTransactionData");


        //根据逗号分割，转为javaBean
        List<TransactionData> dbTransactionDataList = parseDbTransactionData(dbTransactionData);
        DateTime transactionDataStart = DateUtil.parse("2024-08-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        dbTransactionDataList = dbTransactionDataList.stream().filter(x-> DateUtil.isIn(x.getTransactionDate(), transactionDataStart, end)).collect(Collectors.toList());

        log.info("读取TransactionData数据完毕");
        Map<String, List<TransactionData>> salesKey = salesData.stream().collect(Collectors.groupingBy(x -> x.getVoucherCode()));
        Map<String, List<TransactionData>> usageKey = usageData.stream().collect(Collectors.groupingBy(x -> x.getVoucherCode()));

        //收集销售数据和使用数据的交易记录
        Map<String, List<TransactionData>> groupByVoucherCode = dbTransactionDataList.stream().collect(Collectors.groupingBy(x -> x.getVoucherCode()));
        groupByVoucherCode.forEach((k,v)->{
            if (salesKey.containsKey(k)){
                salesResults.put(k,v);
            }else if (usageKey.containsKey(k)){
                usageResults.put(k,v);
            }
        });
        log.info("收集销售数据和使用数据的交易记录数据完毕");





        // 有销售数据，也有使用数据，L98不存在，不需要补数据
        // 1.1如果是先取消，再销售，再使用的，需要补数据
        // 有销售数据，没有使用数据，L98存在，是需要删除的。
        // 2.2如果是先取消，再销售的，L98存在，不需要补数据
        // 无销售数据，有使用数据的，L98不存在，需要补数据。

        Set<String> dataToAdd = new ConcurrentSkipListSet<>();
        Set<String> dataToDelete = new ConcurrentSkipListSet<>();
        Set<String> voucherCodes = new HashSet<>();
        voucherCodes.addAll(salesResults.keySet());
        voucherCodes.addAll(usageResults.keySet());

        Set<String> salesVoucherCodeSet = new HashSet<>(salesVoucherCodes);
        Set<String> usageVoucherCodeSet = new HashSet<>(usageVoucherCodes);
        Set<String> l98VoucherCodeSet = l98DataSet.stream().map(TransactionData::getVoucherCode).collect(Collectors.toSet());

        // 预处理数据
        Map<String, Boolean> wasCancelledThenSoldCache = new ConcurrentHashMap<>();
        Map<String, Boolean> wasCancelledThenSoldThenUsedCache = new ConcurrentHashMap<>();
        log.info("预处理数据完毕");
        salesResults.keySet().forEach(voucherCode ->
                wasCancelledThenSoldCache.put(voucherCode, wasCancelledThenSold(salesResults, voucherCode))
        );

        voucherCodes.forEach(voucherCode ->
                wasCancelledThenSoldThenUsedCache.put(voucherCode, wasCancelledThenSoldThenUsed(salesResults, usageResults, voucherCode))
        );

        List<CompletableFuture<Void>> futures = voucherCodes.stream()
                .map(voucherCode -> processVoucherCode(voucherCode, salesVoucherCodeSet,usageVoucherCodeSet, l98VoucherCodeSet, wasCancelledThenSoldCache, wasCancelledThenSoldThenUsedCache, dataToAdd, dataToDelete))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        try {
            allOf.get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        // 输出结果
        // 写入结果到文件
        writeDataToFile(deleteDataFile, dataToDelete);
        writeDataToFile(addDataFile, dataToAdd);
        log.info("完成！----------------------------");
        executorService.shutdown();
    }

    public static List<List<String>> splitList(List<String> list, int size) {
        List<List<String>> parts = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            parts.add(new ArrayList<>(list.subList(i, Math.min(list.size(), i + size))));
        }
        return parts;
    }



    /*public static CompletableFuture<Void> processVoucherCode(String voucherCode, List<String> salesVoucherCodes, Set<TransactionData> l98DataSet,
                                                             Map<String, List<TransactionData>> salesResults, Map<String, List<TransactionData>> usageResults,
                                                             Set<String> dataToAdd, Set<String> dataToDelete) {
        return CompletableFuture.runAsync(() -> {
            boolean hasUsage = salesVoucherCodes.contains(voucherCode);
            boolean inL98 = l98DataSet.stream().anyMatch(x -> x.getVoucherCode().equals(voucherCode));

            if (hasUsage && !inL98) {
                boolean wasCancelledThenSoldThenUsed = wasCancelledThenSoldThenUsed(salesResults, usageResults, voucherCode);
                if (wasCancelledThenSoldThenUsed) {
                    synchronized (dataToAdd) {
                        dataToAdd.add(voucherCode);
                    }
                }
            } else if (!hasUsage && inL98) {
                boolean wasCancelledThenSold = wasCancelledThenSold(salesResults, voucherCode);
                if (!wasCancelledThenSold) {
                    synchronized (dataToDelete) {
                        dataToDelete.add(voucherCode);
                    }
                }
            }
        }, forkJoinPool);
    }*/


    public static CompletableFuture<Void> processVoucherCode(String voucherCode,
                                                             Set<String> salesVoucherCodes,
                                                             Set<String> usageVoucherCodes,
                                                             Set<String> l98VoucherCodeSet,
                                                             Map<String, Boolean> wasCancelledThenSoldCache,
                                                             Map<String, Boolean> wasCancelledThenSoldThenUsedCache,
                                                             Set<String> dataToAdd,
                                                             Set<String> dataToDelete) {
        return CompletableFuture.runAsync(() -> {
            boolean hasSales = salesVoucherCodes.contains(voucherCode);
            boolean hasUsage = usageVoucherCodes.contains(voucherCode);
            boolean inL98 = l98VoucherCodeSet.contains(voucherCode);

            if (hasSales && hasUsage && !inL98) {
                // 有销售数据，也有使用数据，L98不存在，不需要补数据
                // 1.1如果是先取消，再销售，再使用的，需要补数据
                boolean wasCancelledThenSoldThenUsed = wasCancelledThenSoldThenUsedCache.getOrDefault(voucherCode, false);
                if (wasCancelledThenSoldThenUsed) {
                    dataToAdd.add(voucherCode);
                }
            } else if (hasSales && !hasUsage && inL98) {
                // 有销售数据，没有使用数据，L98存在，是需要删除的。
                // 2.2如果是先取消，再销售的，L98存在，不需要补数据
                boolean wasCancelledThenSold = wasCancelledThenSoldCache.getOrDefault(voucherCode, false);
                if (!wasCancelledThenSold) {
                    dataToDelete.add(voucherCode);
                }
            } else if (!hasSales && hasUsage && !inL98) {
                // 无销售数据，有使用数据的，L98不存在，需要补数据。
                dataToAdd.add(voucherCode);
            }
        }, forkJoinPool);
    }

    public static boolean wasCancelledThenSold(Map<String, List<TransactionData>> salesTransactionData, String voucherCode) {
        List<TransactionData> transactions = salesTransactionData.getOrDefault(voucherCode, Collections.emptyList());
        transactions = transactions.stream().filter(x->DateUtil.isIn(x.getTransactionDate(),start,end)).collect(Collectors.toList());
        transactions.sort(Comparator.comparing(TransactionData::getTransactionDate));
        boolean cancelled = false;

        for (TransactionData transaction : transactions) {
            if ("GIFT VOUCHER CANCEL SELL".equals(transaction.getTransactionType())) {
                cancelled = true;
            } else if ("GIFT VOUCHER SELL".equals(transaction.getTransactionType())) {
                if (cancelled) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean wasCancelledThenSoldThenUsed(Map<String, List<TransactionData>> salesTransactionData, Map<String, List<TransactionData>> usageTransactionData, String voucherCode) {
        List<TransactionData> transactions = new ArrayList<>(salesTransactionData.getOrDefault(voucherCode, Collections.emptyList()));
        transactions.addAll(usageTransactionData.getOrDefault(voucherCode, Collections.emptyList()));
        transactions = transactions.stream().filter(x->DateUtil.isIn(x.getTransactionDate(),start,end)).collect(Collectors.toList());
        transactions.sort(Comparator.comparing(TransactionData::getTransactionDate));

        boolean cancelled = false;
        boolean sold = false;

        for (TransactionData transaction : transactions) {
            if ("GIFT VOUCHER CANCEL SELL".equals(transaction.getTransactionType())) {
                cancelled = true;
            } else if ("GIFT VOUCHER SELL".equals(transaction.getTransactionType())) {
                if (cancelled) {
                    sold = true;
                }
            } else if ("GIFT VOUCHER REDEEM".equals(transaction.getTransactionType())) {
                if (sold) {
                    return true;
                }
            }
        }
        return false;
    }






    /*public static boolean wasCancelledThenSold(Map<String, List<TransactionData>> salesTransactionData, String voucherCode) {
        List<TransactionData> transactions = salesTransactionData.values().stream()
                .flatMap(List::stream)
                .filter(y -> y.getVoucherCode().equals(voucherCode))
                .sorted(Comparator.comparing(TransactionData::getTransactionDate))
                .collect(Collectors.toList());

        boolean cancelled = false;

        for (TransactionData transaction : transactions) {
            if ("GIFT VOUCHER CANCEL SELL".equals(transaction.getTransactionType())) {
                cancelled = true;
            } else if ("GIFT VOUCHER SELL".equals(transaction.getTransactionType())) {
                if (cancelled) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean wasCancelledThenSoldThenUsed(Map<String, List<TransactionData>> salesTransactionData, Map<String, List<TransactionData>> usageTransactionData, String voucherCode) {
        List<TransactionData> transactions = new ArrayList<>();

        transactions.addAll(salesTransactionData.values().stream()
                .flatMap(List::stream)
                .filter(y -> y.getVoucherCode().equals(voucherCode))
                .collect(Collectors.toList()));

        transactions.addAll(usageTransactionData.values().stream()
                .flatMap(List::stream)
                .filter(y -> y.getVoucherCode().equals(voucherCode))
                .collect(Collectors.toList()));

        transactions.sort(Comparator.comparing(TransactionData::getTransactionDate));

        boolean cancelled = false;
        boolean sold = false;

        for (TransactionData transaction : transactions) {
            if ("GIFT VOUCHER CANCEL SELL".equals(transaction.getTransactionType())) {
                cancelled = true;
            } else if ("GIFT VOUCHER SELL".equals(transaction.getTransactionType())) {
                if (cancelled) {
                    sold = true;
                }
            } else if ("1".equals(transaction.getTransactionType())) {
                if (sold) {
                    return true;
                }
            }
        }
        return false;
    }*/






    public static List<TransactionData> parseDbTransactionData(List<String> dbTransactionData) {
        List<TransactionData> transactionDataList = new ArrayList<>();
        for (String data : dbTransactionData) {
            String[] lines = data.split("\n");
            for (String line : lines) {
                TransactionData transactionData = new TransactionData();
                String[] parts = line.split(",");
                transactionData.setVoucherCode(parts[0]);
                transactionData.setTransactionDate(com.gtech.commons.utils.DateUtil.parseDate(parts[1], com.gtech.commons.utils.DateUtil.FORMAT_YYYYMMDDHHMISS));
                transactionData.setTransactionType(parts[2]);
                transactionDataList.add(transactionData);
            }
        }
        return transactionDataList;
    }



    public static void getResult(int numTasksSales, CompletionService<Map<String, List<TransactionData>>> completionService, Map<String, List<TransactionData>> salesResults) {
        for (int i = 0; i < numTasksSales; i++) {
            try {
                Future<Map<String, List<TransactionData>>> future = completionService.take();
                Map<String, List<TransactionData>> result = future.get();
                if (result != null) {
                    log.info("numTasks: "+ numTasksSales +"  result size: " + result.size());
                    salesResults.putAll(result);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public static Map<Integer, List<String>> groupByMod(List<String> vouchers, int mod) {
        return vouchers.stream()
                .collect(Collectors.groupingBy(voucher -> {
                    String numericPart = voucher.replaceAll("[a-zA-Z]", "");
                    return Integer.valueOf((int) (Long.valueOf(numericPart) % mod));
                }));
    }


    public static Map<String, List<TransactionData>> queryUsageTransactionData(List<String> voucherCodes, Integer index) {
        HashMap<String, List<TransactionData>> resultMap = new HashMap<>();

        for (String voucherCode : voucherCodes) {
            try (Connection conn = DbUtil.use().getConnection()) {
                List<Entity> entityList = SqlExecutor.query(conn, "select * from " + "gv_transaction_data_" + index + " where voucher_code = ?", new EntityListHandler(), voucherCode);
                List<TransactionData> voucherTransactionData = entityList.stream().map(x -> {
                    TransactionData transactionData = new TransactionData();
                    transactionData.setVoucherCode(x.getStr("voucher_code"));
                    transactionData.setTransactionDate(x.getDate("transaction_date"));
                    transactionData.setTransactionType(TransactionTypeEnum.getTypeDesc(x.getStr("transaction_type")));
                    return transactionData;
                }).collect(Collectors.toList());

                writerTransactionData(dbTransactionData, voucherTransactionData);

                resultMap.put(voucherCode, voucherTransactionData);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return resultMap;
    }
    public static Map<String, List<LiabilityScript.TransactionData>> querySalesTransactionData(List<String> voucherCodes, Integer index) {
        Map<String, List<LiabilityScript.TransactionData>> resultMap = new ConcurrentHashMap<>();
        List<List<String>> lists = splitList(voucherCodes, BATCH_SIZE);

        for (List<String> batch : lists) {


            try (Connection conn = DbUtil.use().getConnection()) {
                String placeholders = batch.stream().map(code -> "?").collect(Collectors.joining(","));
                String query = "select * from gv_transaction_data_" + index + " where voucher_code IN (" + placeholders + ")" +
                        " and transaction_type in ('10','20','1','11')";
                List<Entity> entityList = SqlExecutor.query(conn, query, new EntityListHandler(), batch.toArray());


                //List<Entity> entityList = SqlExecutor.query(conn, "select * from " + "gv_transaction_data_" + index + " where voucher_code = ? and transaction_type in ('10','20','1','11')", new EntityListHandler(), voucherCode);
                List<LiabilityScript.TransactionData> voucherTransactionData = entityList.stream().map(x -> {
                    LiabilityScript.TransactionData transactionData = new LiabilityScript.TransactionData();
                    transactionData.setVoucherCode(x.getStr("voucher_code"));
                    transactionData.setTransactionDate(x.getDate("transaction_date"));
                    transactionData.setTransactionType(TransactionTypeEnum.getTypeDesc(x.getStr("transaction_type")));
                    return transactionData;
                }).collect(Collectors.toList());

                writerTransactionData(dbTransactionData, voucherTransactionData);
                Map<String, List<LiabilityScript.TransactionData>> voucherGroupBy = voucherTransactionData.stream().collect(Collectors.groupingBy(x -> x.getVoucherCode()));
                voucherGroupBy.forEach(resultMap::put);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return resultMap;
    }

    public static void createDirectoryIfNotExists(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }
    public static Map<String, Integer> getColumnIndices(Sheet sheet) {
        Map<String, Integer> columnIndices = new HashMap<>();
        Row headerRow = sheet.getRow(0);
        for (Cell cell : headerRow) {
            columnIndices.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }
        return columnIndices;
    }


    public static void writerTransactionData(String outputFile, List<TransactionData> transactionData) {
        fileLock.lock();
        try {
            List<String> lines = transactionData.stream()
                    .map(td -> td.getVoucherCode() + "," + td.getTransactionDate() + "," + td.getTransactionType())
                    .collect(Collectors.toList());
            Files.write(Paths.get(outputFile), lines, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            fileLock.unlock();
        }
    }

    public static void readAndStoreFilteredDataUsingPOI(String folderPath, String outputFile, DateTime start, DateTime end) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".xlsx") || name.toLowerCase().endsWith(".xls"));
        Map<String, Integer> columnIndices = new HashMap<>();
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            if (files != null) {
                for (File file : files) {
                    System.gc();
                    System.out.println("Processing file: " + file.getAbsolutePath());
                    try (InputStream inputStream = new FileInputStream(file);
                         Workbook workbook = WorkbookFactory.create(inputStream)) {

                        Sheet sheet = workbook.getSheetAt(0);
                        columnIndices = getColumnIndices(sheet);
                        for (Row row : sheet) {
                            handleRow(row, writer, start, end,columnIndices);
                        }
                    } catch (Exception e) {
                        System.out.println("Error reading file: " + file.getAbsolutePath());
                        e.printStackTrace();
                    }
                }
            } else {
                System.out.println("No files found in folder: " + folderPath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void handleRow(Row row, BufferedWriter writer, DateTime start, DateTime end, Map<String, Integer> columnIndices) throws IOException {
        if (row.getRowNum() == 0) {
            System.out.println("Header row: " + row);
            return; // 忽略标题行
        }

        String voucherCode = getCellValue(row.getCell(columnIndices.get("Voucher Number")));
        String dateStr = getCellValue(row.getCell(columnIndices.get("Transaction Date")));
        String transactionType = getCellValue(row.getCell(columnIndices.get("Transaction Type")));
        String cpgName = getCellValue(row.getCell(columnIndices.get("Voucher Program Group")));

        //System.out.println("Row index: " + row.getRowNum() + ", Voucher Code: " + voucherCode + ", Date: " + dateStr + ", Transaction Type: " + transactionType);

        try {
            DateTime transactionDate = DateUtil.parse(dateStr);
            if (DateUtil.isIn(transactionDate, start, end)) {
                String line = voucherCode + "," + dateStr+ "," + transactionType + "," + cpgName;
                writer.write(line);
                writer.newLine();
            } else {
                //System.out.println("Date " + dateStr + " is not in the range.");
            }
        } catch (Exception e) {
            System.out.println("Error processing row: " + e.getMessage());
            e.printStackTrace();
        }
    }


    public static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    public static Set<TransactionData> readTransactionDataFromFile(String filePath) {
        Set<TransactionData> data = new HashSet<>();
        System.out.println("Reading data from file: " + filePath);
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {

                String[] fields = line.split(",");
                TransactionData transactionData = new TransactionData();
                transactionData.setVoucherCode(fields[0]);
                transactionData.setTransactionDate(DateUtil.parse(fields[1]));
                data.add(transactionData);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("Finished reading data from file: " + filePath + ", total records: " + data.size());
        return data;
    }

    public static List<String> getTxtFilesContentFromFolder(String folderPath) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".txt"));

        List<String> contents = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                System.out.println("Reading txt file: " + file.getAbsolutePath());
                try {
                    contents.add(new String(Files.readAllBytes(Paths.get(file.getAbsolutePath()))));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            System.out.println("No txt files found in folder: " + folderPath);
        }
        return contents;
    }


    public static List<TransactionData> parseL98Data(List<String> l98Data) {
        List<TransactionData> transactionDataList = new ArrayList<>();
        for (String data : l98Data) {
            String[] lines = data.split("\n");
            for (String line : lines) {
                TransactionData transactionData = new TransactionData();
                String[] fields = line.split(",");
                transactionData.setVoucherCode(fields[0]);
                transactionDataList.add(transactionData);
            }
        }
        return transactionDataList;
    }






    public static void writeDataToFile(String filePath, Set<String> data) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (String line : data) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Data
    public static class TransactionData{
       public Date transactionDate;
       public String voucherCode;
       public String transactionType;
       public String cpgName;
    }


}
