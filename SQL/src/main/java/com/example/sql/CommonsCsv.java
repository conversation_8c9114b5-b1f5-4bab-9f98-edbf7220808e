package com.example.sql;

import cn.hutool.core.io.resource.ClassPathResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;
@Slf4j
public class CommonsCsv {

    // 批量插入，单次读取数据量
    public static final int LIST_SIZE = 1000;


    public static final String CSV_FILE_PATH = "/Users/<USER>/IdeaProjects/gvcore/SQL/src/main/resources/csv/voucher/physical/8-Voucher Active Data for Paper Voucher QC Series-output.csv";

    /**
     * 按行读取csv文件并对csv行记录执行func操作
     *
     * @param csvFile csv文件
     * @param func    回调函数，接收List {@link CSVRecord} 对象，大小取决于 {@link CSVUtils#LIST_SIZE}
     * @throws IOException 文件IO异常
     */
    public static void readCSV(File csvFile, Function<List<CSVRecord>, ?> func) throws IOException {
        CSVFormat format = CSVFormat.Builder.create()
                .setHeader() // 读取header作为csv的key，否则CSVRecord.get(headerName)会报错
                .setSkipHeaderRecord(true) // 跳过第一行的列名，列名单独是文件的自行搜索CSVFormat构造
                .build();
        CSVParser parse = format.parse(new FileReader(csvFile));
        Iterator<CSVRecord> csvRecordIterator = parse.iterator();
        // 2023/12/08 更新：写demo时没注意，实际应当使用ConcurrentLinkedQueue同步队列保证线程安全
        List<CSVRecord> list = new ArrayList<>(LIST_SIZE);
        for (int i = 0; i < LIST_SIZE && csvRecordIterator.hasNext(); i++) {
            list.add(csvRecordIterator.next());
            if (i == LIST_SIZE - 1 && csvRecordIterator.hasNext()) {
                i = -1;
                func.apply(list); // 实际使用场景请使用线程池
                list = new ArrayList<>(LIST_SIZE);
            }
        }
        if (!list.isEmpty()) {
            func.apply(list); // 实际使用场景请使用线程池
        }
    }


    public boolean batchSave(List<CSVRecord> csvRecordList) {
        // 这里只演示读取字段，具体如何批量保存请参考多线程插入数据
        for (CSVRecord csvRecord : csvRecordList) {
            log.info(csvRecord.get("columnName")); // columnName: csv列名，如id
        }
        return true;
    }
    // 调用
    public void loadCSV() throws IOException {
        ClassPathResource csvResource = new ClassPathResource("CSV_FILE_PATH"); // CSV_FILE_PATH: csv文件路径
        readCSV(csvResource.getFile(), this::batchSave);
    }

    public static void importCsv(String path)throws IOException {


        //InputStream inputStream =new FileInputStream("D:/CSV/导入.csv");//指定导入文件

        String projectPath = System.getProperty("user.dir");
        InputStream inputStream = new FileInputStream(projectPath + "/SQL/src/main/resources/sqlFile/voucherBooklet/"+path.replace(".csv",".txt"));

        CSVFormat format = CSVFormat.Builder.create()
                .setHeader() // 读取header作为csv的key，否则CSVRecord.get(headerName)会报错
                .setSkipHeaderRecord(true) // 跳过第一行的列名，列名单独是文件的自行搜索CSVFormat构造
                .build();
        InputStreamReader inputStreamReader =new InputStreamReader(inputStream,"UTF-8");
        Reader reader = new BufferedReader(inputStreamReader);
        //指定csv的标题头
        CSVParser csvParser = CSVFormat.EXCEL.withHeader().parse(reader);

        //将每一行记录存入list中
        List<CSVRecord> list = csvParser.getRecords();

        //变量循环list
        for(int i=0;i<list.size();i++){
            System.out.println(list.get(i).get("name")+":"+list.get(i).get("sex")+":"+list.get(i).get("age")+":"+list.get(i).get("birthday"));
        }
    }


}
