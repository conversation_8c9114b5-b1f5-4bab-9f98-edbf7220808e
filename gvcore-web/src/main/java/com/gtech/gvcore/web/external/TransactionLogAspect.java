package com.gtech.gvcore.web.external;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvcoreUnknownException;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.response.transaction.TransactionResponse;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.external.GvPosService;
import com.gtech.gvcore.service.TransactionDataService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class TransactionLogAspect {


    @Lazy
    @Autowired
    private TransactionDataService transactionDataService;



    @Lazy
    @Autowired
    private GvPosService gvPosService;

    @Lazy
    @Autowired
    private VoucherMapper voucherMapper;


    //@Around("execution(* com.gtech.gvcore.web.external.GvPosController.transaction(..))")
    public Object  transactionLogAspect(ProceedingJoinPoint  joinPoint) throws Throwable {
        //此处执行方法,并且获取返回值
        ResponseEntity<TransactionResponse> response  = (ResponseEntity)joinPoint.proceed();
        log.info("TransactionResponse : {}", JSON.toJSONString(response));
        try {
            //transactionDataService.responseLog(response);
            Object[] args = joinPoint.getArgs();
            Object arg = args[0];
            TransactionRequest request = JSON.parseObject(JSON.toJSONString(arg), TransactionRequest.class);
            log.info("TransactionRequest : {}", JSON.toJSONString(request));
            String terminalId = "";
            String batchId = "";
            terminalId = getTerminalIdByAuthHeader((String) args[1]);
            batchId = getBatchIdByAuthHeader((String) args[1]);
            transactionDataService.requestLog(request, terminalId, response,batchId);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return response;
    }





    private String getTerminalIdByAuthHeader(String authHeader) {
        AuthorizePayload authorizePayload = getAuthorizePayload(authHeader);

        if (StringUtils.isBlank(authorizePayload.getTerminalId())) {
            throw new GvcoreUnknownException(
                    String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()),
                    GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
        }
        return authorizePayload.getTerminalId();
    }

    @SneakyThrows
    private String getBatchIdByAuthHeader(String authHeader) {
        AuthorizePayload authorizePayload = getAuthorizePayload(authHeader);

        if (StringUtils.isBlank(authorizePayload.getCurrentBatchNumber())) {

            throw new GvcoreUnknownException(
                    String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()),
                    GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
        }
        return authorizePayload.getCurrentBatchNumber();
    }

    private AuthorizePayload getAuthorizePayload(String authHeader) {
        if (StringUtils.isBlank(authHeader) || !authHeader.startsWith("Bearer ")) {
            throw new GvcoreUnknownException(
                    String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED.getResponseCode()),
                    GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED.getResponseMessage());
        }
        // 获取token
        String authToken = authHeader.substring(7);
        AuthorizePayload authorizePayload = gvPosService.getAuthorizePayloadByToken(authToken);
        return authorizePayload;
    }


}
