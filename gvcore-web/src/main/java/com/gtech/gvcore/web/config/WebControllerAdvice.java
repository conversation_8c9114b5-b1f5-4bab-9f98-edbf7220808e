package com.gtech.gvcore.web.config;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * controller 增强器
 *
 * @since 2018/4/8
 */
@ControllerAdvice
@Slf4j
public class WebControllerAdvice {

    /**
     * 全局异常捕捉处理
     *
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Result<Void> errorHandler(Exception ex) {
        Result<Void> result = new Result<>();
        result.setCode(ResultErrorCodeEnum.FAILED.code());
        String message = ex.getMessage();
        //前端需需要判断是否是未知异常
        if (message.contains("Duplicate entry")) {
            result.setMessage("GvcoreUnknownException:"+ex.getCause().getMessage());
        } else {
            result.setMessage("GvcoreUnknownException:"+message);
        }
        log.error(message, ex);
        return result;
    }


    /**
     * 拦截捕捉自定义异常
     */
    @ResponseBody
    @ExceptionHandler(value = GTechBaseException.class)
    public Result<Void> myErrorHandler(GTechBaseException ex) {
        Result<Void> result = new Result<>();
        result.setCode(ex.getCode());
        result.setMessage(ex.getMessage());
        log.error(ex.getMessage(), ex);
        return result;
    }


    /***
     * <p>
     * 服务端参数验证
     * </p>
     *
     * @date 2018/9/3 15:55
     * @params [ex]
     */
    @ResponseBody
    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public Result<T> argumentNotValidHandler(MethodArgumentNotValidException ex) {
        Result<T> errorResult = new Result<>();
        errorResult.setCode(ResultErrorCodeEnum.PARAMTER_ERROR.code());
        List<ObjectError> errors = ex.getBindingResult().getAllErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (ObjectError error : errors) {
            errorMsg.append(error.getDefaultMessage()).append(";");
        }
        errorResult.setMessage(errorMsg.substring(0, errorMsg.length() - 1));
        return errorResult;
    }

}