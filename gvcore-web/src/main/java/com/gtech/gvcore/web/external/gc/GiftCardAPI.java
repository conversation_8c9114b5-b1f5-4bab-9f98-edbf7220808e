package com.gtech.gvcore.web.external.gc;

import com.gtech.gvcore.common.annotation.NotSysLogger;
import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvcoreUnknownException;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.gcapi.*;
import com.gtech.gvcore.common.request.gcapi.group.GroupCardRedeemRequest;
import com.gtech.gvcore.common.response.gc.*;
import com.gtech.gvcore.external.GvPosService;
import com.gtech.gvcore.giftcard.application.api.GiftCardApiService;
import com.gtech.gvcore.common.annotation.RetryableApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Authorization;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.gtech.gvcore.common.exception.GvBusinessException;
import com.gtech.gvcore.web.handler.ApiResponseHandler;
import javax.validation.groups.Default;
import javax.validation.Valid;

/**
 * Gift Card API
 */
@RestController
@RequestMapping("/gv/external/gc")
@Api(tags = "Gift Card API", description = "Gift Card related APIs", authorizations = {@Authorization(value = "Bearer")})
public class GiftCardAPI {

    /**
     * Available APIs:
     * - BuyGiftCard: Purchase gift cards
     * - AddCard: Add gift card to wallet and activate
     * - Check Balance: Query gift card balance
     * - queryCardDetails: Query gift card details
     * - queryCardStatement: Query gift card transaction statement
     * - queryCustomerCardList: Query customer gift card list
     * - extendActivationPeriod: Extend activation period
     * - New Batch Close: Batch close operations
     * - DynamicBarcode: Generate dynamic barcode
     * - GcIssuance: Gift card issuance
     * - Redeem: Gift card redemption
     */

    @Autowired
    private GiftCardApiService giftCardApiService;

    @Autowired
    private GvPosService gvPosService;

    @Autowired
    private RedisTemplate redisTemplate;




    @NotSysLogger(disableLogger = false)
    @PostMapping("/activateGiftCard")
    @ApiOperation(value = "Add Gift Card",
        notes = "Add a gift card to wallet and activate it. The system will verify activation code, balance and card number information.",
        response = ActivateGiftCardResponse.class)
    public ResponseEntity<ActivateGiftCardResponse> activateGiftCard(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Activate Card Request", required = true) @RequestBody @Valid ActivateCardRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            String batchNumber = getBatchNumber(authHeader.getAuthToken());
            ActivateGiftCardResponse serviceResponse = giftCardApiService.activateCard(request, terminalId,batchNumber);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, ActivateGiftCardResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/checkbalance")
    @ApiOperation(value = "Balance Inquiry",
        notes = "Query the latest balance and validity of gift card. This is part of the redemption process.",
        response = CheckBalanceResponse.class)
    public ResponseEntity<CheckBalanceResponse> checkBalance(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Check Balance Request", required = true) @RequestBody @Valid CheckBalanceRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            String batchNumber = getBatchNumber(authHeader.getAuthToken());
            CheckBalanceResponse serviceResponse = giftCardApiService.checkBalance(request, terminalId);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, CheckBalanceResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/queryCardDetails")
    @ApiOperation(value = "Query Gift Card Details",
        notes = "Get the cardholder's name, email address, balance, expiration date and all information of gift card.",
        response = QueryCardDetailsResponse.class)
    public ResponseEntity<QueryCardDetailsResponse> queryCardDetails(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Query Card Details Request", required = true) @RequestBody @Valid QueryCardDetailsRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            QueryCardDetailsResponse serviceResponse = giftCardApiService.queryCardDetails(request, terminalId);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, QueryCardDetailsResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/queryCardStatement")
    @ApiOperation(value = "Query Gift Card Statement",
        notes = "Get detailed transaction records of gift card within specified date range.",
        response = QueryCardStatementResponse.class)
    public ResponseEntity<QueryCardStatementResponse> queryCardStatement(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Query Card Statement Request", required = true) @RequestBody @Valid QueryCardStatementRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            QueryCardStatementResponse serviceResponse = giftCardApiService.queryCardStatement(request, terminalId);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, QueryCardStatementResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/queryCustomerCardList")
    @ApiOperation(value = "Query Gift Card List",
        notes = "Get all gift cards and their latest status for a customer.",
        response = QueryCustomerCardListResponse.class)
    public ResponseEntity<QueryCustomerCardListResponse> queryCustomerCardList(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Query Customer Card List Request", required = true) @RequestBody @Valid QueryCustomerCardListRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            QueryCustomerCardListResponse serviceResponse = giftCardApiService.queryCustomerCardList(request, terminalId);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, QueryCustomerCardListResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/extendActivationPeriod")
    @ApiOperation(value = "Extend Activation Expiration Date",
        notes = "If the card is beyond activation expiration date but within grace period, users can use this feature to extend and activate the card.",
        response = ExtendActivationPeriodResponse.class)
    public ResponseEntity<ExtendActivationPeriodResponse> extendActivationPeriod(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Extend Activation Period Request", required = true) @RequestBody @Valid ExtendActivationPeriodRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            ExtendActivationPeriodResponse serviceResponse = giftCardApiService.extendActivationPeriod(request, terminalId, getBatchNumber(authHeader.getAuthToken()));
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, ExtendActivationPeriodResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/newEndSession")
    @ApiOperation(value = "New Batch Close",
        notes = "Batch close API used to close transactions in one batch. Mobile EDC and Android apps use this API to log out and close the batch.",
        response = EndSessionResponse.class)
    public ResponseEntity<EndSessionResponse> newEndSession(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "New Batch Close Request", required = true) @RequestBody @Valid NewEndSessionRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            EndSessionResponse serviceResponse = giftCardApiService.newEndSession(request, terminalId,transactionId,token);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, EndSessionResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/dynamicBarcode")
    @ApiOperation(value = "Dynamic Barcode",
        notes = "When user tries to redeem gift card, a secure interface is needed for POS acceptance. POS will use barcode as known parameter and convert to card number. Dynamic barcode will have configurable expiry time.",
        response = DynamicBarcodeResponse.class)
    public ResponseEntity<DynamicBarcodeResponse> dynamicBarcode(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Dynamic Barcode Request", required = true) @RequestBody @Valid DynamicBarcodedRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            DynamicBarcodeResponse serviceResponse = giftCardApiService.dynamicBarcode(request);
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, DynamicBarcodeResponse::new);
        }
    }
    @NotSysLogger(disableLogger = false)
    @PostMapping("/issuance")
    @ApiOperation(value = "GC Issuance",
        notes = "API to issued the Gift Card. The system will verify GCPG and transaction code information based on the request and generate the specified number of gift cards.",
        response = GcIssuanceResponse.class)
    public ResponseEntity<GcIssuanceResponse> gcIssuance(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @RequestHeader(value = "transactionId", required = true) @ApiParam(value = "Transaction ID", required = true) Integer transactionId,
        @RequestHeader(value = "clientTime", required = true) @ApiParam(value = "Client Time", required = true) String clientTime,
        @ApiParam(value = "Gift Card Issuance Request", required = true) @RequestBody @Valid GcIssuanceRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            GcIssuanceResponse serviceResponse = giftCardApiService.gcIssuance(request,terminalId,getBatchNumber(authHeader.getAuthToken()));
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, GcIssuanceResponse::new);
        }
    }

    @RetryableApi
    @NotSysLogger(disableLogger = false)
    @PostMapping("/redeem")
    @ApiOperation(value = "Redeem",
        notes = "API to Redeem the Gift Card. The system will verify gift card number and PIN information and perform the corresponding redemption operation.",
        response = CardRedeemResponse.class)
    public ResponseEntity<CardRedeemResponse> redeem(
        @RequestHeader(value = "Authorization", required = true) @ApiParam(value = "Bearer Token", required = true) String token,
        @ApiParam(value = "Gift Card Redeem Request", required = true) @RequestBody @Validated(value = {GroupCardRedeemRequest.class,Default.class}) CardRedeemRequest request) {
        try {
            AuthorizePayload authHeader = getAuthorizePayloadByAuthHeader(token);
            String terminalId = authHeader.getTerminalId();
            CardRedeemResponse serviceResponse = giftCardApiService.cardRedemption(request,terminalId, getBatchNumber(authHeader.getAuthToken()));
            return ApiResponseHandler.buildResponse(serviceResponse);
        } catch (GvBusinessException e) {
            return ApiResponseHandler.handleGvBusinessException(e, CardRedeemResponse::new);
        }
    }



    private AuthorizePayload getAuthorizePayloadByAuthHeader(String authHeader) {
        if (StringUtils.isBlank(authHeader) || !authHeader.startsWith("Bearer ")) {
            throw new GvBusinessException(
                    String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode()),
                    GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseMessage());
        }

        // Extract token
        String authToken = authHeader.substring(7);
        AuthorizePayload authorizePayload = null;
        try {
            authorizePayload = gvPosService.getAuthorizePayloadByToken(authToken);
        } catch (GvcoreUnknownException e) {
            if (Integer.parseInt(e.getCode()) == GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode()){
                throw new GvBusinessException(
                    GiftCardResponseCodesEnum.TOKEN_EXPIRED);
            }
            throw new GvBusinessException(
                    String.valueOf(e.getCode()),
                    e.getMessage());
        }
        authorizePayload.setAuthToken(authToken);

        if (StringUtils.isBlank(authorizePayload.getTerminalId())) {
            throw new GvBusinessException(
                    String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()),
                    GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
        }

        return authorizePayload;
    }


    private String getBatchNumber(String token){
        String batchNumber = (String) redisTemplate.opsForValue().get("GV:AUTHORIZE_BATCHID:" + token);
        return StringUtils.isBlank(batchNumber) ? "0" : batchNumber;
    }
}
