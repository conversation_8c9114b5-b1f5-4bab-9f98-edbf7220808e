package com.gtech.gvcore.web.external;

import com.gtech.gvcore.monitor.JvmObjectMonitor;
import com.gtech.gvcore.service.impl.SystemLoggerServiceImpl;
import com.gtech.gvcore.service.report.base.ReportTaskRegisterServiceImpl;
import com.gtech.gvcore.service.report.impl.support.sales.CancelSalesDataRefresh;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@RequestMapping(value = "/gv/external")
@Api(value = "Om API.", tags = {"Om API"})
public class OmController {


    @Autowired
    private CancelSalesDataRefresh cancelSalesDataRefresh;

    @Autowired
    private SystemLoggerServiceImpl systemLoggerService;

    @Autowired
    private ReportTaskRegisterServiceImpl reportTaskRegisterService;

    @PostMapping("initCancelSalesData")
    public void initCancelSalesData() {
        cancelSalesDataRefresh.init(null);
    }


    @GetMapping("/truncate")
    public void truncate() {
        systemLoggerService.truncate();
    }

    @GetMapping("/performTask")
    public void performTask() {
        reportTaskRegisterService.performTask();
    }

    /**
     * 每天凌晨2点执行一次垃圾回收并记录效果
     */

    @PostMapping("/gc")
    public void dailyGarbageCollection() {
        try {
            // 记录GC前的内存状态
            String beforeStats = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时GC === GC前内存状态: {}", beforeStats);

            // 执行GC
            System.gc();

            // 等待GC完成
            Thread.sleep(2000);

            // 记录GC后的内存状态
            String afterStats = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时GC === GC后内存状态: {}", afterStats);

            // 计算GC效果
            logGcEffect(beforeStats, afterStats);

        } catch (Exception e) {
            log.error("定时GC执行失败", e);
        }
    }


    /**
     * 分析并记录GC效果
     */
    private void logGcEffect(String beforeStats, String afterStats) {
        try {
            // 简单解析内存使用情况
            long beforeHeap = extractHeapUsage(beforeStats);
            long afterHeap = extractHeapUsage(afterStats);

            if (beforeHeap > 0 && afterHeap > 0) {
                long freedMemory = beforeHeap - afterHeap;
                double freedPercent = (double) freedMemory / beforeHeap * 100;

                if (freedMemory > 0) {
                    log.info("=== GC效果 === 释放内存: {}MB ({:.1f}%)", freedMemory, freedPercent);
                } else {
                    log.warn("=== GC效果 === 内存未减少，可能存在内存泄漏");
                }
            }
        } catch (Exception e) {
            log.debug("分析GC效果失败: {}", e.getMessage());
        }
    }


    /**
     * 从统计字符串中提取堆内存使用量
     */
    private long extractHeapUsage(String stats) {
        try {
            // 解析 "Heap:824/7282MB" 格式
            if (stats.contains("Heap:")) {
                String heapPart = stats.substring(stats.indexOf("Heap:") + 5);
                String usedPart = heapPart.substring(0, heapPart.indexOf("/"));
                return Long.parseLong(usedPart);
            }
        } catch (Exception e) {
            log.debug("解析堆内存使用量失败: {}", e.getMessage());
        }
        return 0;
    }


    //手动触发 oom
    @PostMapping("/oom")
    public void oom(@RequestParam OOMType type) {

        triggerOOM(type);

    }


    public enum OOMType {
        HEAP,           // 堆溢出 (Java heap space)
        METASPACE,      // 元空间溢出 (Metaspace)
        STACK,          // 栈溢出 (StackOverflowError)
        DIRECT_MEMORY,  // 直接内存溢出 (Direct buffer memory)
        NATIVE_THREAD,   // 无法创建新线程 (unable to create new native thread)
        ERROR
    }

    private static final int _1MB = 1024 * 1024; // 1MB 字节

    /**
     * 根据传入的 OOMType 触发对应的 OutOfMemoryError
     * @param type 要触发的 OOM 类型
     * @return 触发结果消息 (通常在 OOM 发生前返回，实际 OOM 会导致 JVM 终止)
     */
    public String triggerOOM(OOMType type) {
        System.out.println("Attempting to trigger " + type.name() + " OOM...");

        try {
            switch (type) {
                case HEAP:
                    triggerHeapOOM();
                    break;
                case METASPACE:
                    triggerMetaspaceOOM();
                    break;
                case STACK:
                    triggerStackOverflow();
                    break;
                case DIRECT_MEMORY:
                    triggerDirectMemoryOOM();
                    break;
                case NATIVE_THREAD:
                    // 为了不阻塞 HTTP 请求线程，我们将 Native Thread OOM 放在后台线程中触发。
                    // 这样 HTTP 请求可以立即返回，但 JVM 仍会尝试创建大量线程直到崩溃。
                    new Thread(this::triggerNativeThreadOOM, "OOM-NativeThread-Generator").start();
                    return "Attempting to trigger NATIVE_THREAD OOM in background. This might not crash immediately, but will likely terminate the JVM soon.";
                case ERROR:
                    throw new OutOfMemoryError();
                default:
                    return "Unknown OOM type: " + type;
            }
        } catch (Throwable e) { // 使用 Throwable 捕获所有可能的错误和异常 (OOM 是 Error 的子类)
            String message = "Successfully caught OOM/Error for type " + type.name() + ": " + e.getMessage();
            System.err.println(message);
            e.printStackTrace();
            return message;
        }
        // 通常，当真正的 OOM 发生时，JVM 会在返回此语句之前终止。
        return type.name() + " OOM scenario initiated. JVM expected to terminate.";
    }

    /**
     * 触发堆内存溢出 (Java heap space)
     * 通过不断创建大对象，直到堆空间耗尽。
     */
    private void triggerHeapOOM() {
        List<byte[]> list = new ArrayList<>(); // 使用 byte[] 确保占用实际内存
        while (true) {
            list.add(new byte[_1MB]); // 每次添加 1MB 字节数组
        }
    }

    /**
     * 触发元空间溢出 (Metaspace)
     * 通过不断创建新的 ClassLoader 并加载类来消耗元空间。
     */
    private void triggerMetaspaceOOM() throws Exception {
        List<ClassLoader> classLoaders = new ArrayList<>();
        int counter = 0;
        // 需要一个虚拟的 URL，即使不实际加载文件
        URL dummyUrl = new URL("file:///dummy");

        while (true) {
            // 每次循环创建一个新的 ClassLoader
            ClassLoader classLoader = new URLClassLoader(new URL[]{dummyUrl});
            classLoaders.add(classLoader); // 保持引用以防止被 GC

            // 使用匿名类来生成新的类定义
            // 每次创建匿名类都会产生一个唯一的类名并被加载到 Metaspace 中
            Class<?> anonClass = new Object() {}.getClass();

            // 调用一个方法以确保类被完全加载和初始化
            Method method = anonClass.getMethod("hashCode");
            method.invoke(new Object());

            counter++;
            if (counter % 1000 == 0) {
                System.out.println("Metaspace OOM: Loaded " + counter + " class definitions/loaders.");
            }
        }
    }

    /**
     * 触发栈溢出 (StackOverflowError)
     * 通过无限递归调用一个方法来耗尽线程栈空间。
     */
    private void triggerStackOverflow() {
        recursiveCall(0);
    }

    private void recursiveCall(int depth) {
        if (depth % 1000 == 0) {
            System.out.println("Stack OOM: Current recursion depth: " + depth);
        }
        recursiveCall(depth + 1); // 无限递归
    }

    /**
     * 触发直接内存溢出 (Direct buffer memory)
     * 通过不断分配直接内存 (Off-Heap) 来耗尽直接内存空间。
     */
    private void triggerDirectMemoryOOM() {
        List<ByteBuffer> buffers = new ArrayList<>();
        while (true) {
            // 分配直接内存，不受 Java 堆大小限制，但受 MaxDirectMemorySize 限制
            ByteBuffer buffer = ByteBuffer.allocateDirect(_1MB);
            buffers.add(buffer);
        }
    }

    /**
     * 触发无法创建新线程 (unable to create new native thread)
     * 通过不断创建并保持大量线程活跃，直到操作系统或 JVM 无法再创建新线程。
     * 注意：这个 OOM 很大程度上依赖于操作系统的线程限制。
     */
    private void triggerNativeThreadOOM() {
        ExecutorService executor = Executors.newCachedThreadPool(); // 按需创建新线程的线程池
        AtomicInteger threadCount = new AtomicInteger(0);

        try {
            while (true) {
                executor.execute(() -> {
                    int currentCount = threadCount.incrementAndGet();
                    if (currentCount % 100 == 0) {
                        System.out.println("Native Thread OOM: Created " + currentCount + " threads.");
                    }
                    try {
                        // 保持线程存活，消耗资源，阻止线程池回收
                        // 注意：实际应用中不应长时间阻塞线程池的线程
                        TimeUnit.HOURS.sleep(1);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt(); // 恢复中断状态
                        // ignore
                    }
                });
            }
        } catch (Throwable e) {
            System.err.println("Error creating thread: " + e.getMessage());
            e.printStackTrace();
        } finally {
            executor.shutdownNow(); // 尝试关闭所有线程
            System.out.println("Native Thread OOM: Total threads attempted: " + threadCount.get());
        }
    }


}




