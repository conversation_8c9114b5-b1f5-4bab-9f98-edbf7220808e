package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.selfactivation.ActivateVouchersRequest;
import com.gtech.gvcore.common.request.selfactivation.GetActivationInfoRequest;
import com.gtech.gvcore.common.request.selfactivation.RequestOtpRequest;
import com.gtech.gvcore.common.response.selfactivation.ActivateVouchersResponse;
import com.gtech.gvcore.common.response.selfactivation.GetActivationInfoResponse;
import com.gtech.gvcore.common.response.selfactivation.RequestOtpResponse;
import com.gtech.gvcore.service.SelfActivationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Self-activation controller for delayed activation functionality
 * Provides APIs for customers to self-activate their vouchers
 */
@Slf4j
@RestController
@RequestMapping(value = "/self-activation")
@Api(value = "Self Activation", tags = {"Self Activation Api"})
public class SelfActivationController {
    
    @Autowired
    private SelfActivationService selfActivationService;
    
    /**
     * Get activation page information by token
     * Used when the frontend page loads to display order information
     */
    @ApiOperation(value = "Get activation info", notes = "Get activation page information by token")
    @PostMapping(value = "/getInfo")
    public Result<GetActivationInfoResponse> getActivationInfo(@RequestBody @Validated GetActivationInfoRequest request) {
        log.info("Getting activation info for token: {}", request.getToken());
        return selfActivationService.getActivationInfo(request);
    }
    
    /**
     * Request OTP for activation
     * Sends an 8-digit OTP to the customer's email
     */
    @ApiOperation(value = "Request OTP", notes = "Request OTP to be sent to customer email")
    @PostMapping(value = "/requestOtp")
    public Result<RequestOtpResponse> requestOtp(@RequestBody @Validated RequestOtpRequest request) {
        log.info("Requesting OTP for token: {}", request.getToken());
        return selfActivationService.requestOtp(request);
    }
    
    /**
     * Activate vouchers with OTP verification
     * This is the core transaction operation that activates all vouchers for the order
     */
    @ApiOperation(value = "Activate vouchers", notes = "Activate vouchers with OTP verification")
    @PostMapping(value = "/activate")
    public Result<ActivateVouchersResponse> activateVouchers(@RequestBody @Validated ActivateVouchersRequest request) {
        log.info("Activating vouchers for token: {}", request.getToken());
        return selfActivationService.activateVouchers(request);
    }
}
