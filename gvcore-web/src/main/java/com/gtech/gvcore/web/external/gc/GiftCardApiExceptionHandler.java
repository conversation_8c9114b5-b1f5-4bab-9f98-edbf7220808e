package com.gtech.gvcore.web.external.gc;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvBusinessException;
import com.gtech.gvcore.common.response.gc.BaseApiResponse;
import com.gtech.gvcore.common.response.gc.ValidationErrorResponse;
import com.gtech.gvcore.web.handler.ApiResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;

/**
 * Gift Card API 专用异常处理器
 * 仅处理 /gv/external/gc 路径下的异常
 */
@ControllerAdvice(basePackageClasses = GiftCardAPI.class)
@Slf4j
@Order(1) // 优先级高于全局异常处理器
public class GiftCardApiExceptionHandler {

    /**
     * 处理缺失请求头异常
     * 当 @RequestHeader(required = true) 的参数缺失时触发
     */
    @ExceptionHandler(value = {MissingRequestHeaderException.class})
    public ResponseEntity<ValidationErrorResponse> handleMissingRequestHeader(MissingRequestHeaderException ex) {
        String headerName = ex.getHeaderName();
        log.warn("Gift Card API - Missing required request header: {}", headerName);
        
        // 根据缺失的请求头返回对应的Gift Card错误码
        GiftCardResponseCodesEnum errorCode = getGiftCardErrorByHeaderName(headerName);
        GvBusinessException exception = new GvBusinessException(errorCode);
        
        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }

    /**
     * 处理请求绑定异常
     */
    @ExceptionHandler(value = {ServletRequestBindingException.class})
    public ResponseEntity<ValidationErrorResponse> handleServletRequestBinding(ServletRequestBindingException ex) {
        log.warn("Gift Card API - Request binding exception: {}", ex.getMessage());
        
        // 检查是否是缺失请求头的异常
        if (ex instanceof MissingRequestHeaderException) {
            return handleMissingRequestHeader((MissingRequestHeaderException) ex);
        }
        
        // 其他请求绑定异常，返回通用验证失败错误
        GvBusinessException exception = new GvBusinessException(
            GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING);
        
        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }

    /**
     * 处理方法参数验证异常 (RequestBody @Valid 验证失败)
     */
    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public ResponseEntity<ValidationErrorResponse> handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
        log.warn("Gift Card API - Method argument validation failed");
        
        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        
        for (FieldError error : fieldErrors) {
            String fieldName = error.getField();
            // 根据字段名映射到具体的错误码
            GiftCardResponseCodesEnum errorCode = getGiftCardErrorByFieldName(fieldName, error.getDefaultMessage());
            
            if (errorCode != GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING) {
                GvBusinessException exception = new GvBusinessException(errorCode);
                return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
            }
        }
        
        // 如果没有找到特定的错误码，返回通用的字段缺失错误
        GvBusinessException exception = new GvBusinessException(
            GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING);
        
        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }

    /**
     * 处理约束验证异常
     */
    @ExceptionHandler(value = {ConstraintViolationException.class})
    public ResponseEntity<ValidationErrorResponse> handleConstraintViolation(ConstraintViolationException ex) {
        log.warn("Gift Card API - Constraint violation: {}", ex.getMessage());
        
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            // 尝试从约束违反信息中提取字段名和错误信息
            String propertyPath = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            
            // 获取最后一个属性名作为字段名
            String fieldName = propertyPath;
            if (propertyPath.contains(".")) {
                fieldName = propertyPath.substring(propertyPath.lastIndexOf('.') + 1);
            }
            
            GiftCardResponseCodesEnum errorCode = getGiftCardErrorByFieldName(fieldName, errorMessage);
            if (errorCode != GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING) {
                GvBusinessException exception = new GvBusinessException(errorCode);
                return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
            }
        }
        
        GvBusinessException exception = new GvBusinessException(
            GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING);
        
        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }

    /**
     * 处理绑定异常 (表单数据绑定失败)
     */
    @ExceptionHandler(value = {BindException.class})
    public ResponseEntity<ValidationErrorResponse> handleBindException(BindException ex) {
        log.warn("Gift Card API - Bind exception: {}", ex.getMessage());
        
        List<FieldError> fieldErrors = ex.getFieldErrors();
        for (FieldError error : fieldErrors) {
            String fieldName = error.getField();
            GiftCardResponseCodesEnum errorCode = getGiftCardErrorByFieldName(fieldName, error.getDefaultMessage());
            
            if (errorCode != GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING) {
                GvBusinessException exception = new GvBusinessException(errorCode);
                return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
            }
        }
        
        GvBusinessException exception = new GvBusinessException(
            GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING);
        
        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }


    /**
     * 处理日期格式化异常
     * Cannot deserialize value of type `java.util.Date` from String value
     * 主要用于处理 JSON 反序列化时的时间格式异常
     */
    @ExceptionHandler(value = {InvalidFormatException.class})
    public ResponseEntity<ValidationErrorResponse> handleInvalidFormatException(InvalidFormatException ex){
        log.warn("Gift Card API - Invalid format exception: {}", ex.getMessage());
        
        // 获取字段路径信息
        String pathReference = ex.getPathReference();
        String fieldName = extractFieldNameFromPath(pathReference);
        
        log.debug("Field path: {}, extracted field name: {}", pathReference, fieldName);
        
        // 检查是否是时间相关字段的格式异常
        GiftCardResponseCodesEnum errorCode = getTimeFormatErrorByFieldName(fieldName, ex);
        
        if (errorCode != GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING) {
            GvBusinessException exception = new GvBusinessException(errorCode);
            return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
        }
        
        // 如果不是特定的时间格式错误，使用通用的字段名错误码匹配
        errorCode = getGiftCardErrorByFieldName(fieldName, ex.getMessage());
        if (errorCode != GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING) {
            GvBusinessException exception = new GvBusinessException(errorCode);
            return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
        }
        
        // 默认返回格式错误
        GvBusinessException exception = new GvBusinessException(
            GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING);

        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }

    /**
     * 处理其他参数异常 (如类型转换异常)
     */
    @ExceptionHandler(value = {IllegalArgumentException.class})
    public ResponseEntity<ValidationErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("Gift Card API - Illegal argument exception: {}", ex.getMessage());
        
        // 检查异常消息中是否包含时间相关的错误信息
        String errorMessage = ex.getMessage().toLowerCase();
        
        if (errorMessage.contains("date") || errorMessage.contains("time")) {
            // 尝试从异常消息中提取字段信息
            if (errorMessage.contains("clienttime")) {
                GvBusinessException exception = new GvBusinessException(
                    GiftCardResponseCodesEnum.CLIENT_TIME_FORMAT_INVALID);
                return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
            }
            if (errorMessage.contains("invoicedate")) {
                GvBusinessException exception = new GvBusinessException(
                    GiftCardResponseCodesEnum.INVOICE_DATE_FORMAT_INVALID);
                return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
            }
        }
        
        // 默认处理
        GvBusinessException exception = new GvBusinessException(
            GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING);
        
        return ApiResponseHandler.handleGvBusinessException(exception, ValidationErrorResponse::new);
    }




    /**
     * 根据请求头名称获取对应的Gift Card错误码
     */
    private GiftCardResponseCodesEnum getGiftCardErrorByHeaderName(String headerName) {
        switch (headerName.toLowerCase()) {
            case "authorization":
                return GiftCardResponseCodesEnum.TOKEN_IS_NULL;
            case "transactionid":
                return GiftCardResponseCodesEnum.TRANSACTION_ID_IS_NULL;
            case "clienttime":
                return GiftCardResponseCodesEnum.CLIENT_TIME_IS_NULL;
            default:
                return GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING;
        }
    }

    /**
     * 根据字段名称获取对应的Gift Card错误码
     */
    private GiftCardResponseCodesEnum getGiftCardErrorByFieldName(String fieldName, String errorMessage) {
        if (fieldName == null) {
            return GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING;
        }
        //giftCards[0].itemNo 去除 [0] 保留 giftCards.itemNo
        fieldName = fieldName.replaceAll("\\[\\d+]", "");
        
        switch (fieldName.toLowerCase()) {
            case "transactioncode":
                return GiftCardResponseCodesEnum.TRANSACTION_CODE_IS_NULL;
            case "transactionid":
                return GiftCardResponseCodesEnum.TRANSACTION_ID_IS_NULL;
            case "inputtype":
                return GiftCardResponseCodesEnum.INPUT_TYPE_IS_NULL;
            case "numberofgiftcards":
                return GiftCardResponseCodesEnum.NUMBER_OF_GIFT_CARDS_IS_NULL;
            case "invoicenumber":
                return GiftCardResponseCodesEnum.INVOICE_NUMBER_IS_NULL;
            case "gcpg":
                return GiftCardResponseCodesEnum.GCPG_IS_NULL;
            case "giftcardnumber":
                return GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IS_NULL;
            case "giftcardpin":
                return GiftCardResponseCodesEnum.GIFT_CARD_PIN_IS_NULL;
            case "giftcards.itemno":
            case "itemno":
                return GiftCardResponseCodesEnum.GIFT_CARDS_ITEM_NO_IS_NULL;
            case "giftcardexpiry":
                return GiftCardResponseCodesEnum.GIFT_CARD_EXPIRY_IS_NULL;
            case "customer":
                return GiftCardResponseCodesEnum.CUSTOMER_IS_NULL;
            case "giftcards":
                return GiftCardResponseCodesEnum.GIFT_CARDS_IS_NULL;
            case "transactionamount":
                return GiftCardResponseCodesEnum.TRANSACTION_AMOUNT_IS_NULL;
            case "giftcards.redemptionamount":
                return GiftCardResponseCodesEnum.REDEMPTION_AMOUNT_IS_NULL;
            case "customerid":
            case "buyer.customerid":
            case "customer.customerid":
                return GiftCardResponseCodesEnum.CUSTOMER_ID_IS_NULL;
            case "buyer":
                return GiftCardResponseCodesEnum.BUYER_REQUIRED_FIELDS_MISSING;
            case "invoicedate":
                // 检查是否是日期格式错误
                if (errorMessage != null && errorMessage.contains("format")) {
                    return GiftCardResponseCodesEnum.INVOICE_DATE_FORMAT_INVALID;
                }
                return GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING;
            case "clienttime":
                // 检查是否是时间格式错误
                if (errorMessage != null && errorMessage.contains("format")) {
                    return GiftCardResponseCodesEnum.CLIENT_TIME_FORMAT_INVALID;
                }
                return GiftCardResponseCodesEnum.CLIENT_TIME_IS_NULL;
            default:
                return GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING;
        }
    }

    /**
     * 从路径引用中提取字段名
     * 例如：com.example.Request["invoiceDate"] -> invoiceDate
     */
    private String extractFieldNameFromPath(String pathReference) {
        if (pathReference == null || pathReference.isEmpty()) {
            return "";
        }
        
        // 处理类似 com.example.Request["fieldName"] 格式
        if (pathReference.contains("[\"") && pathReference.contains("\"]")) {
            int startIndex = pathReference.indexOf("[\"") + 2;
            int endIndex = pathReference.indexOf("\"]", startIndex);
            if (endIndex > startIndex) {
                return pathReference.substring(startIndex, endIndex);
            }
        }
        
        // 处理类似 obj.fieldName 格式
        if (pathReference.contains(".")) {
            return pathReference.substring(pathReference.lastIndexOf('.') + 1);
        }
        
        // 直接返回字段名
        return pathReference;
    }

    /**
     * 根据字段名和异常信息判断是否是时间格式错误
     */
    private GiftCardResponseCodesEnum getTimeFormatErrorByFieldName(String fieldName, InvalidFormatException ex) {
        if (fieldName == null || fieldName.isEmpty()) {
            return GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        String errorMessage = ex.getMessage().toLowerCase();
        
        // 检查是否是时间相关字段并且是格式异常
        boolean isDateFormatError = errorMessage.contains("cannot deserialize") && 
                                   (errorMessage.contains("date") || errorMessage.contains("time"));
        
        if (isDateFormatError) {
            switch (lowerFieldName) {
                case "invoicedate":
                    log.warn("Invoice date format error: {}", ex.getMessage());
                    return GiftCardResponseCodesEnum.INVOICE_DATE_FORMAT_INVALID;
                case "clienttime":
                    log.warn("Client time format error: {}", ex.getMessage());
                    return GiftCardResponseCodesEnum.CLIENT_TIME_FORMAT_INVALID;
                default:
                    // 其他可能的时间字段，根据字段名判断
                    if (lowerFieldName.contains("date")) {
                        return GiftCardResponseCodesEnum.INVOICE_DATE_FORMAT_INVALID;
                    } else if (lowerFieldName.contains("time")) {
                        return GiftCardResponseCodesEnum.CLIENT_TIME_FORMAT_INVALID;
                    }
                    break;
            }
        }
        
        return GiftCardResponseCodesEnum.REQUIRED_FIELDS_MISSING;
    }
}