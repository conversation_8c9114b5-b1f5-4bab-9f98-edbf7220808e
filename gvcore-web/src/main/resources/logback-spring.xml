<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <appender name="gvcorelog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}_%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

	<logger name="gvcore_error" level="INFO">
        <appender-ref ref="gvcorelog"/>
    </logger>

    <logger name="gvcore" level="INFO">
        <appender-ref ref="gvcorelog"/>
    </logger>

    <logger name="gvcore_ts" level="INFO">
        <appender-ref ref="gvcorelog"/>
    </logger>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="gvcorelog"/>
    </root>
</configuration>