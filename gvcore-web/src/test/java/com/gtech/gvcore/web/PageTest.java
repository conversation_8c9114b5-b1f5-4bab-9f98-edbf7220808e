package com.gtech.gvcore.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class PageTest {

	public static void main(String[] args) {
		String json = "[\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000268\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_cancel_sales/approve/:code?\",\r\n"
				+ "        name: \"CancelSales/bulk_cancel_sales/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  },\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000263\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_cancel_redeem/approve/:code?\",\r\n"
				+ "        name: \"CancelSales/bulk_cancel_redeem/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  },\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000262\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_activation/approve/:code?\",\r\n" + "        name: \"CancelSales/bulk_activation/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  },\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000267\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_redeem/approve/:code?\",\r\n" + "        name: \"CancelSales/bulk_redeem/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  },\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000265\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_reissue/approve/:code?\",\r\n" + "        name: \"CancelSales/bulk_reissue/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  },\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000269\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_deactivate/approve/:code?\",\r\n" + "        name: \"CancelSales/bulk_deactivate/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  },\r\n"
				+ "  {\r\n"
				+ "    menuCode: \"ME000264\",\r\n"
				+ "    childRoute: [\r\n"
				+ "      {\r\n"
				+ "        path: \"/order/issue/handling/bulk_reactivate/approve/:code?\",\r\n" + "        name: \"CancelSales/bulk_reactivate/approve\",\r\n"
				+ "      },\r\n"
				+ "    ],\r\n"
				+ "  }\r\n"
				+ "]";
		JSONArray jsonArray = JSON.parseArray(json);
		Integer pageCode = 151;
		for (Object object : jsonArray) {
			JSONObject jsonObj = (JSONObject) object;
			JSONArray list = jsonObj.getJSONArray("childRoute");
			for (Object o : list) {
				pageCode += 1;
				JSONObject b = (JSONObject) o;
				String sql = "INSERT INTO `idm_page`(`domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ('SYSTEM_DEFAULT', 'GV', "
						+ "'" + jsonObj.get("menuCode") + "', '0" + pageCode + "', 'RS0" + +pageCode + "', '" + b.get("name") + "', NULL, '" + b.get("path")
						+ "', 1, NULL, now(), NULL, now(), 0);";

				System.out.println(sql);
			}
		}
	}
}
