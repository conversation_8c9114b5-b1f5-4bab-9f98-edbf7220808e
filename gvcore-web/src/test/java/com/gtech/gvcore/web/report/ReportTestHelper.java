package com.gtech.gvcore.web.report;

import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.ReportFactory;

/**
 * @ClassName ReportTestHelper
 * @Description
 * <AUTHOR>
 * @Date 2023/3/3 14:54
 * @Version V1.0
 **/
public class ReportTestHelper {

    //report factory register
    public static void reportFactoryRegister(CustomTestReport<?, ?> customTestReport) {

        ReportFactory.register(customTestReport.exportTypeEnum(), (BusinessReport<ReportQueryParam, Object>) customTestReport);

    }

}
