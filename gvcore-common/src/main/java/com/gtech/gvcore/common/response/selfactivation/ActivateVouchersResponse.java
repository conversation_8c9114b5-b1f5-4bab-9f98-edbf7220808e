package com.gtech.gvcore.common.response.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response for activating vouchers
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ActivateVouchersResponse", description = "Response for voucher activation")
public class ActivateVouchersResponse {
    
    @ApiModelProperty(value = "Success flag", example = "true")
    private Boolean success;
    
    @ApiModelProperty(value = "Response message", example = "Vouchers activated successfully.")
    private String message;
}
