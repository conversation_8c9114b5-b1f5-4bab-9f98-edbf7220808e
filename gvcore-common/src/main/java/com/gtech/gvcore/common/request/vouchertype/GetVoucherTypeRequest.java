package com.gtech.gvcore.common.request.vouchertype;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetVoucherTypeRequest")
public class GetVoucherTypeRequest {


    @ApiModelProperty(value = "Dd value.", example = "1122333",required = true)
    @NotEmpty(message = "ddValue can not be empty")
    @Length(max = 100)
    //ddValue prefix
    private String ddValue;



}
