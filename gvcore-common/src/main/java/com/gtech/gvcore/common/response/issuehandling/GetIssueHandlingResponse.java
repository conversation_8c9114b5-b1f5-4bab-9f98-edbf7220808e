package com.gtech.gvcore.common.response.issuehandling;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月6日
 */
@Data
public class GetIssueHandlingResponse {
	
	private String issueHandlingCode;

    /**
     * Issuer code.
     */
    private String issuerCode;

    /**
     * issuer type: cancel_sales、cancel_redeem、bulk_active、...
     */
    private String issueType;

    /**
     * report file type：csv
     */
    private String uploadedFileType;

    /**
     * uploaded file name
     */
    private String uploadedFileName;

    /**
     * uploaded file url
     */
    private String uploadedFileUrl;

    /**
     * request remarks
     */
    private String remarks;

    /**
     * count of voucher
     */
    private Integer countVoucher;

    /**
     * process status, 0:created,1:processing,2:success,3:failed
     */
    private Integer processStatus;

    /**
     * count of failed
     */
    private Integer countFailed;

    /**
     * result
     */
    private String result;

    /**
     * create user email
     */
    private String createUserEmail;

    /**
     * status, 0:create, 1:approve, 2:execute,3:cancel
     */
    private Integer status;

    /**
     * create user
     */
    private String createUser;

    private String createUserName;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update user
     */
    private String updateUser;

    private String approveNotes;

    /**
     * update time
     */
    private Date updateTime;
    
    private List<ProofFileInfo> proofFileList;

}


