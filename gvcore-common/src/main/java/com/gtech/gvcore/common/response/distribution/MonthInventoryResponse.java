package com.gtech.gvcore.common.response.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName MonthInventoryResponse
 * @Description 月库存数据
 * <AUTHOR>
 * @Date 2022/7/6 11:00
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "MonthInventoryResponse")
public class MonthInventoryResponse {

    @ApiModelProperty(value = "月份", example = "2022-01")
    private String month;

    @ApiModelProperty(value = "数量", example = "100")
    private Integer count;

}
