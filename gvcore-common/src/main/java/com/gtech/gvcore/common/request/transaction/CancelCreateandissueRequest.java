package com.gtech.gvcore.common.request.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CancelCreateandissueRequest {

	@ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch. For e.g, after the first time init is done, when doing a balance enquiry for the first time, the value for this can be 1, for the second transaction, this should be 2 and so on. Basically, a unique incrementing sequence within the current batch")
	private Integer transactionId;

	@ApiModelProperty(value = "Card Number. ")
	private String cardNumber;

	@ApiModelProperty(value = "Original Amount. ")
	private Integer originalAmount;

	@ApiModelProperty(value = "Original Approval Code. ")
	private String originalApprovalCode;

	@ApiModelProperty(value = "Original Transaction Id. ")
	private Integer originalTransactionId;

	@ApiModelProperty(value = "Original Batch Number. ")
	private Long originalBatchNumber;

	@ApiModelProperty(value = "Date At Client. ")
	private Date dateAtClient;

	private String terminalId;

}

