package com.gtech.gvcore.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class ListUtil {

    private ListUtil(){}

    /**
     * 将list分割成指定大小的list
     * @param list 原list
     * @param groupSize list大小
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitList(List<T> list, int groupSize) {
        int length = list.size();
        // 计算可以分成多少组
        int num = (length + groupSize - 1) / groupSize;
        List<List<T>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = Math.min((i + 1) * groupSize, length);
            newList.add(list.subList(fromIndex, toIndex));
        }
        return newList;
    }

    public static <T> List<List<T>> splitList(Set<T> setList, int groupSize) {
        return splitList(new ArrayList<>(setList), groupSize);
    }

}