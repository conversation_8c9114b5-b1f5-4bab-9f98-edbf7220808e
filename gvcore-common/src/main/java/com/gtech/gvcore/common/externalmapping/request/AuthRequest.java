package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

@Data
public class AuthRequest {

    @ApiModelProperty(value = "Terminal ID. ", example = "SF00001", required = true, notes = "The terminal id associated with the POS. Typically this would be the macid or the unique machineid that we generate.")
    @Length(max = 50, message = "Terminal ID maximum length 50")
    @NotBlank(message = "Terminal ID not blank")
    private String posId;
    @ApiModelProperty(value = "User Name. ", example = "tony", required = true, notes = "The username to login to the Qwikcilver server")
    @Length(max = 50, message = "User Name maximum length 50")
    @NotBlank(message = "User Name not blank")
    private String userName;
    @ApiModelProperty(value = "Password. ", example = "123456", required = true, notes = "The password used to login to the Qwikcilver server")
    @Length(max = 50, message = "Password maximum length 50")
    @NotBlank(message = "Password not blank")
    private String password;
    @ApiModelProperty(value = "Forwarding Entity ID. ", example = "123", notes = "Additional credentials for using WebPOS type POS. Mandatory for WebPOS, IVR or SMS gateway.")
    private String environmentId;
    @ApiModelProperty(value = "Forwarding Entity Password. ", example = "123456", notes = "Additional credentials for using WebPOS type POS. Mandatory for WebPOS, IVR or SMS gateway.")
    private String environmentPassword;
    @ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "Timestamp of client application in YYYY-MM-DD HH:MM:SS format")
    private String clientTime;
    @ApiModelProperty(value = "Transaction ID. ", example = "123", required = true, notes = "The transaction id passed in the request.")
    @NotNull(message = "Transaction ID not blank")
    private Integer transactionId;


    public Map<String,Object> toMap(){

        Map<String, Object> map = new HashMap<>();
        map.put("terminalId", this.getPosId());
        map.put("userName", this.getUserName());
        map.put("password", this.getPassword());
        map.put("forwardingEntityId", this.getEnvironmentId());
        map.put("forwardingEntityPassword", this.getEnvironmentPassword());
        map.put("dateAtClient", this.getClientTime());
        map.put("transactionId", this.getTransactionId());

        return map;
    }

}
