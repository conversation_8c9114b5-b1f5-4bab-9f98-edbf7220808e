package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.transaction.SVRecentTransactionsResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SVRecentTransaction {

    @ApiModelProperty(value = "Card Number.", notes = "Card Number")
    private String voucherCode;
    @ApiModelProperty(value = "Transaction Date.",  notes = "The date of transaction in YYYY-MM-DD HH:MM:SS format")
    private String transactionDate;
    @ApiModelProperty(value = "Transaction Date At Server.",  notes = "The date of transaction at the Server in YYYY-MM-DD HH:MM:SS format")
    private String transactionDateAtServer;
    @ApiModelProperty(value = "Outlet Name.", notes = "The outletname/storename to which this POS is assigned to in the server")
    private String outletName;
    @ApiModelProperty(value = "Outlet Code.", notes = "The code assigned to the outlet")
    private String outletCode;
    @ApiModelProperty(value = "Invoice Number.", notes = "Optional Invoice Number")
    private String invoiceNumber;
    @ApiModelProperty(value = "Transaction Type.", notes = "The transaction type ID passed in the request.")
    private String transactionType;
    @ApiModelProperty(value = "Transaction Amount.", notes = "transacted amount")
    private BigDecimal transactionAmount;
    @ApiModelProperty(value = "Card Balance.", notes = "The balance on the card")
    private BigDecimal voucherBalance;
    @ApiModelProperty(value = "Loyalty.", notes = "Represents loyalty value that got added/redeemed in the particular transaction")
    private BigDecimal loyalty;
    @ApiModelProperty(value = "Notes.", notes = "Any Reference text to be captured along with this transaction")
    private String notes;
    /**
     * 	'batchid',
     *  'approvalcode',
     *  'referencenumber',
     *  'businessreferencenumber',
     *  'currencycode',
     *  'transactionstatus',
     *  'transactiondatetime',
     *  'xactionreference',
     *  'originalxactionreference',
     *  'username'
     */

    private String batchId;
    private String approvalCode;
    private String referenceNumber;
    private String businessReferenceNumber;
    private String currencyCode;
    private String transactionStatus;
    private String transactionDateTime;
    private String xactionReference;
    private String originalXactionReference;
    private String userName;


    public SVRecentTransaction setSVRecentTransaction(SVRecentTransactionsResponse response){

        if(response == null){
            return null;
        }
        this.setVoucherCode(response.getVoucherNumber());
        this.setTransactionDate(response.getTransactionDate());
        this.setTransactionDateAtServer(response.getTransactionDateAtServer());
        this.setOutletName(response.getStoreName());
        this.setOutletCode(response.getStoreCode());
        this.setInvoiceNumber(response.getInvoiceNumber());
        this.setTransactionType(response.getTransactionType());
        this.setTransactionAmount(response.getTransactionAmount());
        this.setVoucherBalance(response.getVoucherBalance());
        this.setLoyalty(response.getLoyalty());
        this.setNotes(response.getNotes());
//        this.setBatchId(response.getBatchId());
//        this.setApprovalCode(response.getApprovalCode());
//        this.setReferenceNumber(response.getReferenceNumber());
//        this.setBusinessReferenceNumber(response.getBusinessReferenceNumber());
//        this.setCurrencyCode(response.getCurrencyCode());
//        this.setTransactionStatus(response.getTransactionStatus());
//        this.setTransactionDateTime(response.getTransactionDateTime());
//        this.setXactionReference(response.getXactionReference());
//        this.setOriginalXactionReference(response.getOriginalXactionReference());
//        this.setUserName(response.getUserName());


        return this;
    }



}
