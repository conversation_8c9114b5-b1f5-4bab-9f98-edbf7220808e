package com.gtech.gvcore.common.enums;

/**
 * @ClassName DistributionTypeEnum
 * @Description 分发类型枚举
 * <AUTHOR>
 * @Date 2022/8/9 14:09
 * @Version V1.0
 **/
public enum DistributionTypeEnum implements IEnum<String>{
    INDIVIDUAL("Individual","单个"),
    BULK("Bulk","单个"),
    ;

    private final String code;
    private final String desc;

    DistributionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(final String code) {
        return this.code().equals(code);
    }

}
