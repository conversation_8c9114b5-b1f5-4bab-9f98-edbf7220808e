package com.gtech.gvcore.common.request.reportmanagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/12 11:21
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "DelReportManagementRequest", description = "Delete a Report Management record")
public class DelReportManagementRequest implements Serializable {

    private static final long serialVersionUID = -4642658857402940533L;
    @ApiModelProperty(value = "delUser")
    private String delUser;
    @ApiModelProperty(value = "reportManagementCode", required = true, example = "RM102203081916000004")
    @NotBlank(message = "reportManagementCode cannot be empty")
    private String reportManagementCode;
}
