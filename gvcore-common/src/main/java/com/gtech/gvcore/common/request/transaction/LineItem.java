package com.gtech.gvcore.common.request.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Data
@Accessors(chain = true)
public class LineItem {
	@ApiModelProperty(value = "Line Item No. ", required = true, example = "123456", notes = "Represents line number from the line number list. LineItemNo will be unique within the order. Sequential numbers recommended")
	@Length(max = 10, message = "Line Item No maximum length 10")
	private String lineItemNo;
	@ApiModelProperty(value = "Input Type. ", required = true, example = "1", notes = "1 - INDIVIDUAL 2 - RANGE 3 - EGV (Only for bulk activate)")
	private String inputType;
	//"amountPerCard" : "50,000"    ETP的销售
	@ApiModelProperty(value = "Amount Per Card. ", example = "0", notes = "Mandatory in case of Bulk Redeem. For Activation amount will be of fixed denomination and will be derived from program setup.")
	private String amountPerCard;
	@ApiModelProperty(value = "Number Of Cards. ", required = true, example = "1", notes = "Quantity(if input type is Range and EGV)")
	private Integer numberOfCards;
	@ApiModelProperty(value = "Card Expiry. ", example = "2022-02-02", notes = "YYYY-MM-DD")
	private String cardExpiry;
	@ApiModelProperty(value = "Start CardInfo. ", example = "1", notes = "See CardInfo below for details(if input type is Range)")
	private CardInfo startCardInfo;
	@ApiModelProperty(value = "End CardInfo. ", example = "1", notes = "See CardInfo below for details(if input type is Range)")
	private CardInfo endCardInfo;
	@ApiModelProperty(value = "CardInfo. ", example = "1", notes = "See CardInfo below for details( if input type is Individual or EGV)")
	private CardInfo cardInfo;

}