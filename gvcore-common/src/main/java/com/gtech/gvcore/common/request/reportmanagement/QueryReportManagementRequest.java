package com.gtech.gvcore.common.request.reportmanagement;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/12 13:25
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "QueryReportManagementRequest", description = "Query the list of Report Management record based on criteria")
public class QueryReportManagementRequest extends PageParam implements Serializable {

    private static final long serialVersionUID = 6923984848635028353L;
    @ApiModelProperty(value = "merchantCode", example = "ME102203021435000523")
    private String merchantCode;
    @ApiModelProperty(value = "reportType")
    private String reportType;
    @ApiModelProperty(value = "roleCode", example = "admin")
    private String roleCode;

}
