package com.gtech.gvcore.common.request.reportexport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ReportExportRequest")
public class ReportExportRequest implements Serializable {
    private static final long serialVersionUID = 7095970857096418826L;
    @ApiModelProperty(value = "reportType", required = true)
    @NotEmpty(message = "ReportType can not be empty")
    private String reportType;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser")
    private String createUser;

}
