package com.gtech.gvcore.common.request.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateCompanyRequest")
public class UpdateCompanyRequest implements Serializable {

    private static final long serialVersionUID = -1068141633231594162L;

    @ApiModelProperty( value = "Company code.", example = "1345566",required = true)
    @NotEmpty(message = "companyCode can not be empty")
    @Length(max = 100)
    private String companyCode;

    @ApiModelProperty( value="Company name.", example="PT AGUNG MANDIRI LESTARI")
    @Length(max = 100)
    private String companyName;

    @ApiModelProperty( value="Sbu.", example="Digimap")
    @Length(max = 50)
    private String sbu;

    @ApiModelProperty( value="Status.", example="0")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 50)
    private String updateUser;


}
