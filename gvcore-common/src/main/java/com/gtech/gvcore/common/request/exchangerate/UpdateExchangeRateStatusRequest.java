package com.gtech.gvcore.common.request.exchangerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Gao.Yuhua
 * @date 2022-02-25
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateExchangeRateRequest")
public class UpdateExchangeRateStatusRequest {


    /**
     * exchangeRateCode
     */
    @NotEmpty(message = "exchangeRateCode can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "exchangeRateCode", example = "currencyCode", required = true)
    private String exchangeRateCode;


    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status can not be null")
    @Range(min = 0, max = 1, message = "status value range [0,1]")
    private Integer status;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
