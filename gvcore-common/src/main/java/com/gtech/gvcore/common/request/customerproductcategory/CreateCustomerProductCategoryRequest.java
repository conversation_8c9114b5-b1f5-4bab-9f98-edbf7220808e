package com.gtech.gvcore.common.request.customerproductcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateCustomerProductCategoryRequest")
public class CreateCustomerProductCategoryRequest {


    @ApiModelProperty(value = "Customer code.", example = "12312312",required = true)
    @NotEmpty(message = "customerCode can not be empty")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Product category code.", example = "11221312",required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    @Length(max = 100)
    private String productCategoryCode;

    //批量添加
    @ApiModelProperty(value = "Product category code.", example = "11221312",hidden = true)
    private List<String> productCategoryCodeList;

    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;







}
