package com.gtech.gvcore.common.request.transaction;

import java.math.BigDecimal;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreateandissueRequest {

	@ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch. For e.g, after the first time init is done, when doing a balance enquiry for the first time, the value for this can be 1, for the second transaction, this should be 2 and so on. Basically, a unique incrementing sequence within the current batch")
	private Integer transactionId;
	@ApiModelProperty(value = "Card Program Group Name. ", example = "0", notes = "Product Name as in QC system (Name of the CPG into which the card falls)")
	@Length(max = 50, message = "Card Program Group Name maximum length 50")
	private String cardProgramGroupName;
	@ApiModelProperty(value = "Amount. ", example = "0", required = true, notes = "Contains the balance on the card, with last two or three digits as implied decimal")
	private BigDecimal amount;
	@ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "The datetime at the client machine in YYYY-MM-DD HH:MM:SS")
	private String dateAtClient;
	@ApiModelProperty(value = "Bill Amount. ", example = "0", notes = "Total Bill amount for a given transaction.")
	private BigDecimal billAmount;
	@ApiModelProperty(value = "Invoice Number. ", example = "0", notes = "The invoice number.")
	@Length(max = 50, message = "Invoice Number maximum length 50")
	private String invoiceNumber;
	@ApiModelProperty(value = "External Card Number. ", example = "0", notes = "Another identity of the card")
	@Length(max = 50, message = "External Card Number maximum length 50")
	private String externalCardNumber;
	@ApiModelProperty(value = "Expiry. ", example = "01012022", notes = "Card Expiry Date  (DDMMYYYY)")
	private String expiry;
	@ApiModelProperty(value = "Notes. ", example = "0", notes = "Any Reference text to be captured along with this transaction")
	@Length(max = 512, message = "Notes maximum length 512")
	private String notes;
	@ApiModelProperty(value = "Terminal ID. ", example = "SF00001", required = true, notes = "The terminal id associated with the POS. Typically this would be the macid or the unique machineid that we generate.")
	@Length(max = 50, message = "Terminal ID maximum length 50")
	private String terminalId;
	@ApiModelProperty(value = "Idempotency Key. ", example = "SF00001", required = true, notes = "Every Request can have a unique Idempotency key, which is a reference number for the request. Say a request has reached server, but the response failed to reach the client. Using the idempotency key, client can resend the request, and the original response will be resent by server The following special characters are not allowed ^ < > ' \\ \" / ; ` % & SPACE , @ * ! ? % +")
	@Length(max = 255, message = "Idempotency Key maximum length 255")
	private String idempotencyKey;
	@ApiModelProperty(value = "Customer. ", notes = "Customer object. Refer Section-Data Structures")
	private CustomerInfo customer;

	private String cardNumber;

}
