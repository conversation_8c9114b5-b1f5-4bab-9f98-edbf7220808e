package com.gtech.gvcore.common.request.businesslog;

import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/11 17:08
 */
@Data
public class CreateBusinessLogRequest {



    private Integer status;

    private Integer success;

    private Integer failed;

    private String contentCode;

    private String content;

    private String createUser;

    private Date createTime;


    private List<CreateBusinessLogDetailRequest> businessLogDetailList;



}
