package com.gtech.gvcore.common.request.cpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/9 14:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCpgVoucherInventory")
public class QueryCpgVoucherInventory {

    @ApiModelProperty(value = "cpgCode",required = true)
    @NotNull(message = "cpgCode can not be null")
    private String cpgCode;

    @ApiModelProperty(value = "issuerCode",required = true)
    @NotNull(message = "issuerCode can not be null")
    private String issuerCode;


    @ApiModelProperty(value = "cpgCode",required = true)
    private List<String> cpgCodes;

    //是否只用count 0-count 1-query
    private String type;





}
