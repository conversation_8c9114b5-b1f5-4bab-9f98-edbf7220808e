package com.gtech.gvcore.common.enums;

/**
 **/
public enum GvPosCardCreationTypeEnum {

	VIRTUAL("VIRTUAL", "VCE", "Virtual"), PHYSICAL("PHYSICAL", "VCR", "Physical");


	private final String code;
	private final String voucherCardCreationTypeCode;

	private final String desc;

	GvPosCardCreationTypeEnum(String code, String voucherCardCreationTypeCode, String desc) {
		this.code = code;
		this.voucherCardCreationTypeCode = voucherCardCreationTypeCode;
		this.desc = desc;
	}

	public String getCode() {
		return code;
	}

	public String getVoucherCardCreationTypeCode() {
		return voucherCardCreationTypeCode;
	}

	public String getDesc() {
		return desc;
	}

	public static String getCodeByVoucherCardCreationTypeCode(String voucherCardCreationTypeCode) {
		for (GvPosCardCreationTypeEnum value : GvPosCardCreationTypeEnum.values()) {
			if (value.getVoucherCardCreationTypeCode().equals(voucherCardCreationTypeCode)) {
				return value.getCode();
			}
		}
		return null;
	}

	public static String getDescByVoucherCardCreationTypeCode(String voucherCardCreationTypeCode) {
		for (GvPosCardCreationTypeEnum value : GvPosCardCreationTypeEnum.values()) {
			if (value.getVoucherCardCreationTypeCode().equals(voucherCardCreationTypeCode)) {
				return value.getDesc();
			}
		}
		return null;
	}

}
