package com.gtech.gvcore.common.request.outlet;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateOutletRequest")
public class CreateOutletRequest {


    @ApiModelProperty(value = "Outlet name.", example = "MAP", required = true)
    @NotEmpty(message = "outletName can not be empty")
    @Length(max = 100)
    private String outletName;

    @ApiModelProperty(value = "Merchant code.", example = "1231254125", required = true)
    @NotEmpty(message = "merchantCode can not be empty")
    @Length(max = 100)
    private String merchantCode;

    @ApiModelProperty(value = "Issuer code.", example = "1231254125")
    private String issuerCode;


    @ApiModelProperty(value = "Sbu",example = "independet")
    private String sbu;

    @ApiModelProperty(value = "Cpg code.", example = "112323", required = true)
    @NotNull(message = "cpgCode can not be empty")
    private List<String> cpgCode;

    @ApiModelProperty(value = "Business outlet code.", example = "1234124", required = true)
    @NotEmpty(message = "businessOutletCode can not be empty")
    @Length(max = 100)
    private String businessOutletCode;

    @ApiModelProperty(value = "Outlet type.", example = "independet", required = true)
    @NotEmpty(message = "outletType can not be empty")
    @Length(max = 100)
    private String outletType;

    @ApiModelProperty(value = "State code.", example = "112233", required = true)
    @NotEmpty(message = "stateCode can not be empty")
    @Length(max = 100)
    private String stateCode;

    @ApiModelProperty(value = "City code.", example = "223344", required = true)
    @NotEmpty(message = "cityCode can not be empty")
    @Length(max = 100)
    private String cityCode;

    @ApiModelProperty(value = "District code.", example = "551122", required = true)
    @NotEmpty(message = "districtCode can not be empty")
    @Length(max = 100)
    private String districtCode;

    @ApiModelProperty(value = "Address1.", example = "2nd Floor NO.23", required = true)
    @NotEmpty(message = "address1 can not be empty")
    @Length(max = 512)
    private String address1;

    @ApiModelProperty(value = "Address2.", example = "2nd Floor NO.23")
    //@NotEmpty(message = "address2 can not be empty")
    @Length(max = 512)
    private String address2;

    @ApiModelProperty(value = "Pin code.", example = "0")
    @Length(max = 100)
    private String pinCode;

    @ApiModelProperty(value = "First name.", example = "name1")
    //@NotEmpty(message = "firstName can not be empty")
    @Length(max = 100)
    private String firstName;

    @ApiModelProperty(value = "Last name.", example = "name2")
    //@NotEmpty(message = "lastName can not be empty")
    @Length(max = 100)
    private String lastName;

    @ApiModelProperty(value = "Email.", example = "<EMAIL>", required = true)
    @NotEmpty(message = "email can not be empty")
    private String email;

    @ApiModelProperty(value = "Phone.", example = "1234125412", required = true)
    @NotEmpty(message = "phone can not be empty")
    @Length(max = 100)
    private String phone;

    @ApiModelProperty(value = "Mobile.", example = "1231245215")
    //@NotEmpty(message = "mobile can not be empty")
    @Length(max = 100)
    private String mobile;

    @ApiModelProperty(value = "Alertnate email.", example = "<EMAIL>")
    private String alertnateEmail;

    @ApiModelProperty(value = "Alertnate phone.", example = "45765765765")
    @Length(max = 100)
    private String alertnatePhone;

    @ApiModelProperty(value = "Descriptive.", example = "text")
    @Length(max = 100)
    private String descriptive;

    @ApiModelProperty(value = "Status.", example = "0")
    private Integer status;

    @ApiModelProperty(value = "Create user.", example = "user1", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;

    @ApiModelProperty(value = "Parent outlet.", example = "user1")
    @Length(max = 100)
    private String parentOutlet;

    public void validation() {


        if (StringUtil.isNotEmpty(phone)) {
            CheckUtils.isTrue(CheckUtils.isPhone(phone), ErrorCodes.PARAM_FORMAT_ERROR, "phone");
        }
        if (StringUtil.isNotEmpty(alertnatePhone)) {
            CheckUtils.isTrue(CheckUtils.isPhone(alertnatePhone), ErrorCodes.PARAM_FORMAT_ERROR, "alertnatePhone");
        }
        if (StringUtil.isEmpty(firstName)){
            CheckUtils.isNotBlank(lastName, ErrorCodes.PARAM_FORMAT_ERROR, "One Of LastName And FirstName Cannot Be Empty");
        }
        if (StringUtil.isEmpty(lastName)){
            CheckUtils.isNotBlank(firstName, ErrorCodes.PARAM_FORMAT_ERROR, "One Of LastName And FirstName Cannot Be Empty");
        }

    }


}
