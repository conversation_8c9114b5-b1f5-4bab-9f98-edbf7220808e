package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.transaction.ActivateonlyResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ActivityOnlyResponse {

    @ApiModelProperty(value = "Response Code.", notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
    private Integer responseCode;
    @ApiModelProperty(value = "Response Message.", notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
    private String responseMessage;
    @ApiModelProperty(value = "Transaction Id.", notes = "This value has to be incremented with every transaction within the batch.")
    private Integer transactionId;
    //    @ApiModelProperty(value = "Track Data.", notes = "The track-2 track data for the card if read from the MSR.")
//    private String trackData;
    @ApiModelProperty(value = "Card Number.", notes = "Card Number")
    private String voucherNumber;
    //    @JsonProperty("CardPIN")
//    @ApiModelProperty(value = "Card Pin.", notes = "PIN associated with the card. Entered by the cardholder/Operator(optional)")
//    private String cardPin;
//    @ApiModelProperty(value = "Invoice Number.", notes = "Optional Invoice Number")
//    private String invoiceNumber;
    @ApiModelProperty(value = "Amount.", notes = "Contains the balance on the card, with last two or three digits as implied decimal")
    private BigDecimal amount;
    //    @ApiModelProperty(value = "Bill Amount.", notes = "Total Bill amount for a given transaction.")
//    private BigDecimal billAmount;
//    @ApiModelProperty(value = "Original Invoice Number.", notes = "Original transaction’s invoiceNumber, available from request param list")
//    private String originalInvoiceNumber;
//    @ApiModelProperty(value = "Original Transaction Id.", notes = "Original transaction’s transactionID, available from original request parameter")
//    private Integer originalTransactionId;
//    @ApiModelProperty(value = "Original Batch Number.", notes = "Original Transaction’s batchNo, available from original request parameter")
    private Integer originalBatchNumber;
    //    @ApiModelProperty(value = "Original Approval Code.", notes = "The approval code of the original transaction")
//    private String originalApprovalCode;
//    @ApiModelProperty(value = "Original Amount.", notes = "The amount of the original transaction")
//    private BigDecimal originalAmount;
    @ApiModelProperty(value = "Approval Code.", notes = "Approval Code for successful transactions")
    private String approvalCode;
    //    @ApiModelProperty(value = "Add-on Card Number.", notes = "Add-on Card Number")
//    private String addonCardNumber;
//    @ApiModelProperty(value = "Add-on Card Track Data.", notes = "The track-2 track data for the add-on card if read from the MSR.")
//    private String addonCardTrackData;
//    @ApiModelProperty(value = "Transfer Card Number.", notes = "Transfer Card Number, if Implicit Transfer is enabled on the server. Works for now using WebPOS POSTypeID only.")
//    private String transferCardNumber;
//    @ApiModelProperty(value = "Merchant Name.", notes = "The name of the merchant to which this  POS belongs to")
//    private String merchantName;
//    @ApiModelProperty(value = "Adjustment Amount.", notes = "Amount Adjusted from Transaction Amount such as Service / Processing Fees fee or top-up value added to the card")
    private BigDecimal adjustmentAmount;
    @ApiModelProperty(value = "Card Expiry.", notes = "card expiry")
    private String voucherExpiry;
    //    @ApiModelProperty(value = "Original Card Number.", notes = "OriginalCardNumber on which the new card would be reissued.")
//    private String originalCardNumber;
//    @ApiModelProperty(value = "Original Card Pin.", notes = "OriginalCardPin on which the OriginalCardNumber would be validated.")
//    private String originalCardPin;
//    @ApiModelProperty(value = "Card Program ID.", notes = "Card program id (Gift-1 , Loyalty-2)")
//    private Integer cardProgramID;
//    @ApiModelProperty(value = "Corporate Name.", notes = "Name of the corporate associated with the cardholder")
//    private String corporateName;
    @ApiModelProperty(value = "Notes.", notes = "Any Reference text to be captured along with this transaction.")
    private String notes;
    //    @ApiModelProperty(value = "Settlement Date.", notes = "Batch Close date, at which settlement happens (DDMMYYYY)")
    private String settlementDate;
    //    @ApiModelProperty(value = "Expiry.", notes = "The expiry date on the card DDMMYYYY")
//    private String expiry;
//    @ApiModelProperty(value = "Purchase Order Number.", notes = "Purchase Order Number. Related to Purchase Order Fulfilment based on Activate transaction.")
//    private String purchaseOrderNumber;
//    @ApiModelProperty(value = "Purchase Order Value.", notes = "Purchase Order Value. Related to Purchase Order Fulfilment based on Activate transaction.")
    private BigDecimal purchaseOrderValue;
    //    @ApiModelProperty(value = "Discount Percentage.", notes = "Discount Percentage. Related to Purchase Order Fulfilment based on Activate transaction")
    private BigDecimal discountPercentage;
    //    @ApiModelProperty(value = "Discount Amount.", notes = "Discount Amount. Related to Purchase Order Fulfilment based on Activate transaction")
    private BigDecimal discountAmount;
    //    @ApiModelProperty(value = "Payment Mode.", notes = "Payment Mode. Related to Purchase Order Fulfilment based on Activate transaction")
    private Integer paymentMode;
    //    @ApiModelProperty(value = "Payment Details.", notes = "Payment Details. Related to Purchase Order Fulfilment based on Activate transaction")
//    private String paymentDetails;
//    @ApiModelProperty(value = "Bulk Type.", notes = "Related to Purchase Order Fulfilment based on Activate transaction. 0 - Indicates that a range of cards was used to activate all the cards for this Bulk Order 1 - Indicates that a CSV/TSV file was used at the client side to send all the cards for this Bulk Order.")
    private Boolean bulkType;
    //    @ApiModelProperty(value = "External Corporate Id.", notes = "External ID of the corporate")
//    private String externalCorporateId;
//    @ApiModelProperty(value = "External Card Number.", notes = "Another identity of the card")
//    private String externalCardNumber;
//    @ApiModelProperty(value = "Merchant Outlet Name.", notes = "The outletname/storename to which this POS is assigned to in the server")
//    private String merchantOutletName;
//    @ApiModelProperty(value = "Merchant Outlet Address1.", notes = "The address1 of the outlet if available.")
//    private String merchantOutletAddress1;
//    @ApiModelProperty(value = "Merchant Outlet Address2.", notes = "The address2 of the outlet if available.")
//    private String merchantOutletAddress2;
//    @ApiModelProperty(value = "Merchant Outlet City.", notes = "Merchant outlet city")
//    private String merchantOutletCity;
//    @ApiModelProperty(value = "Merchant Outlet State.", notes = "Merchant Outlet State")
//    private String merchantOutletState;
//    @ApiModelProperty(value = "Merchant Outlet PinCode.", notes = "Merchant Outlet Pin Code")
//    private String merchantOutletPinCode;
//    @ApiModelProperty(value = "Merchant Outlet Phone.", notes = "The phone number at Merchant Outlet")
//    private String merchantOutletPhone;
//    @ApiModelProperty(value = "Mask Card.", notes = "Only in Authorize method. Default is N. If set to Y, CardNumber would be masked in receipt. Value sent by server in Authorize method. Response will be 1 for Y and 0 for N.")
//    private Integer maskCard;
//    @ApiModelProperty(value = "Print Merchant Copy.", notes = "Only in Authorize method. Default is Y. If set to N, Merchant Copy of receipt is not printed. Value sent by server in Authorize method. Response will be 1 for Y and 0 for N.")
//    private Integer printMerchantCopy;
//    @ApiModelProperty(value = "Invoice Number Mandatory.", notes = "Only in Authorize method. Default is N. If set to Y, InvoiceNumber would be mandatory in Redeem transaction. Value sent by server in Authorize method. Response will be 1 for Y and 0 for N.")
//    private Integer invoiceNumberMandatory;
//    @ApiModelProperty(value = "Numeric User Pwd.", notes = "Only in Authorize method. Default is N. If set to Y, terminal should accept only numeric input for userid / password. Value sent by server in Authorize method. Response will be 1 for Y and 0 for N.")
//    private Integer numericUserPwd;
//    @ApiModelProperty(value = "Integer Amount.", notes = "Only in Authorize method. Default is N. If set to Y, terminal should not accept paise / decimals as input for Amount. Value sent by server in Authorize method. Response will be 1 for Y and 0 for N.")
//    private Integer integerAmount;
//    @ApiModelProperty(value = "Culture.", notes = "Culture name set on the server (default is en-GB)")
//    private String culture;
//    @ApiModelProperty(value = "Currency Symbol.", notes = "Currency symbol, default is Rs.")
//    private String currencySymbol;
//    @ApiModelProperty(value = "Currency Position.", notes = "Position of the Currency symbol, default is 2")
//    private String currencyPosition;
//    @ApiModelProperty(value = "Currency Decimal Digits.", notes = "Number of decimal digits for the amount field, default is 2")
//    private Integer currencyDecimalDigits;
//    @ApiModelProperty(value = "Display Unit For Points.", notes = "Display unit for Points in Loyalty, either Points or Value is used.")
//    private String displayUnitForPoints;
//    @ApiModelProperty(value = "Receipt Footer Line 1.", notes = "Only in Authorize method, used in receipt printing")
//    private String receiptFooterLine1;
//    @ApiModelProperty(value = "Receipt Footer Line 2.", notes = "Only in Authorize method, used in receipt printing")
//    private String receiptFooterLine2;
//    @ApiModelProperty(value = "Receipt Footer Line 3.", notes = "Only in Authorize method, used in receipt printing")
//    private String receiptFooterLine3;
//    @ApiModelProperty(value = "Receipt Footer Line 4.", notes = "Only in Authorize method, used in receipt printing")
//    private String receiptFooterLine4;
//    @ApiModelProperty(value = "Result.", notes = "Yet to be implemented")
    private Boolean result;
//    @ApiModelProperty(value = "Transfer Card Expiry.", notes = "Transfer Card Expiry Date, if implicit Transfer is enabled on the server. Format is YYYY-MM-DD HH:MM:SS")
    private String transferVoucherExpiryDate;
    //    @ApiModelProperty(value = "Transfer Card Balance.", notes = "Transfer Card Balance, if Implicit Transfer is enabled on the server")
    private BigDecimal transferVoucherBalance;
    @ApiModelProperty(value = "Card Status Id.", notes = "Card status ID. Possible Values: 100 CREATED,120 EXPIRED,130 PURCHASED,140 ACTIVATED,150 DEACTIVATED,300 ISSUED,310 CANCELLED")
    private Integer voucherStatusId;
    @ApiModelProperty(value = "Card Status.", notes = "Current Card Status in text form")
    private String voucherStatus;
    @ApiModelProperty(value = "Card Currency Symbol.", notes = "Card’s Native Currency symbol (useful in a multi-currency setup). Useful to display in customer receipts.")
    private String voucherCurrencySymbol;
    @ApiModelProperty(value = "Activation Date.", notes = "Card activation date, in YYYY-MM-DD HH:MM:SS format")
    private String activationDate;
    //    @JsonProperty("SVRecentTransactions")
//    @ApiModelProperty(value = "SV Recent Transactions.", notes = "Array of Json Object")
//    private List<SVRecentTransactionsResponse> sVRecentTransactions;
    @ApiModelProperty(value = "Card Type.", notes = "Card Program Group Name")
    private String voucherType;
    //    @ApiModelProperty(value = "Transfer Card Track Data.", notes = "Track Data of the transfer card(SVC Card)")
//    private String transferCardTrackData;
//    @ApiModelProperty(value = "Transfer Card Pin.", notes = "Card PIN of the transfer card(SVC Card)")
//    private String transferCardPin;
//    @ApiModelProperty(value = "Cumulative Amount Spent.", notes = "Total amount spent by the loyalty customer")
    private BigDecimal cumulativeAmountSpent;
    //    @ApiModelProperty(value = "Currency Conversion Rate.", notes = "Currency conversion rate rule used from the currency conversion table for conversion in a multi-currency setup. Useful to display the rate applied in customer receipts.")
    private BigDecimal currencyConversionRate;
    //    @ApiModelProperty(value = "Currency Converted Amount.", notes = "Transaction Amount / Card Balance converted using the Card’s native currency symbol (useful in a multi-currency setup). Useful to display in customer receipts")
    private BigDecimal currencyConvertedAmount;
    //    @ApiModelProperty(value = "Card Holder Name.", notes = "The full name of the card holder if available")
//    private String cardHolderName;
//    @ApiModelProperty(value = "Stored Value Unit ID.", notes = "Name of the Loyalty stored value")
    private Integer storedValueUnitID;
    //    @ApiModelProperty(value = "Xaction Amount Converted Value.", notes = "Stored value equivalent of the input transaction amount")
    private BigDecimal xactionAmountConvertedValue;
    //    @ApiModelProperty(value = "Stored Value Converted Amount.", notes = "Amount equivalent of the stored value")
    private BigDecimal storedValueConvertedAmount;
    //    @ApiModelProperty(value = "Promotional Value.", notes = "Promotional Value in appropriate Stored value units.")
    private BigDecimal promotionalValue;
    //    @ApiModelProperty(value = "Earned Value.", notes = "The loyalty value earned during the transaction")
    private BigDecimal earnedValue;
    //    @ApiModelProperty(value = "Transaction Amount.", notes = "Transaction Amount")
    private BigDecimal transactionAmount;
//    @ApiModelProperty(value = "Previous Balance.", notes = "Card balance before requested transaction")
    private BigDecimal previousBalance;
    //    @ApiModelProperty(value = "Upgraded Card Program Group Name.", notes = "Upgraded CPG name in case of an upgrade in a customer’s status")
//    private String upgradedCardProgramGroupName;
    @ApiModelProperty(value = "New Batch Number.", notes = "The new batch number assigned to the POS which initiated batch close. Going forward for all transactions from this POS, this new batch number should be used.")
    private Integer newBatchNumber;
    //    @ApiModelProperty(value = "Original Activation Amount.", notes = "First time Card Purchase amount")
    private BigDecimal originalActivationAmount;
    @ApiModelProperty(value = "Card Creation Type.", notes = "Virtual or Physical")
    private String voucherCreationType;
    //    @ApiModelProperty(value = "Error Code.", notes = "Error codes populated during system exceptions")
//    private String errorCode;
//    @ApiModelProperty(value = "Error Description.", notes = "Description of the error code")
//    private String errorDescription;
    @ApiModelProperty(value = "Card Program Group Type.", notes = "CPG Type like Retail Gift Cards, Corporate Cards etc.")
    private String vpgType;
    //    @JsonProperty("ActivationCode")
//    @ApiModelProperty(value = "Activation Code.", notes = "Intermediate reference to EGV number & PIN generated by eGMS. The activation code is part of the URL used to retrieve EGV information.")
//    private String activationCode;
//    @ApiModelProperty(value = "Activation URL.", notes = "The webpage/site integrated with eGMS server to retrieve eGV details (Card Number & PIN) using the Activation Code.")
//    private String activationURL;
//    @ApiModelProperty(value = "Reloadable Amount.", notes = "Maximum possible amount that can be reloaded on the Type 1 prepaid card")
    private BigDecimal reloadableAmount;
    @ApiModelProperty(value = "Barcode.", notes = "Qwikcilver 26 barcode")
    private String barcode;
    //
//    //	Customer
//    @ApiModelProperty(value = "Customer.")
//    private String customer;
//
    @ApiModelProperty(value = "Current Batch Number.", notes = "Current batch number assigned to this POS. This value needs to be sent in every request. When a batch close is performed, a new batch number is assigned.")
    private Integer currentBatchNumber;
    @ApiModelProperty(value = "Transaction Type.", notes = "Type of Transaction")
    private String transactionType;
    @ApiModelProperty(value = "Theme Id.", notes = "This is input to CreateAndIssue API, and same will be echoed back in CreateAndIssue, ActivateOnly, BalanceEnquiry APIs.")
    private String themeId;
    //@ApiModelProperty(value = "Pre Auth Code.", notes = "A unique 10-digit number generated for each PreAuth transaction")
    //private String preAuthCode;
    //@ApiModelProperty(value = "Currency Code.", notes = "Currency Code for the transaction")
    //private String currencyCode;
    //@ApiModelProperty(value = "Start Index.", notes = "For pulling out pagination based transaction history")
    //private Integer startIndex;
    //@ApiModelProperty(value = "UPC Bar Code.", notes = "Universal Product Code in barcode format")
    //private String uPCBarCode;
    //@ApiModelProperty(value = "Total Preauth Amount.", notes = "Total amount blocked for redemption")
    //private BigDecimal totalPreauthAmount;
    //@ApiModelProperty(value = "Total Redeemed Amount.", notes = "Total amount redeemed from the card")
    //private BigDecimal totalRedeemedAmount;
    //@ApiModelProperty(value = "Total Reloaded Amount.", notes = "Total amount reloaded from the card")
    //private BigDecimal totalReloadedAmount;

    //https://jira.gtech.asia/browse/MER-733
//    @JsonProperty("MerchantID")
//    @ApiModelProperty(value = "Merchant id.", notes = "Merchant id")
//    private Integer merchantID;
//
//    //https://jira.gtech.asia/browse/MER-733
//    @JsonProperty("MID")
//    @ApiModelProperty(value = "MID.", notes = "MID")
    private Integer merchantId;
    private Integer originalTransactionId;


    public ActivityOnlyResponse setActivityOnlyResonse(ActivateonlyResponse response) {
        if (response == null) {
            return this;
        }
        this.setResponseCode(response.getResponseCode());
        this.setResponseMessage(response.getResponseMessage());
        this.setTransactionId(response.getTransactionId());
//        this.setTrackData(response.getTrackData());
        this.setVoucherNumber(response.getCardNumber());
//        this.setCardPin(response.getCardPin());
//        this.setInvoiceNumber(response.getInvoiceNumber());
        this.setAmount(response.getAmount());
//        this.setBillAmount(response.getBillAmount());
//        this.setOriginalInvoiceNumber(response.getOriginalInvoiceNumber());
//        this.setOriginalTransactionId(response.getOriginalTransactionId());
        this.setOriginalBatchNumber(response.getOriginalBatchNumber());
//        this.setOriginalApprovalCode(response.getOriginalApprovalCode());
//        this.setOriginalAmount(response.getOriginalAmount());
        this.setApprovalCode(response.getApprovalCode());
//        this.setAddonCardNumber(response.getAddonCardNumber());
//        this.setAddonCardTrackData(response.getAddonCardTrackData());
//        this.setTransferCardNumber(response.getTransferCardNumber());
//        this.setMerchantName(response.getMerchantName());
        this.setAdjustmentAmount(response.getAdjustmentAmount());
        this.setVoucherExpiry(response.getCardExpiry());
//        this.setOriginalCardNumber(response.getOriginalCardNumber());
//        this.setOriginalCardPin(response.getOriginalCardPin());
//        this.setCardProgramID(response.getCardProgramID());
//        this.setCorporateName(response.getCorporateName());
        this.setNotes(response.getNotes());
        this.setSettlementDate(response.getSettlementDate());
//        this.setExpiry(response.getExpiry());
//        this.setPurchaseOrderNumber(response.getPurchaseOrderNumber());
        this.setPurchaseOrderValue(response.getPurchaseOrderValue());
        this.setDiscountPercentage(response.getDiscountPercentage());
        this.setDiscountAmount(response.getDiscountAmount());
        this.setPaymentMode(0);
//        this.setPaymentDetails(response.getPaymentDetails());
        this.setBulkType(response.getBulkType());
//        this.setExternalCorporateId(response.getExternalCorporateId());
//        this.setExternalCardNumber(response.getExternalCardNumber());
//        this.setMerchantOutletName(response.getMerchantOutletName());
//        this.setMerchantOutletAddress1(response.getMerchantOutletAddress1());
//        this.setMerchantOutletAddress2(response.getMerchantOutletAddress2());
//        this.setMerchantOutletCity(response.getMerchantOutletCity());
//        this.setMerchantOutletState(response.getMerchantOutletState());
//        this.setMerchantOutletPinCode(response.getMerchantOutletPinCode());
//        this.setMerchantOutletPhone(response.getMerchantOutletPhone());
//        this.setMaskCard(response.getMaskCard());
//        this.setPrintMerchantCopy(response.getPrintMerchantCopy());
//        this.setInvoiceNumberMandatory(response.getInvoiceNumberMandatory());
//        this.setNumericUserPwd(response.getNumericUserPwd());
//        this.setIntegerAmount(response.getIntegerAmount());
//        this.setCulture(response.getCulture());
//        this.setCurrencySymbol(response.getCurrencySymbol());
//        this.setCurrencyPosition(response.getCurrencyPosition());
//        this.setCurrencyDecimalDigits(response.getCurrencyDecimalDigits());
//        this.setDisplayUnitForPoints(response.getDisplayUnitForPoints());
//        this.setReceiptFooterLine1(response.getReceiptFooterLine1());
//        this.setReceiptFooterLine2(response.getReceiptFooterLine2());
//        this.setReceiptFooterLine3(response.getReceiptFooterLine3());
//        this.setReceiptFooterLine4(response.getReceiptFooterLine4());
        this.setResult(response.getResult());
        this.setTransferVoucherExpiryDate(response.getTransferCardExpiry());
        this.setTransferVoucherBalance(response.getTransferCardBalance());
        this.setVoucherStatusId(response.getCardStatusId());
        this.setVoucherStatus(response.getCardStatus());
        this.setVoucherCurrencySymbol(response.getCardCurrencySymbol());
        this.setActivationDate(response.getActivationDate());
//        this.setSVRecentTransactions(response.getSVRecentTransactions());
        this.setVoucherType(response.getCardType());
//        this.setTransferCardTrackData(response.getTransferCardTrackData());
//        this.setTransferCardPin(response.getTransferCardPin());
        this.setCumulativeAmountSpent(response.getCumulativeAmountSpent());
        this.setCurrencyConversionRate(response.getCurrencyConversionRate());
        this.setCurrencyConvertedAmount(response.getCurrencyConvertedAmount());
//        this.setCardHolderName(response.getCardHolderName());
        this.setStoredValueUnitID(response.getStoredValueUnitID());
        this.setXactionAmountConvertedValue(response.getXactionAmountConvertedValue());
        this.setStoredValueConvertedAmount(response.getStoredValueConvertedAmount());
        this.setPromotionalValue(response.getPromotionalValue());
        this.setEarnedValue(response.getEarnedValue());
        this.setTransactionAmount(response.getTransactionAmount());
        this.setPreviousBalance(response.getPreviousBalance());
//        this.setUpgradedCardProgramGroupName(response.getUpgradedCardProgramGroupName());
        this.setNewBatchNumber(response.getNewBatchNumber());
        this.setOriginalActivationAmount(response.getDenomination());
        this.setVoucherCreationType(response.getCardCreationType());
//        this.setErrorCode(response.getErrorCode());
//        this.setErrorDescription(response.getErrorDescription());
        this.setVpgType(response.getCardProgramGroupType());
//        this.setActivationCode(response.getActivationCode());
//        this.setActivationURL(response.getActivationURL());
        this.setReloadableAmount(response.getReloadableAmount());
        this.setBarcode(response.getBarcode());
//        this.setCustomer(response.getCustomer());
        this.setCurrentBatchNumber(response.getCurrentBatchNumber());
        this.setTransactionType(response.getTransactionType());
        this.setThemeId(response.getThemeId());
        //this.setPreAuthCode(response.getPreAuthCode());
        //this.setCurrencyCode(response.getCurrencyCode());
        //this.setStartIndex(response.getStartIndex());
        //this.setUPCBarCode(response.getUPCBarCode());
        //this.setTotalPreauthAmount(response.getTotalPreauthAmount());
        //this.setTotalRedeemedAmount(response.getTotalRedeemedAmount());
        //this.setTotalReloadedAmount(response.getTotalReloadedAmount());
//        this.setMerchantID(response.getMerchantID());
        this.setMerchantId(0);
        this.setOriginalTransactionId(0);
        return this;
    }

}
