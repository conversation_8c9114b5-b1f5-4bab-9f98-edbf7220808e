package com.gtech.gvcore.common.enums;

/**
 * 
 * <AUTHOR>
 * @description
 * @date 2022年3月8日
 *
 * @param <T>
 */
public interface IEnum<T> {

    /**
     * 获取code
     *
     * @return code
     */
    public T code();

    /**
     * 获取name
     *
     * @return name
     */
    public String desc();

    /**
     * 判断code是否相等
     * 
     * @param code
     * @return true-相等, false-不等
     */
    public boolean equalsCode(T code);

}
