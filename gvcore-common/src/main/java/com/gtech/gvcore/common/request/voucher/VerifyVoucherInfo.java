package com.gtech.gvcore.common.request.voucher;

import com.gtech.gvcore.common.request.transaction.CardInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class VerifyVoucherInfo {


    //0-Activation  1-Redemption 2-VerifyActivation  3-VerifyRedemption
    private String type;

    @ApiModelProperty(value = "Input Type. ", required = true, example = "0", notes = "1 - INDIVIDUAL 2 - RANGE 3 - EGV (Only for bulk activate)")
    private String inputType;

    //单张
    private CardInfo cardInfo;

    //批量（无使用）
    private List<CardInfo> cardInfos;

    //范围
    private CardInfo startCardInfo;
    private CardInfo endCardInfo;

}

