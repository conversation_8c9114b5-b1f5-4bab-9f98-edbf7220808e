package com.gtech.gvcore.common.request.issuer;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateIssuerRequest")
public class CreateIssuerRequest implements Serializable {


    private static final long serialVersionUID = 494666069330528822L;
    /**
     * issuer name
     */
    @ApiModelProperty( value="Issuer name.", example="Starbucks",required=true)
    @NotEmpty(message = "issuerName can not be empty")
    @Length(max = 100)
    private String issuerName;

    /**
     * create user
     */
    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;




}
