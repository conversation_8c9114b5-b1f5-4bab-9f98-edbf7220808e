package com.gtech.gvcore.common.request.meansofpayment;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryMeansOfPaymentsByPageRequest extends PageBean {

    @ApiModelProperty(value = "mopName", required = false)
    private String mopName;

    @ApiModelProperty(value = "mopCode", required = false)
    private String mopCode;

    @ApiModelProperty(value = "status", required = false)
    private Integer status;

    @ApiModelProperty(value = "outletCode", required = false)
    private String outletCode;

}
