package com.gtech.gvcore.common.request.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * Request for activating vouchers
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ActivateVouchersRequest", description = "Request to activate vouchers with OTP verification")
public class ActivateVouchersRequest {
    
    @NotBlank(message = "Token cannot be blank")
    @ApiModelProperty(value = "Activation token", required = true, example = "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8")
    private String token;
    
    @NotBlank(message = "OTP cannot be blank")
    @Pattern(regexp = "\\d{8}", message = "OTP must be 8 digits")
    @ApiModelProperty(value = "8-digit OTP code", required = true, example = "12345678")
    private String otp;
}
