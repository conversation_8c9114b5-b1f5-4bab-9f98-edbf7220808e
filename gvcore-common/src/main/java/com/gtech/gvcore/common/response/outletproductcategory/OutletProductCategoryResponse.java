package com.gtech.gvcore.common.response.outletproductcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OutletProductCategoryResponse")
public class OutletProductCategoryResponse {

    @ApiModelProperty(value = "Outlet product category code.", example = "112233")
    private String outletProductCategoryCode;

    @ApiModelProperty(value = "Outlet code.", example = "1122333")
    private String outletCode;

    @ApiModelProperty(value = "Product category code.", example = "2123123")
    private String productCategoryCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Create user.", example = "user12")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:22")
    private Date createTime;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:22")
    private Date updateTime;


}
