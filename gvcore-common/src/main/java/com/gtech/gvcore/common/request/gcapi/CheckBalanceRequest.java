package com.gtech.gvcore.common.request.gcapi;

import com.gtech.gvcore.common.response.gc.GiftCardInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * Check Balance Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CheckBalanceRequest", description = "Check Balance Request")
public class CheckBalanceRequest {

    @ApiModelProperty(value = "Gift Card List", notes = "List of gift cards to check balance", required = true, position = 1)
    @Valid
    private List<GiftCardInfo> giftCards;



}