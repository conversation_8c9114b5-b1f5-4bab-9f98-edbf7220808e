/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 * <p>
 * <PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.common.exception;

import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;


public abstract class ErrorCodes {

    protected ErrorCodes() {
    }

    public static final String VOUCHER_BATCH = "1001";

    public static final ErrorCode ERROR_UNKNOWN = Exceptions.errorCode(GvcoreUnknownException.class,
             "9999", "An unknown error.");

    public static final ErrorCode PARAM_EMPTY = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, "2701", "The Parameter ({0}) cannot be empty.");

    public static final ErrorCode PARAM_ERROR = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, "2702", "Parameter ({0}) specification error.");

    public static final ErrorCode PARAM_LEAST_ONE = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, "2703", "At least one parameter ({0}) needs to be specified.");

    public static final ErrorCode PARAM_SPECIFICATION_ERROR = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, "2704", "{0}.");

    public static final ErrorCode PARAM_CALIBRATION_ERROR = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, "2705", "Parameter ({0}) calibration error.");

    public static final ErrorCode PARAM_FORMAT_ERROR = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, "2706", "{0} format error.");


    public static final ErrorCode ERROR_EXCEL_EXPORT = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, ".error-excel-export", "Export excel file failed.");

    public static final ErrorCode ERROR_DATA_TO_BYTE_ARRAY = Exceptions.errorCode(GvcoreParamValidateException.class,
            VOUCHER_BATCH, ".error-data-to-ByteArray", "Data to ByteArray failed.");
}
