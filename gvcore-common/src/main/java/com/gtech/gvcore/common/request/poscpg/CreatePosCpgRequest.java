package com.gtech.gvcore.common.request.poscpg;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreatePosCpgRequest")
public class CreatePosCpgRequest {


    private String posCode;

    private String cpgCode;

    private String createUser;
}
