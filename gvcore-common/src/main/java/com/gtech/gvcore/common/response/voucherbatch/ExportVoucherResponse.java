package com.gtech.gvcore.common.response.voucherbatch;

import com.gtech.basic.filecloud.exports.core.annotation.FileColumn;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/3/9 17:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ExportVoucherResponse")
public class ExportVoucherResponse  {



    @FileColumn(name = "BOOKLET NUMBER")
    private String bookletCode;


    @FileColumn(name = "BOOKLET-QCBARCODE-27-V1")
    private String bookletBarCode;


    @FileColumn(name = "VOUCHER NUMBER")
    private String voucherCode;


    @FileColumn(name = "CARD-QCBARCODE-27-V1")
    private String voucherBarCode;


    @FileColumn(name = "AMOUNT")
    private String denomination;


    @FileColumn(name = "VOUCHER EXPIRY")
    private String voucherEffectiveDate;



}
