package com.gtech.gvcore.common.response.transaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CardResponse {

	@ApiModelProperty(value = "Card Number.", required = true, notes = "End card number in the lineitem")
	private String cardNumber;
	@JsonProperty("cardPin")
	@ApiModelProperty(value = "Card Pin.", notes = "Card Pin returned in case of EGV line item")
	private String cardPin;
	@ApiModelProperty(value = "Activation Code.", notes = "Depends on program setting")
	private String activationCode;
	@ApiModelProperty(value = "Card Creation Type.", notes = "Virtual or Physical")
	private String cardCreationType;
	@ApiModelProperty(value = "Activation URL.", notes = "Depends on program setting")
	private String activationURL;
	@ApiModelProperty(value = "Card Balance.", notes = "")
	private BigDecimal cardBalance;
	@ApiModelProperty(value = "Card Expiry.", notes = "card expiry")
	private String cardExpiry;
	@ApiModelProperty(value = "Transaction Amount.", notes = "transacted amount @ card level")
	private BigDecimal transactionAmount;
	@ApiModelProperty(value = "Card Status.", notes = "Please refer lookup in intro section")
	private String cardStatus;
	@ApiModelProperty(value = "Approval Code.", notes = "only for activate and redeem")
	private String approvalCode;
	@ApiModelProperty(value = "Card Program Group Name.", notes = "Card program group")
	private String cardProgramGroupName;
	@ApiModelProperty(value = "Sequence No.", notes = "Sequence number associated to the card")
	private Long sequenceNo;
	@ApiModelProperty(value = "Product Code.", notes = "Product code")
	private String productCode;
	@ApiModelProperty(value = "Design Code.", notes = "Design code")
	private String designCode;
	@ApiModelProperty(value = "Track Data.", notes = "trackData")
	private String trackData;
	@ApiModelProperty(value = "barcode.", notes = "barcode")
	private String barcode;
	@ApiModelProperty(value = "Response Code.", required = true, notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
	private Integer responseCode;
	@ApiModelProperty(value = "Response Message.", notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
	private String responseMessage;

}