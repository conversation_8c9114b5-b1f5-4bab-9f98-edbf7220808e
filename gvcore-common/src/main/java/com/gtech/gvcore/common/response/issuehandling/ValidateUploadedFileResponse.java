package com.gtech.gvcore.common.response.issuehandling;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class ValidateUploadedFileResponse {

    private String uploadedFileName;
	
	private Integer processStatus;
	
	private Integer countVoucher;
	
	private Integer countFailed;
	
	private String result;
	
	private List<ExceptionRow> exceptionRowList;
	
	private String startTime;
}
