package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/6/24 14:41
 */
public enum VpgDisableGenerationEnum implements IEnum{


    ENABLE("0", "Can be used to generate"),
    DISABLED("1", "Disable generation, sales");



    private final String code;

    private final String desc;

    VpgDisableGenerationEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }
    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }



    @Override
    public boolean equalsCode(Object code) {
        return this.code.equals(code);
    }


}
