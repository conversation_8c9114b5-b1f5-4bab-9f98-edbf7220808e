package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;

/**
 * Dynamic Barcode Response
 */
@Data
@ApiModel(value = "DynamicBarcodeResponse", description = "Dynamic Barcode Response")
public class DynamicBarcodeResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number")
    private String giftCardNumber;

    @ApiModelProperty(value = "Barcode", notes = "Dynamically generated barcode")
    private String barcode;

    @ApiModelProperty(value = "Barcode Expiry", notes = "Dynamically generated barcode expiry time")
    private Date barcodeExpiry;

    @ApiModelProperty(value = "GCPG", notes = "GCPG name")
    private String gcpg;

    @ApiModelProperty(value = "Denomination", notes = "Denomination when first time purchased gift card")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Remaining Balance", notes = "Remaining Balance for Gift card")
    private BigDecimal remainingBalance;

    @ApiModelProperty(value = "Redemption Amount", notes = "Amount to be redeemed")
    private BigDecimal redemptionAmount;

} 