package com.gtech.gvcore.common.response.releaseapprove;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 15:35
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApproveNodeRecordResponse implements Serializable {
    private static final long serialVersionUID = 8427587580792777496L;
    private String approveNodeRecordCode;
    private String approveType;
    private String businessCode;
    private String note;
    private Boolean status;
    private String approveUser;
    private String approveRoleCode;
    private Date approveTime;

}
