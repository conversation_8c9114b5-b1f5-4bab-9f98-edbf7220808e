package com.gtech.gvcore.common.utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BeanUtil extends BeanUtils {

	public static <T> T make(Class<T> targetCls, Object source) {

		try {

			T dest = targetCls.newInstance();

			copyProperties(dest, source);

			return dest;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return null;
	}

	public static <T> T makeByJson(Class<T> targetCls, Object source) {

		try {

			return JSON.parseObject(JSON.toJSONString(source), targetCls);

		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return null;
	}

	public static <T> List<T> makeListByJson(Class<T> targetCls, Object source) {

		try {

			return JSON.parseArray(JSON.toJSONString(source), targetCls);

		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return new ArrayList<>();
	}

	public static <T> List<T> makeList(Class<T> targetCls, List<?> source) {
		List<T> destList = new ArrayList<>();
		if (CollectionUtils.isEmpty(source)) {
			return destList;
		}
		try {
			source.forEach(s -> destList.add(make(targetCls, s)));
			return destList;

		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return destList;
	}


	public static String[] getNullPropertyNames(Object source) {
		final BeanWrapper src = new BeanWrapperImpl(source);
		java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

		Set<String> emptyNames = new HashSet<>();
		for (java.beans.PropertyDescriptor pd : pds) {
			Object srcValue = src.getPropertyValue(pd.getName());
			if (srcValue == null)
				emptyNames.add(pd.getName());
		}
		String[] result = new String[emptyNames.size()];
		return emptyNames.toArray(result);
	}

	public static void copyPropertiesIgnoreNull(Object src, Object target) {
		org.springframework.beans.BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
	}
}
