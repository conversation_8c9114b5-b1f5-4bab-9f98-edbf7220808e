package com.gtech.gvcore.common.request.orderreport;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/6 11:29
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "CreateOrderReportRequest")
public class CreateReportRequest implements Serializable {

    private static final long serialVersionUID = -9111488731761660572L;


    @ApiModelProperty(value = "Report type", required = true)
    @NotNull(message = "reportType can not be empty")
    private Integer reportType;

    @ApiModelProperty(value = "Create user", required = true)
    private String createUser;


    /**
     * 该字段用于权限判断不由外部传递
     */
	@ApiModelProperty(value = "Issuer code list", hidden = true)
	private List<String> issuerCodeList;

	@ApiModelProperty(value = "Issuer code")
	private String issuerCode;

    @ApiModelProperty(value = "Company code")
    private List<String> companyCodes;

    @ApiModelProperty(value = "Merchant code")
    private List<String> merchantCodes;

    @ApiModelProperty(value = "Outlet code")
    private List<String> outletCodes;

    @ApiModelProperty(value = "outbound Outlet code")
    private List<String> outboundCodeList;

    @ApiModelProperty(value = "inbound Outlet code")
    private List<String> inboundCodeList;


    @ApiModelProperty(value = "Individual or Corporate")
    private String customerType;

    @ApiModelProperty(value = "Customer code")
    private List<String> customerCodes;

    @ApiModelProperty(value = "Contact division.", example = "user1")
    private String contactDivision;


    @ApiModelProperty(value = "Voucher code")
    private String voucherCode;

    @ApiModelProperty(value = "voucherStatus")
    private List<String> voucherStatus;

    @ApiModelProperty(value = "Voucher code num start")
    private Long voucherCodeNumStart;

    @ApiModelProperty(value = "Voucher code number end")
    private Long voucherCodeNumEnd;

    @ApiModelProperty(value = "Voucher effective Date start")
    private Date voucherEffectiveDateStart;

    @ApiModelProperty(value = "Voucher effective date end")
    private Date voucherEffectiveDateEnd;

    @ApiModelProperty(value = "Select migration data flag default = false")
    private Boolean selectMigrationDataFlag = false;


    @ApiModelProperty(value = "Booklet status")
    private List<String> bookletStatus;

    @ApiModelProperty(value = "Booklet start")
    private String bookletStart;

    @ApiModelProperty(value = "Booklet end")
    private String bookletEnd;


    @ApiModelProperty(value = "Transaction date start")
    private Date transactionDateStart;

    @ApiModelProperty(value = "Transaction date end")
    private Date transactionDateEnd;

    @ApiModelProperty(value = "Expiry date start")
    private Date expiryDateStart;

    @ApiModelProperty(value = "Expiry date end")
    private Date expiryDateEnd;

    @ApiModelProperty(value = "transactionType")
    private List<String> transactionTypes;

    @ApiModelProperty(value = "transactionStatus")
    private String transactionStatus;

    @ApiModelProperty(value = "Transaction Corporate Name.", example = "user1")
    private String transactionCorporateName;


    @ApiModelProperty(value = "Purchase order number (PO NUMBER)")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "Invoice number")
    private String invoiceNo;

    @ApiModelProperty(value = "Voucher program group code")
    private List<String> cpgCodes;

    @ApiModelProperty(value = "Order status")
    private List<String> orderStatuses;
    @ApiModelProperty(value = "用户邮箱")
    private String customerEmail;


    @ApiModelProperty(value = "操作时间起始")
    private Date operateTimeBegin;

    @ApiModelProperty(value = "操作时间截止")
    private Date operateTimeEnd;

    @ApiModelProperty(value = "操作人编码")
    private String operateUserCode;

    @ApiModelProperty(value = "sysLoggerId")
    private String sysLoggerId;


    @ApiModelProperty(value = "uploadedFileUrl")
    private String uploadedFileUrl;

    @ApiModelProperty(value = "uploadedFileName")
    private String uploadedFileName;


    @ApiModelProperty(value = "voucherRequestId (卡券移动的请求id)")
    private String voucherRequestId;

    @ApiModelProperty(value = "voucherRequestSourceList")
    private List<String> voucherRequestSourceList;

    @ApiModelProperty(value = "voucherAllocationStatusList")
    private List<String> voucherAllocationStatusList;

    @ApiModelProperty(value = "voucherPrintingStatusList") //Voucher Printing Report -> Status
    private List<String> voucherPrintingStatusList;

    @ApiModelProperty(value = "printingVendorList")// Voucher Printing Report - > Printing Vendor
    private List<String> printingVendorList;

    @ApiModelProperty(value = "voucherReturnTransferStatusList")// Voucher Return & Transfer Report -> Status
    private List<String> voucherReturnTransferStatusList;

    @ApiModelProperty(value = "voucherReturnTransferFromStoreList")// Voucher Return & Transfer Report -> From/To Store Name
    private List<String> voucherReturnTransferFromStoreList;

    @ApiModelProperty(value = "voucherReturnTransferToStoreList")// Voucher Return & Transfer Report -> From/To Store Name
    private List<String> voucherReturnTransferToStoreList;

    @ApiModelProperty(value = "webReturnTransferFromTo(前端为了回显使用的字段后端不做处理)")
    private String webReturnTransferFromTo;

	private List<String> voucherCodeList;

    private Date activationDateStart;
    private Date activationDateEnd;

    private Date purchaseDateStart;
    private Date purchaseDateEnd;
}
