/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.common.response.useraccount;

import java.io.Serializable;
import java.util.List;

import com.gtech.basic.idm.web.vo.bean.RoleInfoBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * GetUserAccountResult
 *
 *
 * @Date 2019-09-24 16:20
 */
@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GvUserAccountResponse")
public class UserAccountResponse implements Serializable {

    private static final long serialVersionUID = 1926224132283848897L;

    @ApiModelProperty(value = "Domain code.", example="D00001", required=true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.", example="T00001", required=true)
    private String tenantCode;

    @ApiModelProperty(value = "User code.", example="U00001", required=true)
    private String userCode;

    @ApiModelProperty(value = "User type. (1-PUser 2-BUser 3-CUser)", example="1", required=true)
    private String userType;

    @ApiModelProperty(value = "Login account.", example="jack.zhang", required=true)
    private String account;

    @ApiModelProperty(value = "Last name.", example="zhang")
    private String lastName;

    @ApiModelProperty(value = "First name.", example="jack")
    private String firstName;

	@ApiModelProperty(value = "Full name.", example = "jack zhang")
	private String fullName;

    @ApiModelProperty(value = "Email address.", example="<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Email address verified flag. (0-unverified, 1-verified)", example="1")
    private String emailVerified;

    @ApiModelProperty( value="Country code", example="+86")
    private String mobileCountry;

    @ApiModelProperty(value = "Mobile number.", example="***********")
    private String mobile;

    @ApiModelProperty(value = "Mobile number verified flag. (0-unverified, 1-verified)", example="1")
    private String mobileVerified;

    @ApiModelProperty(value = "Organization code.", example="O00001")
    private String orgCode;

    @ApiModelProperty(value = "Source of user registration.", example="speedwork")
    private String source;

    @ApiModelProperty(value = "User account status. (0-invalid 1-valid 2-disabled by system 3-disabled by admin)", example="1")
    private String status;

    @ApiModelProperty(value = "User roles list.")
    private List<RoleInfoBean> roleList;

    @ApiModelProperty(value = "Last update password time")
    private String lastUpdatePasswordTime;

    @ApiModelProperty(value = "Tenant creator",example = "1",notes = "Is tenant create user(1-Yes,0-No)")
    private Integer tenantCreator;

	@ApiModelProperty(value = "Issuer code.")
	private String issuerCode;

	@ApiModelProperty(value = "If need double check when login.", example = "1")
	private Integer doubleCheck;

	@ApiModelProperty(value = "Issuer permission list.")
	private List<IssuerPermissionResponse> issuerPermissionList;

}
