package com.gtech.gvcore.common.request.cpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 16:25
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Query outlet denomination request")
public class QueryOutletDenominationRequest {

    @NotBlank(message = "Outlet code can not be empty")
    @ApiModelProperty(value = "Outlet code", required = true, example = "OU102203211651000029")
    private String outletCode;
}
