package com.gtech.gvcore.common.request.customer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/31 13:59
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetEmailValidateCodeRequest {

    @ApiModelProperty(value = "email", required = true)
    @Email(message = "Please enter the correct email address")
    @NotBlank(message = "email can not be empty")
    private String email;


    @ApiModelProperty(value = "Issuer code")
    private String issuerCode;




}
