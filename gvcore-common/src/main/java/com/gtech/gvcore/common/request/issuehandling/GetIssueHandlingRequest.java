package com.gtech.gvcore.common.request.issuehandling;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月6日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetIssueHandlingRequest {
	
    @ApiModelProperty(value = "issueHandlingCode", required = true)
    @NotEmpty(message = "issueHandlingCode can not be empty")
	private String issueHandlingCode;

}


