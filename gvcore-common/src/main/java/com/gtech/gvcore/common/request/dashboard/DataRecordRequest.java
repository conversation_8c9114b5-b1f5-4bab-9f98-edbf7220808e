package com.gtech.gvcore.common.request.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/9/14 14:11
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "DataRecordRequest")
public class DataRecordRequest {

    @NotNull(message = "date can not be null")
    @ApiModelProperty(value = "Date", example = "2022-08-31 15:26:12")
    private Date date;
}
