package com.gtech.gvcore.common.response.releaseapprove;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 16:54
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("GetNextAmountResponse")
public class GetNextAmountResponse {
    @ApiModelProperty(value = "Range name ", example = "1")
    private Integer rangeName;

    @ApiModelProperty(value = "Start num ", example = "1")
    private BigDecimal startNum;


}
