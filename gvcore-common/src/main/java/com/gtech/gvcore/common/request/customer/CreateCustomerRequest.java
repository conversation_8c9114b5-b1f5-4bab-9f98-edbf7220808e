package com.gtech.gvcore.common.request.customer;

import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.commons.utils.CheckUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateCustomerRequest")
public class CreateCustomerRequest {


    public static final String CORPORATE = "Corporate";
    public static final String INDIVIDUAL = "Individual";


    @ApiModelProperty(value = "Customer name.", example = "user1")
    private String customerName;


    @ApiModelProperty(value = "Issuer code.", example = "123124", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 100)
    private String issuerCode;

    @ApiModelProperty(value = "Outlet code.", example = "123124", required = true)
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Product category code.", example = "112323", required = true)
    private List<String> productCategoryCodeList;

    @ApiModelProperty(value = "Product category code.", example = "1")
    private String mopGroup;

    @ApiModelProperty(value = "Customer type.", example = "1", required = true)
    @NotEmpty(message = "customerType can not be empty")
    @Length(max = 100)
    private String customerType;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Company name.", example = "*********")
    @Length(max = 100)
    private String companyName;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Contact first name.", example = "user1")
    @Length(max = 100)
    private String contactFirstName;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Contact first name.", example = "user2")
    @Length(max = 100)
    private String contactLastName;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Contact division.", example = "user1")
    @Length(max = 100)
    private String contactDivision;

    @ApiModelProperty(value = "Contact phone.", example = "11223344", required = true)
    @NotEmpty(message = "contactPhone can not be empty")
    @Length(max = 100)
    private String contactPhone;

    @ApiModelProperty(value = "Contact email.", example = "<EMAIL>")
    @Length(max = 100)
    @Email(message = "email format error ")
    private String contactEmail;

    @ApiModelProperty(value = "Shipping address1.", example = "2nd Floor NO.23")
    @Length(max = 512)
    @NotEmpty(message = "shippingAddress1 can not be empty")
    private String shippingAddress1;

    @ApiModelProperty(value = "Shipping address2.", example = "2nd Floor NO.23")
    @Length(max = 512)
    private String shippingAddress2;

    /**
     * 在customerType为Corporate时必填
     */
    @ApiModelProperty(value = "Transfer account.", example = "**********")
    @Length(max = 100)
    private String transferAccount;

    @ApiModelProperty(value = "Band card issuer.", example = " ********")
    @Length(max = 100)
    private String bankCardIssuer;

    @ApiModelProperty(value = "Note.", example = "note")
    @Length(max = 500)
    private String note;

    @ApiModelProperty(value = "Create user.", example = "user1")
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;

    @ApiModelProperty(value = "Distribution Function.", example = "0")
    @Length(max = 10)
    private String distributionFunction;

    @ApiModelProperty(value = "User email", example = "<EMAIL>;<EMAIL>")
    private String userEmail;

    @ApiModelProperty(value = "channel", example = "")
    private String channel;

    @ApiModelProperty(value = "Registration Year", example = "2021")
    private String registrationYear;

    @ApiModelProperty(value = "Beneficiary Name", example = "")
    private String beneficiaryName;

    @ApiModelProperty(value = "Branch Name", example = "")
    private String branchName;

    @ApiModelProperty(value = "Bank Name", example = "")
    private String bankName;

    @ApiModelProperty(value = "Account Number", example = "")
    private String accountNumber;

    @ApiModelProperty(value = "pph(0/1)", example = "0")
    private Integer pph;

    public void validation() {

        if (customerType.equals(CORPORATE)) {

            CheckUtils.isNotBlank(this.transferAccount, ErrorCodes.PARAM_EMPTY, "transferAccount");
            CheckUtils.isNotBlank(this.contactFirstName, ErrorCodes.PARAM_EMPTY, "contactFirstName");
            CheckUtils.isNotBlank(this.contactLastName, ErrorCodes.PARAM_EMPTY, "contactLastName");
            CheckUtils.isNotBlank(this.contactDivision, ErrorCodes.PARAM_EMPTY, "contactDivision");

        }


    }


}
