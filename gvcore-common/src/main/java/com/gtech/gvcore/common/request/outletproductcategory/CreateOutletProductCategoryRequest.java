package com.gtech.gvcore.common.request.outletproductcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateOutletProductCategoryRequest")
public class CreateOutletProductCategoryRequest {


    @ApiModelProperty(value = "Outlet code.", example = "1122333",required = true)
    @NotEmpty(message = "outletCode can not be empty")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Product category code.", example = "2123123",required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    @Length(max = 100)
    private String productCategoryCode;

    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;








}
