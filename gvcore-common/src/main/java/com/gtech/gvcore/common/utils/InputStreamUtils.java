package com.gtech.gvcore.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-03-08 14:44
 **/
@Slf4j
public class InputStreamUtils {

    /**
     * 连接超时时间
     */
    private static final int CONNECTION_TIMEOUT = 120 * 1000;

    /**
     * 连接超时时间
     */
    private static final int READ_TIMEOUT = 120 * 1000;

    /**
     * 根据地址获得数据的输入流
     *
     * @param strUrl 网络连接地址
     * @return url的输入流
     */
    public static java.io.InputStream getInputStreamByUrl(String strUrl) {

        HttpURLConnection conn = null;

        try (final ByteArrayOutputStream output = new ByteArrayOutputStream()) {

            URL url = new URL(strUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");

            log.info("connectionTimeout:{}", CONNECTION_TIMEOUT);
            conn.setConnectTimeout(CONNECTION_TIMEOUT);

            log.info("readTimeout:{}", READ_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);


            IOUtils.copy(conn.getInputStream(), output);

            return new ByteArrayInputStream(output.toByteArray());

        } catch (Exception e) {
            log.error("getInputStreamByUrl Exception:{}", e.getMessage());

        } finally {
            try {
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (Exception e) {
                log.error("getInputStreamByUrl disconnect Exception:{}", e.getMessage());
            }
        }
        return null;
    }

    public static byte[] toByteArray(InputStream input) throws IOException {

        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = input.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();

        return  buffer.toByteArray();
    }

    private InputStreamUtils() {
    }
}
