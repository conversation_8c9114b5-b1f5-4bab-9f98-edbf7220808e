package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 购买礼品卡响应
 */
@Data
@ApiModel(value = "BuyGiftCardResponse", description = "购买礼品卡响应")
public class BuyGiftCardResponse extends BaseApiResponse {
    

    
    @ApiModelProperty(value = "备注", notes = "参考文本信息")
    private String notes;
    
    @ApiModelProperty(value = "重试键", notes = "请求的参考编号")
    private String retryKey;
    
    @ApiModelProperty(value = "来源", notes = "Reference for outletname/POSname/brandname/partnername")
    private String source;

    @ApiModelProperty(value = "订单号", notes = "购买礼品卡的订单号")
    private String orderNumber;

    @ApiModelProperty(value = "Gift Cards", notes = "List of issued gift cards")
    private List<GiftCardInfo> giftCards;
    
    @ApiModelProperty(value = "面额", notes = "购买的礼品卡面额")
    private BigDecimal denomination;
    
    @ApiModelProperty(value = "货币", notes = "货币代码")
    private String currency;
    
    @ApiModelProperty(value = "购买日期", notes = "购买日期")
    private Date purchaseDate;

    @ApiModelProperty(value = "GCPG", notes = "GCPG名称")
    private String gcpg;
    
    @ApiModelProperty(value = "批准码", notes = "交易批准码")
    private String approvalCode;
} 