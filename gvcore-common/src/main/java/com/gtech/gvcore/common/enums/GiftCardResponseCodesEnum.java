package com.gtech.gvcore.common.enums;

/**
 * Gift Card API Response Codes Enum
 * Error codes starting with 6 (60000-69999) for Gift Card specific errors
 */
public enum GiftCardResponseCodesEnum {
    
    // Success
    TRANSACTION_SUCCESS(60000, "Transaction successful.", "Gift card transaction completed successfully"),
    
    // Common validation errors (60001-60099)
    TOKEN_IS_NULL(60001, "token is null", "Token field is required in request header"),
    TRANSACTION_ID_IS_NULL(60002, "transactionId is null", "TransactionId field is required in request header"),
    CLIENT_TIME_IS_NULL(60003, "clientTime is null", "ClientTime field is required in request header"),
    TOKEN_EXPIRED(60004, "token is already expired", "The provided token has expired"),
    CLIENT_TIME_FORMAT_INVALID(60005, "clientTime format is not \"YYYY-MM-DD'T'HH:MM:SS\"", "ClientTime must be in YYYY-MM-DD'T'HH:MM:SS format"),
    INVOICE_DATE_FORMAT_INVALID(60006, "invoiceDate format is not \"YYYY-MM-DD'T'HH:MM:SS\"", "InvoiceDate must be in YYYY-MM-DD'T'HH:MM:SS format"),
    TRANSACTION_FAILED(60007, "Transaction failed", "The transaction failed due to a business error"),
    INVALID_TERMINAL_ID(60008, "Invalid terminalId", "TerminalId is invalid"),
    MERCHANT_OUTLET_AUTH_FAILED(60009, "Merchant Outlet Auth failed", "Merchant Outlet does not have permission to perform the transaction"),
    CUSTOMER_ID_IS_NULL(60010, "customerId is null", "customerId is required in request body"),

    // gc/gcissuance specific errors (60100-60199)
    TRANSACTION_CODE_IS_NULL(60100, "transactionCode is null", "TransactionCode field is required in request body"),
    INPUT_TYPE_IS_NULL(60101, "inputType is null", "InputType field is required in request body"),
    NUMBER_OF_GIFT_CARDS_IS_NULL(60102, "numberOfGiftCards is null", "NumberOfGiftCards field is required in request body"),
    INVOICE_NUMBER_IS_NULL(60103, "invoiceNumber is null", "InvoiceNumber field is required in request body"),
    GCPG_IS_NULL(60104, "gcpg is null", "Gcpg field is required in request body"),
    NUMBER_OF_GIFT_CARDS_EXCEEDED(60105, "numberOfGiftCards has exceeded the maximum quantity allowed", "Number of gift cards requested exceeds system limit"),
    GCPG_DOES_NOT_EXIST(60106, "gcpg doesn't exist", "The specified gcpg does not exist in system"),
    CPG_IS_DISABLED(60107, "cpg is already disabled", "The CPG associated with gcpg is disabled"),
    BUYER_REQUIRED_FIELDS_MISSING(60108, "buyer required fields are missing", "Required customer fields in buyer object are not provided"),
    
    // gc/addgc specific errors (60200-60299)
    GIFT_CARD_NUMBER_IS_NULL(60200, "giftCardNumber is null", "GiftCardNumber field is required in request body"),
    GIFT_CARD_PIN_IS_NULL(60201, "giftCardPIN is null", "GiftCardPIN field is required in request body"),
    GIFT_CARD_EXPIRY_IS_NULL(60202, "giftCardExpiry is null", "GiftCardExpiry field is required in request body"),
    CUSTOMER_IS_NULL(60203, "customer is null", "Customer field is required in request body"),
    GIFT_CARD_NUMBER_DOES_NOT_EXIST(60204, "giftCardNumber doesn't exist", "The specified gift card number does not exist"),
    CARD_STATUS_NOT_ALLOWED(60205, "status of the card is not allowed", "Card status does not allow this operation"),
    CARD_ALREADY_EXPIRED(60206, "card is already expired", "The gift card has expired and cannot be activated"),
    GIFT_CARD_PIN_NOT_MATCHING(60207, "giftCardPIN is not matching with giftCardNumber", "The provided PIN does not match the gift card number"),
    ACTIVATION_CODE_NOT_MATCHING(60208, "activationCode is not matching with giftCardNumber", "The provided activation code does not match the gift card number"),
    GIFT_CARD_ACTIVATION_EXPIRY_NOT_MATCHING(60209, "giftCardActivationExpiry is not matching with card's activation deadline", "The provided activation expiry does not match the card's activation deadline"),
    CARD_ALREADY_ACTIVE(60210, "card is already active", "The gift card is already active"),

    // gc/checkbalance specific errors (60300-60399)
    // GIFT_CARD_NUMBER_IS_NULL already defined above (60200)
    // GIFT_CARD_NUMBER_DOES_NOT_EXIST already defined above (60204)
    
    // gc/redeem specific errors (60400-60499)
    GIFT_CARDS_IS_NULL(60400, "giftCards is null", "GiftCards field is required in request body"),
    TRANSACTION_AMOUNT_IS_NULL(60401, "transactionAmount is null", "TransactionAmount field is required in request body"),
    // INVOICE_NUMBER_IS_NULL already defined above (60103)
    GIFT_CARD_NUMBER_IN_GIFT_CARDS_DOES_NOT_EXIST(60402, "giftCardNumber in giftCards body doesn't exist", "One or more gift card numbers in giftCards array do not exist"),
    REDEMPTION_AMOUNT_NOT_ALLOWED(60403, "RedemptionAmount is not allowed", "Redemption amount must be greater than 0 and not exceed available balance"),
    GIFT_CARD_NOT_EXIST(60404, "GiftCard doesn't exist", "Gift card does not exist."),
    GIFT_CARD_NOT_ACTIVATED(60405, "GiftCard is not activated", "Gift card is not activated and balance cannot be deducted"),
    GIFT_CARD_EXPIRED(60406, "GiftCard is expired", "Gift card has expired and balance cannot be deducted"),
    GIFT_CARD_DESTROYED(60407, "GiftCard is destroyed", "Gift card is destroyed and balance cannot be deducted"),
    DEDUCTION_AMOUNT_NOT_ALLOWED(60408, "DeductionAmount is not allowed", "Deduction amount must be greater than 0 and not exceed available balance"),
    INSUFFICIENT_BALANCE(60409, "Insufficient balance", "Insufficient balance in gift card"),
    GIFT_CARDS_ITEM_NO_IS_NULL(60410, "Item No is null", "GiftCards item no is required in request body"),
    REDEMPTION_AMOUNT_IS_NULL(60411, "redemptionAmount is null", "RedemptionAmount field is required in request body"),

    // GIFT_CARD_PIN_NOT_MATCHING already defined above (60207)
    // CARD_STATUS_NOT_ALLOWED already defined above (60205)
    // CARD_ALREADY_EXPIRED already defined above (60206)
    
    // gc/dynamicbarcode specific errors (60500-60599)
    // TOKEN_IS_NULL, TRANSACTION_ID_IS_NULL, CLIENT_TIME_IS_NULL already defined above
    // GIFT_CARD_NUMBER_IS_NULL already defined above (60200)
    // GIFT_CARD_NUMBER_DOES_NOT_EXIST already defined above (60204)
    INVALID_BARCODE(60501, "barcode is invalid", "The provided barcode is not valid"),
    CARD_STATUS_NOT_ALLOWED_FOR_BARCODE(60500, "status of the card is \"expired\",\"deactivated\" or \"destroyed\", not allowed", "Card status does not allow barcode generation"),
    
    // gc/extendactivate specific errors (60600-60699)
    // TOKEN_IS_NULL already defined above (60001)
    // GIFT_CARD_NUMBER_IS_NULL already defined above (60200)
    // GIFT_CARD_NUMBER_DOES_NOT_EXIST already defined above (60204)
    // CARD_STATUS_NOT_ALLOWED already defined above (60205)
    EXTEND_ACTIVATION_TIME_INVALID(60600, "extend activation time is invalid", "Extension time must be after card's activatable period"),
    EXTEND_ACTIVATION_PERIOD_EXCEEDED(60601, "extend activation period exceeded", "Cannot extend activation beyond allowed extension period"),
    CARD_ALREADY_EXTENDED(60602, "card activation has already been extended", "Card activation period has already been extended once"),
    CARD_NOT_YET_ACTIVATED_TIME(60603, "not yet activated expiration time", "Gift card has not reached the activation expiration time and cannot be extended"),

    // gc/queryCardStatement specific errors (60700-60799)
    // TOKEN_IS_NULL already defined above (60001)
    // GIFT_CARD_NUMBER_IS_NULL already defined above (60200)
    // GIFT_CARD_NUMBER_DOES_NOT_EXIST already defined above (60204)
    
    // gc/queryCardDetails specific errors (60800-60899)
    // TOKEN_IS_NULL already defined above (60001)
    // GIFT_CARD_NUMBER_IS_NULL already defined above (60200)
    // GIFT_CARD_NUMBER_DOES_NOT_EXIST already defined above (60204)
    
    // gc/queryCustomerCardList specific errors (60900-60999)
    // TOKEN_IS_NULL already defined above (60001)
    CUSTOMER_ID_DOES_NOT_EXIST(60900, "customerID doesn't exist", "The specified customer ID does not exist"),
    REQUIRED_FIELDS_MISSING(60901, "required fields are missing", "One or more required fields are missing"),
    
    // gc/newEndSession specific errors (61000-61099)
    // TOKEN_IS_NULL already defined above (60001)
    // REQUIRED_FIELDS_MISSING already defined above (60901)
    
    // Authorization and permission errors (61100-61199)
    POS_CPG_AUTHORIZATION_FAILED(61100, "POS does not have authorization for this CPG", "The POS terminal does not have permission to manage this CPG"),
    OUTLET_CPG_AUTHORIZATION_FAILED(61101, "Outlet does not have authorization for this CPG", "The outlet does not have permission to manage this CPG"),
    
    // General business logic errors (61200-61299)
    CPG_CARD_GENERATION_RULES_DISABLED(61200, "CPG card generation rules are disabled", "Card number generation rules for this CPG are disabled"),
    CPG_PERFORMANCE_LIMIT_EXCEEDED(61201, "CPG performance limit exceeded", "Request exceeds performance limits for this CPG"),
    
    ;

    private final int responseCode;
    private final String responseMessage;
    private final String comment;

    GiftCardResponseCodesEnum(int responseCode, String responseMessage, String comment) {
        this.responseCode = responseCode;
        this.responseMessage = responseMessage;
        this.comment = comment;
    }

    public int getResponseCode() {
        return responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public String getComment() {
        return comment;
    }
}
