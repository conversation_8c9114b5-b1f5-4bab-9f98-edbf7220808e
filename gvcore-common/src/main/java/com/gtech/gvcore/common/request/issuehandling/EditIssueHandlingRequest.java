package com.gtech.gvcore.common.request.issuehandling;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月15日
 */
@Data
public class EditIssueHandlingRequest {
    
    @ApiModelProperty(value = "issueHandlingCode", required = true)
    @NotEmpty(message = "issueHandlingCode can not be empty")
    private String issueHandlingCode;

    @ApiModelProperty(value = "uploadedFileName", required = true)
    @NotEmpty(message = "uploadedFileName can not be empty")
    @Length(max = 200)
    private String uploadedFileName;

    @ApiModelProperty(value = "uploadedFileUrl", required = true)
    @NotEmpty(message = "uploadedFileUrl can not be empty")
    @Length(max = 500)
    private String uploadedFileUrl;

    @ApiModelProperty(value = "remarks", required = false)
    @Length(max = 400)
    private String remarks;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

    @ApiModelProperty(value = "proofFileList", required = false)
    @Valid
    @Size(max = 200, message = "The number of proof file cannot be greater than 200")
    @NotEmpty(message = "proofFileList can not be empty")
    private List<ProofFile> proofFileList;

}
