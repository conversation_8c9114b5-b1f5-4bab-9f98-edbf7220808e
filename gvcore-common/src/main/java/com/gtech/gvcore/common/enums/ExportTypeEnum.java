package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/16 16:39
 */

public enum ExportTypeEnum {
    EXCEL_TYPE("EXCEL", "EXCEL", ".xlsx"), PDF_TYPE("PDF", "PDF", ".pdf");

    private final String type;
    private final String desc;
	private final String suffix;

	ExportTypeEnum(String type, String desc, String suffix) {
        this.type = type;
        this.desc = desc;
		this.suffix = suffix;
    }


    public String getDesc() {
        return desc;
    }

	public String getType() {
        return type;
    }

	public String getSuffix() {
		return suffix;
	}

	public static String getSuffix(String type) {
		for (ExportTypeEnum value : ExportTypeEnum.values()) {
			if (value.getType().equals(type)) {
				return value.getSuffix();
			}
		}
		return null;
	}
}
