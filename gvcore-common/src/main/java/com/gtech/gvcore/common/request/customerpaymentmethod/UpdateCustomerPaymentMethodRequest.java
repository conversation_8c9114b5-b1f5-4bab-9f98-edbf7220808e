package com.gtech.gvcore.common.request.customerpaymentmethod;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateCustomerPaymentMethodRequest")
public class UpdateCustomerPaymentMethodRequest {

    @ApiModelProperty(value = "Customer payment method code", example = "112233",required = true)
    @NotEmpty(message = "customerPaymentMethodCode can not be empty")
    @Length(max = 100)
    private String customerPaymentMethodCode;

    @ApiModelProperty(value = "Customer code", example = "112233")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Mop group", example = "1")
    @Length(max = 100)
    private String mopGroup;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 100)
    private String updateUser;

}
