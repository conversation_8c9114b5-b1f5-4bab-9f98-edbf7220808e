package com.gtech.gvcore.common.request.meansofpayment;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@Data
public class UpdateMeansOfPaymentRequest {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id can not be null")
    private Long id;

    @ApiModelProperty(value = "mopName", required = true)
    @NotEmpty(message = "mopName can not be empty")
    @Length(max = 100)
    private String mopName;

    @ApiModelProperty(value = "mopGroup", required = true)
    @NotEmpty(message = "mopGroup can not be empty")
    @Length(max = 100)
    private String mopGroup;

    @ApiModelProperty(value = "remarks", required = false)
    @Length(max = 500)
    private String remarks;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

    @ApiModelProperty(value = "outletCodeList", required = true)
    @NotEmpty(message = "outletCodeList can not be empty")
    private List<String> outletCodeList;

}
