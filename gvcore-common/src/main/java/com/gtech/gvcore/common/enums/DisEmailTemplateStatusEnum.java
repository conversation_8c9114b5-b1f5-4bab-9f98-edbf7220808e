package com.gtech.gvcore.common.enums;

/**
 * @ClassName DisEmailTemplateStatusEnum
 * @Description 分发邮件模板状态
 * <AUTHOR>
 * @Date 2022/7/5 14:52
 * @Version V1.0
 **/
public enum DisEmailTemplateStatusEnum implements IEnum<Integer> {

    INVALID(0, "invalid"),
    VALID(1, "valid"),
    ;

    public static final String REMARK = "分发邮件模板状态: 0-invalid , 1-valid";

    DisEmailTemplateStatusEnum(final int code, final String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;

    public static DisEmailTemplateStatusEnum valueOfCode(final Integer status) {

        for (DisEmailTemplateStatusEnum anEnum : DisEmailTemplateStatusEnum.values()) {
            if (anEnum.equalsCode(status)) {
                return anEnum;
            }
        }

        return null;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(final Integer code) {
        return this.code.equals(code);
    }
}
