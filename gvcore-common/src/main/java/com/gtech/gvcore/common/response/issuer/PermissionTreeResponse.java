package com.gtech.gvcore.common.response.issuer;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "PermissionTreeResponse")
public class PermissionTreeResponse {


	@ApiModelProperty(value = "code.", example = "1345566")
	private String code;

	@ApiModelProperty(value = "name.", example = "PT AGUNG MANDIRI LESTARI")
	private String name;

	@ApiModelProperty(value = "type.", example = "1:issuer, 2:company,3:merchant,4:outlet")
	private Integer type;

	@ApiModelProperty(value = "Child List.")
	private List<PermissionTreeResponse> childList;

}
