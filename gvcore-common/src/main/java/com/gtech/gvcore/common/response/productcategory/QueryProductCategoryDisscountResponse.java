package com.gtech.gvcore.common.response.productcategory;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月24日
 */
@Data
public class QueryProductCategoryDisscountResponse {

    private String productCategoryCode;

    private String categoryName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validFrom;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validUpto;

    private Integer status;

    private List<ProductCategoryDisscountDetailVo> disscountDetailList;

}
