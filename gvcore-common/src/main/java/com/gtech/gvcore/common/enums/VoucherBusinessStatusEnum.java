package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @date 2022年6月7日
 */
public enum VoucherBusinessStatusEnum implements IEnum<String> {
    CREATED("Created", "Created"),
    ACTIVATED("Activated", "Activated");

    private final String code;

    private final String desc;

    VoucherBusinessStatusEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
