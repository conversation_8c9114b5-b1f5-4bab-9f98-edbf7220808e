package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/10 17:43
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("Bulk get voucher request request")
public class BulkGetVoucherRequestRequest {

    @ApiModelProperty(value = "Bulk voucher request code", required = true)
    @NotNull(message = "VoucherRequestCodes cannot be empty")
    private List<GetVoucherRequestRequest> voucherRequestCodeList;

}
