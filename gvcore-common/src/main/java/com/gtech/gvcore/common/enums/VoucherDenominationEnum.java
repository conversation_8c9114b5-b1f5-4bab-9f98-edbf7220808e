package com.gtech.gvcore.common.enums;

import java.math.BigDecimal;

public enum VoucherDenominationEnum {

    /**
     * 0-无限定
     * 1-50k
     * 2-100k
     * 3-500k
     * 4-1000k
     */
    UNLIMITED(new BigDecimal("50000"), "Unlimited"),
    DENOMINATION_50K(new BigDecimal("50000"), "50K"),
    DENOMINATION_100K(new BigDecimal("100000"), "100K"),
    DENOMINATION_500K(new BigDecimal("500000"), "500K"),
    DENOMINATION_1000K(new BigDecimal("1000000"), "1000K");

    private final BigDecimal code;

    private final String desc;

    VoucherDenominationEnum(BigDecimal code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    public BigDecimal code() {
        return this.code;
    }

    public String desc() {
        return this.desc;
    }

    public boolean equalsCode(BigDecimal code) {
        return this.code.compareTo(code) == 0;
    }



}
