package com.gtech.gvcore.common.response.cpg;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/26 16:17
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Query vpg by outlet code")
public class QueryCpgByOutletCodeResponse {

    private String cpgName;

    private String cpgCode;

    private BigDecimal denomination;
}
