package com.gtech.gvcore.common.response.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CpgInventoryDetailResponse
 * @Description VPG库存详情
 * <AUTHOR>
 * @Date 2022/7/6 10:40
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "CpgInventoryDetailResponse")
public class CpgInventoryDetailResponse {

    @ApiModelProperty(value = "VPG库存信息列表")
    private List<CpgInventoryResponse> cpgInventoryList;

    @ApiModelProperty(value = "总金额", example = "100")
    private BigDecimal amount;

    @ApiModelProperty(value = "总库存数", example = "100")
    private Integer quantity;

    @ApiModelProperty(value = "可用库存数", example = "80")
    private Integer available;

    @ApiModelProperty(value = "已分发数", example = "20")
    private Integer distributed;

}
