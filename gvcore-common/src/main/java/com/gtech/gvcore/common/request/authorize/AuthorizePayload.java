package com.gtech.gvcore.common.request.authorize;

import com.gtech.commons.utils.StringUtil;
import lombok.Data;
import org.apache.commons.lang3.RandomUtils;

@Data
public class AuthorizePayload {

	private String merchantOutletName;
	private String acquirerId;
	private String organizationName;
	private String posEntryMode;
	private String posTypeId;
	private String posName;
	private String termAppVersion;
	private String currentBatchNumber;
	private String terminalId;
	private String userName;
	private String password;
	private String forwardingEntityId;
	private String forwardingEntityPassword;

	private String authToken;

	public Integer getResponseBatchId(Integer index){
		if (StringUtil.isNotBlank(currentBatchNumber)){
			return Integer.valueOf(currentBatchNumber.substring(currentBatchNumber.length() - index));
		}
		return RandomUtils.nextInt(0, 9999);
	}


}
