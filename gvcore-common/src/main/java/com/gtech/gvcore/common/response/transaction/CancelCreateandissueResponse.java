package com.gtech.gvcore.common.response.transaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/24 16:21
 */
@Data
public class CancelCreateandissueResponse {


    private Long currentBatchNumber;
    private Integer transactionId;
    private String trackData;
    private String cardNumber;

    @JsonProperty("CardPIN")
    private String cardPIN;
    private String invoiceNumber;
    private Integer amount;
    private Integer billAmount;
    private String originalInvoiceNumber;
    private Integer originalTransactionId;
    private Integer originalBatchNumber;
    private String originalApprovalCode;
    private String originalAmount;
    private String approvalCode;
    private String addonCardNumber;
    private String addonCardTrackData;
    private String transferCardNumber;
    private String merchantName;
    private Integer adjustmentAmount;
    private Date cardExpiry;
    private String originalCardNumber;
    private String originalCardPin;
    private String cardProgramID;
    private String corporateName;
    private String notes;
    private Date settlementDate;
    private Date expiry;
    private String purchaseOrderNumber;
    private Integer purchaseOrderValue;
    private Integer discountPercentage;
    private Integer discountAmount;
    private Integer paymentMode;
    private String paymentDetails;
    private Boolean bulkType;
    private String externalCorporateId;
    private String externalCardNumber;
    private Integer responseCode;
    private String responseMessage;
    private String merchantOutletName;
    private String merchantOutletAddress1;
    private String merchantOutletAddress2;
    private String merchantOutletCity;
    private String merchantOutletState;
    private String merchantOutletPinCode;
    private String merchantOutletPhone;
    private String maskCard;
    private String printMerchantCopy;
    private String invoiceNumberMandatory;
    private String numericUserPwd;
    private String integerAmount;
    private String culture;
    private String currencySymbol;
    private String currencyPosition;
    private String currencyDecimalDigits;
    private String displayUnitForPoints;
    private String receiptFooterLine1;
    private String receiptFooterLine2;
    private String receiptFooterLine3;
    private String receiptFooterLine4;
    private Boolean result;
    private Date transferCardExpiry;
    private Integer transferCardBalance;
    private Integer cardStatusId;
    private String cardStatus;
    private String cardCurrencySymbol;
    private Date activationDate;
    private String SVRecentTransactions;
    private String cardType;
    private String transferCardTrackData;
    private String transferCardPin;
    private Integer cumulativeAmountSpent;
    private Integer currencyConversionRate;
    private Integer currencyConvertedAmount;
    private String cardHolderName;
    private Integer storedValueUnitID;
    private Integer xactionAmountConvertedValue;
    private Integer storedValueConvertedAmount;
    private Integer promotionalValue;
    private Integer earnedValue;
    private Integer transactionAmount;
    private Integer previousBalance;
    private String upgradedCardProgramGroupName;
    private Integer newBatchNumber;
    private Integer originalActivationAmount;
    private String cardCreationType;
    private String errorCode;
    private String errorDescription;
    private String cardProgramGroupType;
    private String activationCode;
    private String activationURL;
    @JsonProperty("MerchantID")
    private Integer merchantID;
    private Integer reloadableAmount;
    private String barcode;
    private String customer;
    private String transactionType;

}
