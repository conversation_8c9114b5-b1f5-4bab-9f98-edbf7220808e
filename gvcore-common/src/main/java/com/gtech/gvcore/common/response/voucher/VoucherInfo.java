package com.gtech.gvcore.common.response.voucher;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/29 11:23
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VoucherInfo {


    private String voucherBatchCode;

    private String issuerCode;

    private String bookletCode;

    private Long bookletCodeNum;

    private String voucherCode;

    private Long voucherCodeNum;

    private String cpgCode;

    private String cpgName;

    private String mopCode;

    private BigDecimal denomination;

    private String voucherPin;

    private String voucherBarcode;

    private Date voucherEffectiveDate;

    private String cardCreationType;

    private Integer status;

    //启动禁用
    private Integer voucherStatus;

    private Integer circulationStatus;

    private String voucherActiveCode;

    private String voucherActiveUrl;

    private String voucherOwnerCode;

    private String voucherOwnerType;

    private Date voucherUsedTime;

    private String productCode;

}
