package com.gtech.gvcore.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 非常规请求日志标记
 * 用于声明禁止记录的请求日志参数
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface NotSysLogger {

    /**
     * 禁止记录日志
     * @return
     */
    boolean disableLogger() default false;

    /**
     * 不记录响应
     * @return
     */
    boolean notResponseLogger() default false;

    /**
     * 不记录请求
     * @return
     */
    boolean notRequestLogger() default false;

    /**
     * 不记录请求地址
     * @return
     */
    boolean notRequestPath() default false;

    /**
     * 不记录请求人
     * @return
     */
    boolean notUser() default false;

}
