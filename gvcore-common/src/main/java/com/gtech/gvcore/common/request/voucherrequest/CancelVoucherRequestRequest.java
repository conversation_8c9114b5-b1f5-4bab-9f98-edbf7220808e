package com.gtech.gvcore.common.request.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/10 17:30
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Cancel voucher request details")
@Builder
public class CancelVoucherRequestRequest implements Serializable {
    private static final long serialVersionUID = 1878844660237156247L;
    @NotBlank(message = "The canceler cannot do without")
    @ApiModelProperty(value = "Cancel user")
    private String cancelUser;

    @NotNull(message = "The request voucherRequestCode cannot be empty")
    @ApiModelProperty(value = "voucherRequestCode of the request that you want to cancel", example = "REQ2200006", required = true)
    private String voucherRequestCode;
}
