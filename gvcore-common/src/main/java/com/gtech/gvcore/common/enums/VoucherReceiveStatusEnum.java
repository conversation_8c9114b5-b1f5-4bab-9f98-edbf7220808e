package com.gtech.gvcore.common.enums;

public enum VoucherReceiveStatusEnum {

	PROCESSING(0, "Processing"),
	COMPLETED(1, "Completed");
	
    private final int code;

    private final String desc;

    VoucherReceiveStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
