package com.gtech.gvcore.common.request.issuehandling;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月19日
 */
@Data
public class IssueHandlingCancelRequest {
    
    @ApiModelProperty(value = "issueHandlingCode", required = true)
    @NotEmpty(message = "issueHandlingCode can not be empty")
    private String issueHandlingCode;
    
    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}


