package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Query Card Details Response
 */
@Data
@ApiModel(value = "QueryCardDetailsResponse", description = "Query Card Details Response")
public class QueryCardDetailsResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Notes", notes = "Reference text information")
    private String notes;

    @ApiModelProperty(value = "Retry Key", notes = "Reference number for the request")
    private String retryKey;

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number")
    private String giftCardNumber;

    @ApiModelProperty(value = "Card Holder Name", notes = "Gift card holder name")
    private String cardHolderName;

    @ApiModelProperty(value = "Card Holder Email", notes = "Gift card holder email")
    private String cardHolderEmail;

    @ApiModelProperty(value = "Card Holder Address", notes = "Gift card holder address")
    private String cardHolderAddress;

    @ApiModelProperty(value = "Remaining Balance", notes = "Gift card remaining balance")
    private BigDecimal remainingBalance;

    @ApiModelProperty(value = "Denomination", notes = "Initial gift card denomination when purchased")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Gift Card Status", notes = "Current gift card status")
    private String giftCardStatus;

    @ApiModelProperty(value = "Currency", notes = "Currency code")
    private String currency;

    @ApiModelProperty(value = "Activation Date", notes = "Gift card activation date")
    private Date activationDate;

    @ApiModelProperty(value = "Expiry Date", notes = "Gift card expiry date")
    private Date expiryDate;

    @ApiModelProperty(value = "GCPG", notes = "GCPG name")
    private String gcpg;

    @ApiModelProperty(value = "Issuer", notes = "Issuer")
    private String issuer;

}