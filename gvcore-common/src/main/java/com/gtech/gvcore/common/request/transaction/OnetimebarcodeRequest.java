package com.gtech.gvcore.common.request.transaction;

import java.util.List;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OnetimebarcodeRequest {

	@ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch.")
	private Integer transactionId;
	@ApiModelProperty(value = "Cards. ", example = "123456", required = true, notes = "Array of Object APICard • Only Card Number in this Object is mandatory. • Card Number: Contains the Virtual CardNumber for which OneTimeBarcode has to be generated. • As of now only one card is supported. If more than one card is passed “Invalid request” error will be thrown.")
	private List<CardInfo> cards;
	@ApiModelProperty(value = "Merchant Name. ", example = "123456", notes = "Merchant Name Note: As of now this field is just for capture only, and no validation is being performed.")
	@Length(max = 256, message = "Merchant Name maximum length 256")
	private String merchantName;
	@ApiModelProperty(value = "Outlet Name. ", example = "123456", notes = "A unique identifier which represents the name of the outlet. This is unique for the merchant")
	@Length(max = 100, message = "Outlet Name maximum length 100")
	private String outletName;
	@ApiModelProperty(value = "Notes. ", example = "0", notes = "Any Reference text to be captured along with this transaction.")
	@Length(max = 512, message = "Notes maximum length 512")
	private String notes;
	@ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "The datetime at the client machine in YYYY-MM-DD HH:MM:SS")
	private String dateAtClient;


	private String terminalId;
	private String batchId;

}
