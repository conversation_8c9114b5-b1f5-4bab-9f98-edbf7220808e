package com.gtech.gvcore.common.enums;

import com.gtech.commons.code.IEnum;

/**
 * @ClassName OutletTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/2/3 16:19
 * @Version V1.0
 **/
public enum OutletTypeEnum implements IEnum {

    WAREHOUSE("Warehouse", "Warehouse"),
    MV_STORE("MVStore", "MV Store"),
    OFFLINE("Offline", "Offline"),
    INDEPENDET("Independet", "Independet"),

    ;

    public static final String REMARK = "";

    OutletTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //code
    private final String code;

    //desc
    private final String desc;

    public static OutletTypeEnum valueOfCode(String code) {
        for (OutletTypeEnum value : OutletTypeEnum.values()) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
