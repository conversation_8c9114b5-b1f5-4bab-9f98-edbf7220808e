/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.common.request.useraccount;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GvQueryUserAccountsCondition {

    // Domain code.
    private String domainCode;

    // Tenant code.
    private String tenantCode;

    // Organization code.
    private String orgCode;

    // Role code.
    private String roleCode;
    
    private List<String> roleCodeList;

    // Source of user registration
    private String source;

    // User account status. (0-invalid 1-valid 2-disabled by system 3-disabled by admin)
    private Integer status;

    private String account;

    private String userCode;

    private String mobile;

    private String email;

	private String fullName;

	private String issuerCode;
}