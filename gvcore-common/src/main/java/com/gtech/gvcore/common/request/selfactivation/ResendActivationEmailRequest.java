package com.gtech.gvcore.common.request.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * Request for admin to resend activation email
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ResendActivationEmailRequest", description = "Request for admin to resend activation email")
public class ResendActivationEmailRequest {
    
    @NotBlank(message = "Customer order code cannot be blank")
    @ApiModelProperty(value = "Customer order code", required = true, example = "CUST-ORD-12345")
    private String customerOrderCode;
    
    @Email(message = "Invalid email format")
    @ApiModelProperty(value = "New email address (optional)", example = "<EMAIL>")
    private String newEmail;
}
