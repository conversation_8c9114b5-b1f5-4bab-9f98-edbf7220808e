package com.gtech.gvcore.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @Date 2021/7/13 14:09
 */
@Slf4j
@Component
public class VoucherShardingConfig implements PreciseShardingAlgorithm<String> {


    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<String> preciseShardingValue) {
        String value = preciseShardingValue.getValue();
        if (StringUtils.isNotBlank(value)) {
            //不去除字母的情况下转为全数字
            value = value.replaceAll("[a-zA-Z]", "");
            Long x = Long.valueOf(value) % collection.size();
            int i = x.intValue(); // NOSONAR
            return collection.toArray()[i].toString();
        } else {

            throw new IllegalArgumentException();
        }
    }






    /**
     *  String value = preciseShardingValue.getValue();
     *         if (StringUtils.isNotBlank(value)) {
     *
     *             //获取面额+年份
     *             String substring = value.substring(3, 6);
     *
     *             for (String s : collection) {
     *                 if (s.contains(substring)){
     *                     return s;
     *                 }
     *             }
     *             throw new IllegalArgumentException();
     *
     *         } else {
     *             throw new IllegalArgumentException();
     *         }
     */


    /*// 重写doSharding方法
    // 返回值: 分片后的N张表后缀
    // 参数availableTargetNames: 所有可用的表名
    // 参数shardingValue: 包含逻辑表名, 以及解析查询后的列名和对应值map
    @Override
    public Collection<String> doSharding(Collection availableTargetNames, ComplexKeysShardingValue shardingValue) {
        List<String> shardingResults = Lists.newArrayList();
        // 将查询时带的条件map拿出来
        Map<String, Collection<String>> columnNameAndShardingValuesMap = shardingValue.getColumnNameAndShardingValuesMap();
        // 判断查询条件带不带ShardingColumnEnum中定义的列, 不带分片键的查询统一报错
        if (!validateAndPopulateShardingValueList(shardingResults, availableTargetNames, columnNameAndShardingValuesMap)) {
            log.error("invalid params in shardingResults:{}", shardingValue);
            throw new IllegalArgumentException("invalid params in shardingResults!");
        }
        return shardingResults;
    }

    private boolean validateAndPopulateShardingValueList(List<String> shardingResults, Collection<String> tableNames, Map<String, Collection<String>> columnNameAndShardingValuesMap) {
        return Arrays.stream(ShardingColumnEnum.values())
                .map(ShardingColumnEnum::getCode)
                .anyMatch(v -> {
                    Collection<String> shardingValues = columnNameAndShardingValuesMap.get(v);
                    if (CollectionUtils.isEmpty(shardingValues) || shardingValues.contains("")) {
                        return false;
                    }
                    return shardingValues.stream()
                            .anyMatch(shardingValue -> tableNames.stream()
                                    .anyMatch(tableName -> {
                                        if (tableNameEndsWithShardingValueIgnoreCase(tableName, shardingValue)) {
                                            shardingResults.add(tableName);
                                            return true;
                                        }
                                        return false;
                                    }));
                });
    }

    private boolean tableNameEndsWithShardingValueIgnoreCase(String tableName, String shardingValue) {

        return Optional.ofNullable(tableName)
                .map(String::toLowerCase)
                .map(t -> t.split("_"))
                .map(split -> split[split.length - 1].startsWith(shardingValue.substring(3,6)))
                .orElse(false);
    }*/

}
