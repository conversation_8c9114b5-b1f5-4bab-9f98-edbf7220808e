package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/9/7 14:11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RedemptionProportionResponse")
public class RedemptionProportionResponse {



    private String name;

    private BigDecimal amount;




}
