package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Map;

@Data
public class Payment {
    @ApiModelProperty(value = "Payment Mode Id. ", required = true, example = "1", notes = "550 CHECK 551 CREDIT CARD 552 DEBIT CARD 553 CASH 554 DEMAND DRAFT 555 CREDIT SALE 556 OTHERS")
    private Integer paymentModeId;
    @ApiModelProperty(value = "Payment Details. ", required = true, example = "1", notes = "Payment details")
    @Length(max = 1000, message = "Payment Details maximum length 1000")
    private String paymentDetails;


    public Map<String,Object> toMap(){
        java.util.Map<String,Object> map = new java.util.HashMap<>();
        map.put("paymentModeId",paymentModeId);
        map.put("paymentDetails",paymentDetails);
        return map;
    }

}
