package com.gtech.gvcore.common.request.issuehandling;

import java.util.Date;

import com.gtech.gvcore.common.request.base.PageBean;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月6日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryIssueHandlingByPageRequest  extends PageBean {
	
	private String issuerCode;
	
    private String issueHandlingCode;

	private String issueType;
	
	private Integer status;
	
	private Date createTimeStart;
	
	private Date createTimeEnd;
	
}


