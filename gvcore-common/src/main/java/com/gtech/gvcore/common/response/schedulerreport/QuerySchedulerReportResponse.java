package com.gtech.gvcore.common.response.schedulerreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 17:50
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Query scheduler report")
public class QuerySchedulerReportResponse implements Serializable {

    private static final long serialVersionUID = -5088348514521401392L;
    @ApiModelProperty(value = "Scheduler report code")
    private String schedulerReportCode;

    @ApiModelProperty(value = "Scheduler name")
    private String schedulerName;

    @ApiModelProperty(value = "Execution time")
    private Date executionTime;

    @ApiModelProperty(value = "Execution frequency,optional value is once/daily/weekly/monthly/or/yearly", example = "daily")
    private String frequency;

    @ApiModelProperty(value = "Every _ Day(s),Every _ Week(s),Every _ Month(s) or Every _ Yearly")
    private Integer every;

    @ApiModelProperty(value = "A list of selected dates,If frequency by weekly SUNDAY = 1;MONDAY = 2;TUESDAY = 3;WEDNESDAY = 4;THURSDAY = 5;FRIDAY = 6;SATURDAY = 7;")
    private Integer[] days;

    @ApiModelProperty(value = "cron expression，The schedulerCron of frequency is not once")
    private String schedulerCron;

    @ApiModelProperty(value = "Repeat End,The value can be never, after, or on date. The default value is never")
    private String repeatEnd;

    @ApiModelProperty(value = "repeat end after")
	private Integer repeatEndAfter;

    @ApiModelProperty(value = "repeat end On date")
	private Date repeatEndTime;

    @ApiModelProperty(value = "Report name")
    private String reportName;

    @ApiModelProperty(value = "Report type")
    private Integer reportType;

    @ApiModelProperty(value = "Issuer code")
    private String issuerCode;

    @ApiModelProperty(value = "Merchant code")
    private String merchantCode;

    @ApiModelProperty(value = "Merchant outlet code")
    private String merchantOutletCode;

    @ApiModelProperty(value = "Transaction type")
    private String transactionType;

    @ApiModelProperty(value = "Data range", example = "yesterday(1), last 7 days(7), last 30 days(30)")
    private Integer dataRange;

    @ApiModelProperty(value = "Transaction Status")
    private String transactionStatus;

    @ApiModelProperty(value = "Voucher status")
    private Integer voucherStatus;

    @ApiModelProperty(value = "Voucher program group code")
    private String vpgCode;

    @ApiModelProperty(value = "Email(s),you can enter multiple use ','", example = "<EMAIL>,<EMAIL>")
    private String emails;

    @ApiModelProperty(value = "Email subject")
    private String emailSubject;

    @ApiModelProperty(value = "FTP address")
    private String ftpAddress;

    @ApiModelProperty(value = "Login method")
    private String loginMethod;

    @ApiModelProperty(value = "Encryption key")
    private String encryptionKey;

    @ApiModelProperty(value = "Status")
    private Boolean status;

    @ApiModelProperty(value = "Update time")
    private Date updateTime;

    @ApiModelProperty(value = "Last execution time")
    private Date lastExecutionTime;

    @ApiModelProperty(value = "username")
    private String ftpUsername;

    @ApiModelProperty(value = "password")
    private String ftpPassword;

}
