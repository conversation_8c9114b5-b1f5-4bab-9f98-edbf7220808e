package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/11 11:44
 */
public enum SystemVoucherStatusEnum {

    /**
     *  VOUCHER_NEWLY_GENERATED = 0;
     *  VOUCHER_ACTIVATED = 1;
     *  VOUCHER_USED = 2;
     *  VOUCHER_CANCELLED = 3;
     *  VOUCHER_EXPIRED = 4;
     */

    VOUCHER_NEWLY_GENERATED(0, "CREATED"),
    VOUCHER_ACTIVATED(1, "ACTIVATED"),
    VOUCHER_USED(2, "REDEEMED"),
    VOUCHER_CANCELLED(3, "CANCELLED"),
    VOUCHER_EXPIRED(4, "EXPIRED");

    private final Integer code;

    private final String desc;

    SystemVoucherStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public boolean equalsCode(Integer code) {

        return null != code && code.equals(this.code);
    }

    public static SystemVoucherStatusEnum valueOfCode (Integer code) {
        for(SystemVoucherStatusEnum flowNodeEnum : SystemVoucherStatusEnum.values()) {
            if (flowNodeEnum.getCode().equals(code) ) {
                return flowNodeEnum;
            }
        }
        return null;
    }

    public static String getByCode(Integer code) {
        for(SystemVoucherStatusEnum flowNodeEunm : SystemVoucherStatusEnum.values()) {
            if (flowNodeEunm.getCode().equals(code)) {
                return flowNodeEunm.getDesc();
            }
        }
        return null;
    }

}
