package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/22 16:09
 */

@Data
@NoArgsConstructor
@ApiModel(value = "Submit customer order request")
public class SubmitCustomerOrderRequest implements Serializable {
    private static final long serialVersionUID = -2849738734253238374L;
    @ApiModelProperty(value = "Customer order code", required = true)
    @NotBlank(message = "Customer order code cannot be empty")
    private String customerOrderCode;
    @ApiModelProperty(value = "User code", required = true)
    @NotBlank(message = "User code cannot be empty")
    private String userCode;
    @NotBlank(message = "Role code cannot be empty")
    @ApiModelProperty(value = "Role code")
    private String roleCode;


}
