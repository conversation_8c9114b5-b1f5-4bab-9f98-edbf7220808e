package com.gtech.gvcore.common.request.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * Request for getting activation page information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetActivationInfoRequest", description = "Request to get activation page information")
public class GetActivationInfoRequest {
    
    @NotBlank(message = "Token cannot be blank")
    @ApiModelProperty(value = "Activation token", required = true, example = "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8")
    private String token;
}
