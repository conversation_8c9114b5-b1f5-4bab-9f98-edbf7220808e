package com.gtech.gvcore.common.response.cardnumberconfig;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CardNumberResponse {

    @ApiModelProperty(value = "configCode")
    private String configCode;
    @ApiModelProperty(value = "type")
    private String type;
    @ApiModelProperty(value = "description")
    private String description;
    @ApiModelProperty(value = "code")
    private String code;
    @ApiModelProperty(value = "remark")
    private String remark;
    @ApiModelProperty(value = "status")
    private String status;
    @ApiModelProperty(value = "denomination")
    private String denomination;
}
