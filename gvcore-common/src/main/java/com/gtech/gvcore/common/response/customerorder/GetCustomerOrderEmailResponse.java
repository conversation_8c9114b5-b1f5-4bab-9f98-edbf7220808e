package com.gtech.gvcore.common.response.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@ApiModel(value = "Get customer order email response")
@Accessors(chain = true)
public class GetCustomerOrderEmailResponse {

    @ApiModelProperty(value = "email code", example = "OCE9928212313")
    private String emailCode;

    @ApiModelProperty(value = "email address", example = "<EMAIL>")
    private String emailAddress;

    @ApiModelProperty(value = "send status (1/0)", example = "1")
    private Integer sendStatus;

    @ApiModelProperty(value = "send status Desc", example = "Succeeded")
    public String getSendStatusDesc() {
        return sendStatus != null && sendStatus == 1 ? "Succeeded" : "Fail";
    }

    @ApiModelProperty(value = "Customer order detail code", example = "COD102203201832000003")
    private Date sendTime;

}
