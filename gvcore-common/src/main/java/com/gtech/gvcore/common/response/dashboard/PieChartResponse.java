package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/8/31 16:27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PieChartResponse")
public class PieChartResponse {


    @ApiModelProperty(value = "Name")
    private String name;


    @ApiModelProperty(value = "Amount")
    private BigDecimal amount;






}
