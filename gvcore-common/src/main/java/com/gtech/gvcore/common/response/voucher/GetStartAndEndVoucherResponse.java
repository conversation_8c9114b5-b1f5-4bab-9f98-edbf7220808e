package com.gtech.gvcore.common.response.voucher;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "GetStartAndEndVoucherResponse")
public class GetStartAndEndVoucherResponse {
	
    @ApiModelProperty(value = "Voucher start code.",notes ="Voucher start code", example = "1002222220000001")
    private String voucherStartNo;

    @ApiModelProperty(value = "Voucher end code.",notes ="Voucher end code. ", example = "1002222220003000")
    private String voucherEndNo;
    
    @ApiModelProperty(value = "Denomination.",notes ="Denomination.", example = "50000")
    private BigDecimal denomination;
    
    @ApiModelProperty(value = "Voucher num.",notes ="Voucher num.", example = "10")
    private Long voucherNum;
    
    @ApiModelProperty(value = "Total amount.",notes ="amount.", example = "500000")
    private BigDecimal amount;


    private String responseMessage;
    
}
