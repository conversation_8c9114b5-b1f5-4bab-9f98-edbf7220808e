package com.gtech.gvcore.common.request.voucherrequest;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Create voucher request details")
@Builder
public class CreateVoucherRequestDetailsRequest implements Serializable {

    private static final long serialVersionUID = 3838530940034738165L;
    @NotNull(message = "denomination can't be empty")
    @ApiModelProperty(required = true, value = "Denomination voucher：50000/100000/500000/1000000", example = "50000")
    private BigDecimal denomination;
    @ApiModelProperty(value = "voucherRequestDetailsCode", example = "VRD102204121713000184")
    private String voucherRequestDetailsCode;
    @NotNull(message = "voucherNum can't be empty")
    @ApiModelProperty(value = "Number of Vouchers", required = true, example = "2000")
    private Integer voucherNum;
    @NotNull(message = "voucherAmount can't be empty")
    @ApiModelProperty(value = "Voucher Amount", required = true, example = "100000000")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Voucher program group code")
    private String cpgCode;
    @ApiModelProperty(value = "Voucher program group name")
    private String vpgName;

}
