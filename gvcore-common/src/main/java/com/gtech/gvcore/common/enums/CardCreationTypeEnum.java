package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/29 14:16
 */
public enum CardCreationTypeEnum {


    VIRTUAL_VOUCHER ("0", "Virtual"),
    PHYSICAL_VOUCHER("1", "Physical");
    private final String code;

    private final String desc;

    CardCreationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }



}
