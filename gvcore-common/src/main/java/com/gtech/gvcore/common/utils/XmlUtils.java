package com.gtech.gvcore.common.utils;

import java.io.StringWriter;
import java.nio.charset.StandardCharsets;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022年6月13日
 */
@Slf4j
public class XmlUtils {

    private XmlUtils() {

    }

    private static DocumentBuilderFactory documentBuilderFactory = null;

    private static DocumentBuilder defaultDocumentBuilder = null;

    /**
     * 
     * <AUTHOR>
     * @return
     * @date 2022年6月13日
     */
    private static DocumentBuilderFactory getDocumentBuilderFactory() {

        if (documentBuilderFactory == null) {
            documentBuilderFactory = DocumentBuilderFactory.newInstance();// NOSONAR
            documentBuilderFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, ""); // Compliant
            documentBuilderFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_SCHEMA, ""); // compliant
            documentBuilderFactory.setExpandEntityReferences(false);
        }
        return documentBuilderFactory;
    }

    /**
     * 
     * <AUTHOR>
     * @return
     * @throws ParserConfigurationException
     * @date 2022年6月13日
     */
    private static DocumentBuilder getDefaultDocumentBuilder() throws ParserConfigurationException {

        if (defaultDocumentBuilder == null) {
            defaultDocumentBuilder = getDocumentBuilderFactory().newDocumentBuilder();
        }
        return defaultDocumentBuilder;
    }

    /**
     * 
     * <AUTHOR>
     * @return
     * @throws ParserConfigurationException
     * @date 2022年6月13日
     */
    public static Document newDocument() throws ParserConfigurationException {

        return getDefaultDocumentBuilder().newDocument();
    }

    /**
     * 
     * <AUTHOR>
     * @param document
     * @param elementName
     * @return
     * @date 2022年6月13日
     */
    public static Element createElement(Document document, String elementName) {

        return document.createElement(elementName);
    }

    /**
     * 
     * <AUTHOR>
     * @param document
     * @param elementName
     * @param textContent
     * @return
     * @date 2022年6月13日
     */
    public static Element createElement(Document document, String elementName, String textContent) {

        Element element = document.createElement(elementName);
        element.setTextContent(textContent);
        return element;
    }

    /**
     * 
     * <AUTHOR>
     * @param document
     * @return
     * @date 2022年6月13日
     */
    public static String documentToString(Document document) {

        try {
            TransformerFactory tf = TransformerFactory.newInstance();
            tf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.ENCODING, StandardCharsets.UTF_8.name());
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");

            DOMSource source = new DOMSource(document);
            StringWriter writer = new StringWriter();
            StreamResult result = new StreamResult(writer);
            transformer.transform(source, result);
            String output = writer.getBuffer().toString();
            writer.close();
            return output;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

}
