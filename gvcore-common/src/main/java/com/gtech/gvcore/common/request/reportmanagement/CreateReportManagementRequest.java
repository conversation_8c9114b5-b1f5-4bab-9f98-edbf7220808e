package com.gtech.gvcore.common.request.reportmanagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/11 16:55
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "CreateReportManagementRequest", description = "Click Assign Access to operate, and finally save an array")
public class CreateReportManagementRequest implements Serializable {

    private static final long serialVersionUID = 9106326119595147620L;
    @ApiModelProperty(value = "Merchant code", required = true, example = "ME102203021435000523")
    @NotBlank(message = "merchantCode cannot be empty")
    private String merchantCode;
    @ApiModelProperty(value = "Report name", example = "Bulk Order Report", required = true)
    @NotBlank(message = "reportName cannot be empty")
    private String reportName;
    @ApiModelProperty(value = "Report type", example = "1", required = true)
    @NotBlank(message = "reportType cannot be empty")
    private Integer reportType;
    @ApiModelProperty(value = "Role code", example = "`admin`", required = true)
    @NotBlank(message = "roleCode cannot be empty")
    private String roleCode;
    @ApiModelProperty(value = "export PDF", example = "true", required = true)
    @NotNull(message = "exportPdf cannot be empty")
    private Boolean exportPdf;
    @ApiModelProperty(value = "export Excel", required = true, example = "false")
    @NotNull(message = "exportExcel cannot be empty")
    private Boolean exportExcel;
    @ApiModelProperty(value = "Create user", example = "createUser", required = true)
    @NotBlank(message = "createUser cannot be empty")
    private String createUser;

}
