package com.gtech.gvcore.common.response.voucherreceive;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VoucherReceiveBatchResponse {

    /**
     * voucher receive batch code
     */
	@ApiModelProperty(value = "Voucher receive batch code")
    private String voucherReceiveBatchCode;

    /**
     * voucher receive code
     */
	@ApiModelProperty(value = "Voucher receive code")
    private String voucherReceiveCode;

    /**
     * cpg code
     */
	@ApiModelProperty(value = "Cpg code")
    private String cpgCode;

	/**
	 * cpg name
	 */
	@ApiModelProperty(value = "Cpg name")
	private String cpgName;

    /**
     * voucher start NO
     */
	@ApiModelProperty(value = "Voucher start NO")
    private String voucherStartNo;

    /**
     * voucher end NO
     */
	@ApiModelProperty(value = "Voucher end NO")
    private String voucherEndNo;

    /**
     * number of vouchers
     */
	@ApiModelProperty(value = "Number of vouchers")
    private Integer voucherNum;

    /**
     * denomination
     */
	@ApiModelProperty(value = "Denomination")
    private BigDecimal denomination;

    /**
     * received number of vouchers
     */
	@ApiModelProperty(value = "Received number of vouchers")
    private Integer receivedNum;

    /**
     * booklet start NO
     */
	@ApiModelProperty(value = "Booklet start NO")
    private String bookletStartNo;

    /**
     * booklet end NO
     */
	@ApiModelProperty(value = "Booklet end NO")
    private String bookletEndNo;

    /**
     * booklet number
     */
	@ApiModelProperty(value = "Booklet number")
    private Integer bookletNum;

    /**
     * create user
     */
	@ApiModelProperty(value = "Create user")
    private String createUser;

    /**
     * create time
     */
	@ApiModelProperty(value = "Create time")
    private Date createTime;

	/**
	 * update user
	 */
	@ApiModelProperty(value = "Update user")
	private String updateUser;

	/**
	 * update time
	 */
	@ApiModelProperty(value = "Update time")
	private Date updateTime;

}