package com.gtech.gvcore.common.response.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class LineItemResponseInfo {

	@ApiModelProperty(value = "Line Item No.", required = true, notes = "Represents line number from the line number list")
	private String lineItemNo;
	@ApiModelProperty(value = "Line Item Status.", required = true, notes = "SUCCESS FAILED")
	private String lineItemStatus;
	@ApiModelProperty(value = "Success Card Count.", required = true, notes = "Total success card count @ line item level")
	private Integer successCardCount;
	@ApiModelProperty(value = "Total Card Count.", required = true, notes = "Total Card Count @ line level")
	private Integer totalCardCount;
	@ApiModelProperty(value = "Design Code.", required = true, notes = "This is an article code")
	private String designCode;
	@ApiModelProperty(value = "Product Code.", required = true, notes = "This is product code")
	private String productCode;

	//PLIPos删除  Inside LineItems object, please remove “CardProgramGroupName” and “ResponseMessage”. >> May this that made yesterday test was error
//	@ApiModelProperty(value = "Card Program Group Name.", required = true, notes = "Card Program Group Name")
//	private String cardProgramGroupName;
//	@ApiModelProperty(value = "Response Message.", required = true, notes = "Success / Failure message")
//	private String responseMessage;



//	@ApiModelProperty(value = "Total Amount.", required = true, notes = "Total success / fail amount")
	private BigDecimal totalAmount;
	@ApiModelProperty(value = "Transaction Amount.", required = true, notes = "Total success / fail amount")
	private BigDecimal transactionAmount;
	@ApiModelProperty(value = "Start Card Number.", required = true, notes = "Start card number in the lineitem")
	private String startCardNumber;
	@ApiModelProperty(value = "End Card Number.", required = true, notes = "End card number in the lineitem")
	private String endCardNumber;
	@ApiModelProperty(value = "Cards.", notes = "If sendOnlyFailedCards is true then only failed cards information in this range will be sent.")
	private List<CardResponse> cards;

}