package com.gtech.gvcore.common.response.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response for requesting OTP
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "RequestOtpResponse", description = "Response for OTP request")
public class RequestOtpResponse {
    
    @ApiModelProperty(value = "Success flag", example = "true")
    private Boolean success;
    
    @ApiModelProperty(value = "Response message", example = "OTP has been sent to your email.")
    private String message;
}
