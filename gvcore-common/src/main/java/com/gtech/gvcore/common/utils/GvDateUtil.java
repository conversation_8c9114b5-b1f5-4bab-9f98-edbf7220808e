package com.gtech.gvcore.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * @ClassName GvDateUtil
 * @Description GV 时间工具类
 * <AUTHOR>
 * @Date 2022/10/19 16:53
 * @Version V1.0
 **/
public class GvDateUtil {

    private GvDateUtil() {
    }

    public static final String FORMAT_US_DATETIME_DD_MMM_YYYY = "dd MMM yyyy";
    public static final String FORMAT_US_DATETIME_DD_MMMM_YYYY = "dd MMMM yyyy";
    public static final String FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_SS = "dd-MM-yyyy HH:mm:ss";
    public static final String FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_A = "dd-MM-yyyy HH:mm a";
    public static final String FORMAT_US_DATETIME_DD_MMM_YY = "dd MMM yy";
    public static final String FORMAT_US_DATETIME_MMM_YYYY = "MMM yyyy";
    public static final String FORMAT_US_DATETIME_DD_MMM_YY_HH_MM_A = "dd MMM yy HH:mm a";
    public static final String FORMAT_US_DATETIME_DD_MM_YY_HH_MM_A = "dd-MM-yy HH:mm a";

    public static final String FORMAT_MM_YY_SLASH = "MM/yy";
    public static final String FORMAT_YYYY_MM_BAR = "yyyy-MM";
    public static final String FORMAT_YYYY_MM_SLASH = "yyyy/MM";
    public static final String FORMAT_DD_MM_YYYY_JOIN_SLASH = "dd/MM/yyyy";
    public static final String FORMAT_HH_MM_SS_JOIN_POINT = "HH.mm.ss";

    public static final String FORMAT_DEFAULT = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern();

    public static final TimeZone TIME_ZONE = TimeZone.getDefault();
    public static final String TIME_ZONE_DISPLAY_NAME = TIME_ZONE.getDisplayName();

    public static final Locale LOCALE_ID = new Locale("id", "ID");
    public static final Locale LOCALE_US = Locale.US;

    public static String formatUs (Date date, String format) {

        return formatLocale(date, format, LOCALE_US);
    }

    public static String formatId (Date date, String format) {

        return formatLocale(date, format, LOCALE_ID);
    }


    private static String formatLocale(Date date, String format, Locale locale) {

        if (null == date) return "";

        return DateFormatUtils.format(date, format, locale);
    }

    public static Date min(Date a, Date b) {

        if (a == null || null == b) return null;

        return a.compareTo(b) > 0 ? b : a;
    }

    public static Date max(Date a, Date b) {

        if (a == null && null == b) return null;

        if (a == null) return b;
        if (b == null) return a;

        return a.compareTo(b) > 0 ? a : b;
    }

}
