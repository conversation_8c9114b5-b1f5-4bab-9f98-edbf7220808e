package com.gtech.gvcore.common.response.issuer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "IssuerResponse")
public class IssuerResponse {

    @ApiModelProperty( value="Issuer code.", example="1122334455")
    private String issuerCode;

    @ApiModelProperty( value="Issuer name.", example="Starbucks")
    private String issuerName;

    @ApiModelProperty( value="Status 0:disable,1:enable.", example="0")
    private Integer status;


    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;


}
