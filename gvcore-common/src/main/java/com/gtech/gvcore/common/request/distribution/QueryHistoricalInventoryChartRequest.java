package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName queryHistoricalInventoryChartRequest
 * @Description Query historical inventory chart interface parameters
 * <AUTHOR>
 * @Date 2022/7/6 10:57
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "QueryHistoricalInventoryChartRequest")
public class QueryHistoricalInventoryChartRequest {

    @ApiModelProperty(value = "CPG Code, when empty, query all CPGs under this customer", example = "CP001")
    private String cpgCode;

    @ApiModelProperty(value = "Customer Code", example = "UC001",required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

}
