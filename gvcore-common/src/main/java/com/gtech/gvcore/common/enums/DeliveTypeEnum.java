package com.gtech.gvcore.common.enums;

public enum DeliveTypeEnum {


    EXTERNAL_COURIER(0, "External Courier"),
	WALK_IN(1, "walk-in"),
    INTERNAL_COURIER(2, "Internal Courier");

    private final int code;

    private final String desc;

    DeliveTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
