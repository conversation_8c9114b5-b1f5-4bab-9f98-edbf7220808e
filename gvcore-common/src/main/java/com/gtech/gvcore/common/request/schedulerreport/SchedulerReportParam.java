package com.gtech.gvcore.common.request.schedulerreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/3 16:27
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SchedulerReportParam {

    private String schedulerReportCode;

    private Integer reportType;

    private String issuerCode;

    private String merchantCode;

    private String merchantOutletCode;

    private String transactionType;

    private Integer dataRange;

    private String transactionStatus;

    private Integer voucherStatus;

    private String vpgCode;

    private String ftpAddress;

    private String loginMethod;

    private String encryptionKey;

    private Integer numberOfExecutions;

    private String createUser;

}
