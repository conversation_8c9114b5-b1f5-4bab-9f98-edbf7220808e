package com.gtech.gvcore.common.response.releaseapprove;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 17:12
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("Release approve node ")
public class ReleaseApproveNodeResponse {

    @ApiModelProperty(value = "Node name ", example = "1")
    private Integer nodeName;
    @ApiModelProperty(value = "Role code ", example = "admin")
    private String roleCode;

}
