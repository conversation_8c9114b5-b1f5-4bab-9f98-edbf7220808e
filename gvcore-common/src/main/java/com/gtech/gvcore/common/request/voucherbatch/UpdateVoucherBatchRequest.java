package com.gtech.gvcore.common.request.voucherbatch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/3/24 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateVoucherBatchRequest")
public class UpdateVoucherBatchRequest {
    @ApiModelProperty(value = "Voucher batch code.", example = "1122333",required = true)
    @NotEmpty(message = "voucherBatchCode can not be empty")
    private String voucherBatchCode;
    @ApiModelProperty(value = "Purchase order no.", example = "1122333",required = true)
    @NotEmpty(message = "purchaseOrderNo can not be empty")
    @Length(max=40)
    private String purchaseOrderNo;
    @ApiModelProperty(value = "Update user.", example = "1122333",required = true)
    @NotEmpty(message = "updateUser can not be empty")
    private String updateUser;



}
