package com.gtech.gvcore.common.response.reportmanagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/12 13:38
 */


@Data
@ApiModel("Query report management list")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryReportManagementResponse implements Serializable {
    private static final long serialVersionUID = 3571396041415189085L;
    @ApiModelProperty(value = "Report management code")
    private String reportManagementCode;
    @ApiModelProperty(value = "Merchant name")
    private String merchant;
    @ApiModelProperty(value = "Report type")
    private Integer reportType;
    @ApiModelProperty(value = "Report name")
    private String reportName;
    @ApiModelProperty(value = "Role name")
    private String role;
    @ApiModelProperty(value = "export PDF permission")
    private Boolean exportPdf;
    @ApiModelProperty(value = "export Excel permission")
    private Boolean exportExcel;

}
