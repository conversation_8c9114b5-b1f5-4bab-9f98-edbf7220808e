package com.gtech.gvcore.common.response.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/1/4 14:25
 */
@Data
@ApiModel("Query HeadOffice And WareHouse Response")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryHeadOfficeAndWareHouseResponse {

    @ApiModelProperty(value = "HeadOffice")
    private String headOffice;
    @ApiModelProperty(value = "WareHouse")
    private String wareHouse;

    @ApiModelProperty(value = "mv01")
    private String mv01;

    @ApiModelProperty(value = "mv03")
    private String mv03;

    @ApiModelProperty(value = "mv04")
    private String mv04;

}
