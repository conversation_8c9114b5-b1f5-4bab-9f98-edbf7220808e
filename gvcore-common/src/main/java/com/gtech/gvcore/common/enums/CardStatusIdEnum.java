package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/11/2 15:32
 */
public enum CardStatusIdEnum {

    /**
     * VOUCHER_NEWLY_GENERATED(0, "Created"),
     *     VOUCHER_ACTIVATED(1, "Activated"),
     *     VOUCHER_USED(2, "Used"),
     *     VOUCHER_CANCELLED(3, "Cancelled"),
     *     VOUCHER_EXPIRED(4, "Expired");
     */

    //CREATED( "9", "100"),
    PURCHASED( "9", "130"),
    ACTIVATED( "1", "140"),
    USED("2", "140"),
    CANCELLED( "3", "310"),
    EXPIRED("4", "120")
    ;


    private final String code;

    private final String desc;

    CardStatusIdEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }



    public static String getByCode(Integer code) {
        for(CardStatusIdEnum cardStatusIdEnum : CardStatusIdEnum.values()) {
            if (cardStatusIdEnum.code.equals(String.valueOf(code)) ) {
                return cardStatusIdEnum.getDesc();
            }
        }
        return null;
    }


}
