package com.gtech.gvcore.common.response.cpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-22 14:49
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetCpgTypeByPageResponse")
public class GetCpgTypeResponse {


    /**
     * cpgTypeCode
     */
    @ApiModelProperty(value = "cpgTypeCode")
    private String cpgTypeCode;

    /**
     * CPG type name
     */
    @ApiModelProperty(value = "cpgTypeName")
    private String cpgTypeName;

    /**
     * prefix
     */
    @ApiModelProperty(value = "cpgTypeName", example = "100")
    private String prefix;

    @ApiModelProperty(value = "automaticActivate", example = "YES/NO")
    private String automaticActivate;

    /**
     * status,0:disable,1:enable
     */
    @ApiModelProperty(value = "status", example = "status,0:disable,1:enable")
    private Integer status;

    /**
     * update time
     */
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
