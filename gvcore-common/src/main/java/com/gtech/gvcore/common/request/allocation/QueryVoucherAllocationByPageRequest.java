package com.gtech.gvcore.common.request.allocation;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022年3月11日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryVoucherAllocationByPageRequest extends PageBean {

    @ApiModelProperty(value = "issuerCode", required = true)
    @NotBlank(message = "IssuerCode is required")
    private String issuerCode;

    @ApiModelProperty(value = "outletCode", required = false)
    private String outletCode;

    @ApiModelProperty(value = "denomination", required = false)
    private BigDecimal denomination;

    @ApiModelProperty(value = "cpgCode")
    private String cpgCode;

    @ApiModelProperty(value = "status", required = false)
    private Integer status;

    @ApiModelProperty(value = "login user code", example = "UC0001",required = true)
    @NotBlank(message = "UserCode is required")
    private String userCode;

}
