package com.gtech.gvcore.common.request.company;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCompanyRequest")
public class QueryCompanyRequest extends PageBean implements Serializable {


    private static final long serialVersionUID = 1237218780595646576L;

    @ApiModelProperty(value = "Company code.", example = "11223344")
    @Length(max = 100)
    private String companyCode;
    
    @ApiModelProperty(value = "Company code list.")
    private List<String> companyCodeList;

    @ApiModelProperty(value = "Issuer code.", example = "11223344")
    @Length(max = 100)
    private String issuerCode;

    @ApiModelProperty(value = "Company name.", example = "11223344")
    @Length(max = 100)
    private String companyName;

    @ApiModelProperty(value = "Sbu.", example = "Digimap")
    @Length(max = 50)
    private String sbu;

    @ApiModelProperty( value="Status.", example="1")
    @Length(max = 100)
    private String status;


}
