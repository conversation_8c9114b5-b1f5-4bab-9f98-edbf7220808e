package com.gtech.gvcore.common.request.productcategory;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月24日
 */
@Data
public class CreateOrUpdateProductCategoryDisscountRequest {

    @ApiModelProperty(value = "productCategoryCode", required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    @Length(max = 50)
    private String productCategoryCode;

    @ApiModelProperty(value = "validFrom", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validFrom;

    @ApiModelProperty(value = "validUpto", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validUpto;

    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status can not be null")
    @Range(min = 0, max = 1)
    private Integer status;

    @ApiModelProperty(value = "disscountDetailList", required = true)
    @NotEmpty(message = "disscountDetailList can not be empty")
    @Valid
    private List<DisscountDetailParamter> disscountDetailList;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
    private String createUser;

}
