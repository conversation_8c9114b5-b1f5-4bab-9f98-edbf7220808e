package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/9/9 10:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RedemptionOtherSumResponse")
public class RedemptionOtherSumResponse {

    private String potentialGVIncome;

    private String potentialExpired;

    private String potentialTotalLIabilities;

    private String totalInactiveVoucher;






}
