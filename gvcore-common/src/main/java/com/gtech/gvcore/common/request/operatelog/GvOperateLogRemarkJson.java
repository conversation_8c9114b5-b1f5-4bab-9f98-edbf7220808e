package com.gtech.gvcore.common.request.operatelog;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-28 13:46
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class GvOperateLogRemarkJson implements Serializable {

    /**
     * language
     */
    @ApiModelProperty( value = "language",example = "en-US/zh-CN")
    private String language;

    /**
     * desc
     */
    @ApiModelProperty( value = "desc")
    private String desc;
}
