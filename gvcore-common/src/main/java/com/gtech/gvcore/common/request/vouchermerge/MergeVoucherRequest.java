package com.gtech.gvcore.common.request.vouchermerge;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MergeVoucherRequest")
public class MergeVoucherRequest implements Serializable {


    private static final long serialVersionUID = 53157181676373018L;

	private List<String> voucherCodeList;

	private String operateUser;

}
