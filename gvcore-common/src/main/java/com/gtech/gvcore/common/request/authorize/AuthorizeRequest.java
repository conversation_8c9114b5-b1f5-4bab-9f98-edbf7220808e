package com.gtech.gvcore.common.request.authorize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

@Data
@Accessors(chain = true)
public class AuthorizeRequest {

	@ApiModelProperty(value = "Terminal ID. ", example = "SF00001", required = true, notes = "The terminal id associated with the POS. Typically this would be the macid or the unique machineid that we generate.")
	@Length(max = 50, message = "Terminal ID maximum length 50")
	@NotBlank(message = "Terminal ID not blank")
	private String terminalId;
	@ApiModelProperty(value = "User Name. ", example = "tony", required = true, notes = "The username to login to the Qwikcilver server")
	@Length(max = 50, message = "User Name maximum length 50")
	@NotBlank(message = "User Name not blank")
	private String userName;
	@ApiModelProperty(value = "Password. ", example = "123456", required = true, notes = "The password used to login to the Qwikcilver server")
	@Length(max = 50, message = "Password maximum length 50")
	@NotBlank(message = "Password not blank")
	private String password;
	@ApiModelProperty(value = "Forwarding Entity ID. ", example = "123", notes = "Additional credentials for using WebPOS type POS. Mandatory for WebPOS, IVR or SMS gateway.")
	private String forwardingEntityId;
	@ApiModelProperty(value = "Forwarding Entity Password. ", example = "123456", notes = "Additional credentials for using WebPOS type POS. Mandatory for WebPOS, IVR or SMS gateway.")
	private String forwardingEntityPassword;
	@ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "Timestamp of client application in YYYY-MM-DD HH:MM:SS format")
	private String dateAtClient;
	@ApiModelProperty(value = "Transaction ID. ", example = "123", required = true, notes = "The transaction id passed in the request.")
	@NotNull(message = "Transaction ID not blank")
	private Integer transactionId;
}
