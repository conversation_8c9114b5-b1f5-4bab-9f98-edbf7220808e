package com.gtech.gvcore.common.request.distribution;

import com.gtech.gvcore.common.enums.DisEmailTemplateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName UpdateDistributionEmailTemplateRequest
 * @Description 创建分发邮件模板参数
 * <AUTHOR>
 * @Date 2022/7/5 15:41
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "UpdateDistributionEmailTemplateRequest")
public class UpdateDistributionEmailTemplateRequest {

    @ApiModelProperty(value = "邮件模板编码", example = "ET001",required = true)
    @NotEmpty(message = "Template code can not be empty")
    private String templateCode;

    @ApiModelProperty(value = "邮件模板名称", example = "Untitled Single Send",required = true)
    @NotEmpty(message = "Template name can not be empty")
    private String templateName;

    @ApiModelProperty(value = DisEmailTemplateTypeEnum.REMARK, example = "0",required = true)
    @NotNull(message = "Template type can not be null")
    private Integer templateType;

    @ApiModelProperty(value = "邮件主题", example = "Informasi Penukaran Map Gift Voucher",required = true)
    @NotEmpty(message = "Subject can not be empty")
    private String subject;

    @ApiModelProperty(value = "富文本内容", example = "context",required = true)
    @NotEmpty(message = "Rich text can not be empty")
    private String richText;

    @ApiModelProperty(value = "客户编码", example = "UC001",required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

    @ApiModelProperty(value = "用户编码", example = "UC001",required = true)
    @NotEmpty(message = "User code can not be empty")
    private String userCode;

}
