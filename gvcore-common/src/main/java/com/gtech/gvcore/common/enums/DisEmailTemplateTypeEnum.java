package com.gtech.gvcore.common.enums;

/**
 * @ClassName DisEmailTemplateTypeEnum
 * @Description 分发邮件类型
 * <AUTHOR>
 * @Date 2022/7/5 14:52
 * @Version V1.0
 **/
public enum DisEmailTemplateTypeEnum implements IEnum<Integer> {

    INDIVIDUAL(0, "Individual"),
    BULK(1, "Bulk"),
    ;

    public static final String REMARK = "分发邮件模板类型: 0-Individual , 1-Bulk";

    DisEmailTemplateTypeEnum(final int code, final String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;


    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(final Integer code) {
        return this.code.equals(code);
    }
}
