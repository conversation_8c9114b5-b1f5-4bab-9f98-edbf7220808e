package com.gtech.gvcore.common.response.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response for getting activation page information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetActivationInfoResponse", description = "Response containing activation page information")
public class GetActivationInfoResponse {
    
    @ApiModelProperty(value = "Customer order code", example = "CUST-ORD-12345")
    private String customerOrderCode;
    
    @ApiModelProperty(value = "Customer email", example = "<EMAIL>")
    private String email;
    
    @ApiModelProperty(value = "Number of vouchers to be activated", example = "5")
    private Integer voucherCount;
}
