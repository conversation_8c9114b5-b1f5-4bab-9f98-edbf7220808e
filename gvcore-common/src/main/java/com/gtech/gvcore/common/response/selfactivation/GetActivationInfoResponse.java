package com.gtech.gvcore.common.response.selfactivation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Response for getting activation page information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetActivationInfoResponse", description = "Response containing activation page information")
public class GetActivationInfoResponse {

    @ApiModelProperty(value = "Customer name", example = "Lorem Ipsum")
    private String customerName;

    @ApiModelProperty(value = "Customer email", example = "<EMAIL>")
    private String customerEmail;

    @ApiModelProperty(value = "Order number", example = "MV04-12345-67890")
    private String orderNumber;

    @ApiModelProperty(value = "Order amount", example = "50000000")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "Currency code", example = "IDR")
    private String currencyCode;

    @ApiModelProperty(value = "Shipping address", example = "Jl. Jendral Sudirman")
    private String shippingAddress;

    @ApiModelProperty(value = "Order details - voucher information")
    private List<VoucherDetail> orderDetails;

    /**
     * Voucher detail information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "VoucherDetail", description = "Voucher detail information")
    public static class VoucherDetail {

        @ApiModelProperty(value = "Voucher type", example = "Physical")
        private String voucherType;

        @ApiModelProperty(value = "Denomination", example = "50K")
        private String denomination;

        @ApiModelProperty(value = "Quantity", example = "50")
        private Integer quantity;
    }
}
