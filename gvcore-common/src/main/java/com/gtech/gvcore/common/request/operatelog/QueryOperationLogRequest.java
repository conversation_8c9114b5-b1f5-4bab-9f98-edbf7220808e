package com.gtech.gvcore.common.request.operatelog;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOperationLogRequest")
public class QueryOperationLogRequest extends PageBean {

    @ApiModelProperty(value = "businessCode")
    private String businessCode;

    @ApiModelProperty(value = "method")
    private String method;

    @ApiModelProperty(value = "operateUser")
    private String operateUser;
}
