package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.HashMap;
import java.util.Map;

@Data
public class BulkProcessVoucherInfo {

    @ApiModelProperty(value = "Card Number. ", required = true, example = "123456", notes = "Mandatory in case of Physical card")
    @Length(max = 50, message = "Card Number maximum length 50")
    private String voucherNumber;
    @ApiModelProperty(value = "Card Program Group Name. ", required = true, example = "123456", notes = "(CPG) Mandatory in case of Digital Card (eGV)")
    @Length(max = 50, message = "Card Program Group Name maximum length 50")
    private String vpgName;
    @ApiModelProperty(value = "Card Pin. ", required = true, example = "123456", notes = "Indicates card security code. Mandatory depending on program setup.")
    @Length(max = 50, message = "Card Pin maximum length 50")
    private String voucherPin;
    @ApiModelProperty(value = "Sequence No. ", required = true, example = "123456", notes = "Sequence Number")
    private Long sequenceNo;
    @ApiModelProperty(value = "Product Code. ", example = "123456", notes = "Identifies card product. Can be used if input type is EGV")
    private String articleCode;
    @ApiModelProperty(value = "Design Code. ", example = "123456", notes = "Identifies card design. Can be used if input type is EGV")
    private String mopCode;
    @ApiModelProperty(value = "Track Data. ", required = true, example = "123456", notes = "Track 2 data, or barcode. Mandatory depending on the program setting")
    private String trackData;

    public Map<String,Object> toMap(){
        Map<String,Object> map = new HashMap<>();
        map.put("cardNumber", voucherNumber);
        map.put("cardProgramGroupName", vpgName);
        map.put("cardPin", voucherPin);
        map.put("sequenceNo",sequenceNo);
        map.put("productCode", articleCode);
        map.put("designCode", mopCode);
        map.put("trackData", trackData);
        return map;
    }





}
