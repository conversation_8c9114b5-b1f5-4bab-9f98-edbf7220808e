package com.gtech.gvcore.common.request.cancelredeem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Cancel Redemption Request
 * 取消兑换请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CancelRedeemRequest", description = "Cancel Redemption Request")
public class CancelRedeemRequest {

    @ApiModelProperty(value = "Terminal ID", notes = "Terminal identification", example = "TERM001", position = 1)
    private String terminalId;

    @ApiModelProperty(value = "Transaction ID", notes = "Unique transaction identifier", example = "12345", position = 2)
    private Integer transactionId;
    
    @ApiModelProperty(value = "Card Number", notes = "Gift card number", example = "1234567890123456", required = true, position = 3)
    private String cardNumber;
    
    @ApiModelProperty(value = "Voucher PIN", notes = "Gift card PIN", example = "123456", position = 4)
    private String voucherPin;
    
    @ApiModelProperty(value = "Amount", notes = "Redemption amount to be cancelled", example = "100.00", required = true, position = 5)
    private BigDecimal amount;
    
    @ApiModelProperty(value = "Original Approval Code", notes = "Approval code from original redemption", example = "AP12345", required = true, position = 6)
    private String originalApprovalCode;
    
    @ApiModelProperty(value = "Original Invoice Number", notes = "Invoice number from original redemption", example = "INV-12345", required = true, position = 7)
    private String originalInvoiceNumber;
    
    @ApiModelProperty(value = "Original Batch Number", notes = "Batch number from original redemption", example = "BATCH001", position = 8)
    private String originalBatchNumber;
    
    @ApiModelProperty(value = "Original Transaction ID", notes = "Transaction ID from original redemption", example = "12345", position = 9)
    private Integer originalTransactionId;
    
    @ApiModelProperty(value = "Notes", notes = "Additional notes for cancellation", example = "Customer request", position = 10)
    private String notes;
    
    @ApiModelProperty(value = "Date at Client", notes = "Client application timestamp, format YYYY-MM-DDTHH:MM:SS", example = "2023-01-01T12:00:00", position = 11)
    private Date dateAtClient;

    @ApiModelProperty(value = "Batch ID", notes = "Batch identification", example = "BATCH123", position = 12)
    private String batchID;
}
