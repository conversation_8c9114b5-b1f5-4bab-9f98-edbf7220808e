package com.gtech.gvcore.common.request.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetCompanyRequest")
public class GetCompanyRequest implements Serializable {


    private static final long serialVersionUID = 3973518179992864814L;
    @ApiModelProperty(value = "Company code.", example = "1345566", required = true)
    @NotEmpty(message = "companyCode can not be empty")
    @Length(max = 100)
    private String companyCode;




}
