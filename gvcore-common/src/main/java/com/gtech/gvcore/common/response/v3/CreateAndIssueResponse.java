
package com.gtech.gvcore.common.response.v3;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
@JsonPropertyOrder({"TransactionId,CurrentBatchNumber,TransactionTypeId,TotalAmount,Notes,ApprovalCode,ResponseCode,ResponseMessage,ErrorCode,ErrorDescription,InputType,TotalCards,NumberOfCards,Cards,BusinessReferenceNumber,IdempotencyKey,GeneralLedger,CostCentre,ExecutionMode"})
public class CreateAndIssueResponse implements Serializable {


    private static final long serialVersionUID = -327170987447882064L;
    @JsonProperty("TransactionId")
    private Long TransactionId;

    @JsonProperty("CurrentBatchNumber")
    private Long CurrentBatchNumber;

    @JsonProperty("TransactionTypeId")
    private Integer TransactionTypeId;

    @JsonProperty("TotalAmount")
    private BigDecimal TotalAmount;

    @JsonProperty("Notes")
    private String Notes;

    @JsonProperty("ApprovalCode")
    private String ApprovalCode;

    @JsonProperty("ResponseCode")
    private Integer ResponseCode;

    @JsonProperty("ResponseMessage")
    private String ResponseMessage;

    @JsonProperty("ErrorCode")
    private String ErrorCode;

    @JsonProperty("ErrorDescription")
    private String ErrorDescription;

    @JsonProperty("InputType")
    private String InputType;

    @JsonProperty("TotalCards")
    private Integer TotalCards;

    @JsonProperty("NumberOfCards")
    private Integer NumberOfCards;

    @JsonProperty("Cards")
    private List<CardsResponse> Cards;

    @JsonProperty("BusinessReferenceNumber")
    private String BusinessReferenceNumber;

    @JsonProperty("IdempotencyKey")
    private String IdempotencyKey;

    @JsonProperty("GeneralLedger")
    private String GeneralLedger;

    @JsonProperty("CostCentre")
    private String CostCentre;

    @JsonProperty("ExecutionMode")
    private Integer ExecutionMode;

}