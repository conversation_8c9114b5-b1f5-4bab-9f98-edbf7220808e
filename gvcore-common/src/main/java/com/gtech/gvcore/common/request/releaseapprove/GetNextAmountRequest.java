package com.gtech.gvcore.common.request.releaseapprove;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 15:11
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("GetNextAmountRequest")
public class GetNextAmountRequest {

    @ApiModelProperty(value = "Range name (NO.)", required = true, example = "1")
    @NotNull(message = "rangeName can not be empty")
    private Integer rangeName;

    @NotNull(message = "endNum can not be empty")
    @ApiModelProperty(value = "End num", required = true, example = "200")
    private BigDecimal endNum;

}
