package com.gtech.gvcore.common.request.flow;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class QueryFlowRequest extends PageBean {

	/**
	 * issuer code
	 */
	@ApiModelProperty(value = "issuer code")
	private String issuerCode;

	/**
	 * flow code
	 */
	@ApiModelProperty(value = "flow code")
	private String flowCode;

	/**
	 * flow name
	 */
	@ApiModelProperty(value = "flow name")
	private String flowName;

}