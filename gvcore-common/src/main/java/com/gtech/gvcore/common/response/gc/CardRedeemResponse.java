package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Card Redeem Response
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CardRedeemResponse", description = "Card Redeem Response")
public class CardRedeemResponse extends BaseApiResponse {

    @ApiModelProperty(value = "Notes", notes = "Reference text", example = "Redemption successful", position = 3)
    private String notes;

    @ApiModelProperty(value = "Retry Key", notes = "Reference number for the request", example = "RETRY123456", position = 4)
    private String retryKey;

    @ApiModelProperty(value = "Source", notes = "Reference for outlet name/POS name/brand name/partner name", example = "MOBILE_APP", position = 5)
    private String source;

    @ApiModelProperty(value = "Gift Cards", notes = "JSON Object containing gift card information", position = 6)
    private List<GiftCardInfo> giftCards;

    @ApiModelProperty(value = "Batch Number", notes = "Batch number from token generation", example = "1001", position = 7)
    private String batchNumber;
} 