package com.gtech.gvcore.common.request.outlet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/21 14:05
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryOutletByBusinessRequest")
public class QueryOutletByBusinessRequest implements Serializable {
    private static final long serialVersionUID = -4353510384013133743L;

    @ApiModelProperty(value = "issuerCode", required = true)
    @NotBlank(message = "issuerCode can not be empty")
    private String issuerCode;

    @ApiModelProperty(value = "outletName")
    private String outletName;

    @ApiModelProperty(value = "businessType request or return&transfer", required = true, example = "return&transfer")
    @NotBlank(message = "businessType can not be empty")
    private String businessType;

    @ApiModelProperty(value = "'from or to'", example = "from")
    private String fromOrTo;
}
