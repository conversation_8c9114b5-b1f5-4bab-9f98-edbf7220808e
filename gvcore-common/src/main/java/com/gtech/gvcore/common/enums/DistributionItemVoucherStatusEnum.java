package com.gtech.gvcore.common.enums;

/**
 * @ClassName DistributionStatusEnum
 * @Description 分发Item状态
 * <AUTHOR>
 * @Date 2022/8/9 11:02
 * @Version V1.0
 **/
public enum DistributionItemVoucherStatusEnum implements IEnum<String> {

    // 如果voucher没有分发,则为AVAILABLE状态
    AVAILABLE("Available", "可用的"),
    DISTRIBUTING("Distributing", "分发中"),
    DISTRIBUTED("Distributed", "分发完成"),
    FAIL("Fail", "分发失败"),
    ;

    private final String code;
    private final String desc;

    DistributionItemVoucherStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(final String code) {
        return this.code().equals(code);
    }

}
