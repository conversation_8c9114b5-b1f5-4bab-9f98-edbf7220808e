package com.gtech.gvcore.common.response.distribution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName GetCustomerDistributionResult
 * @Description 获得客户分发明细
 * <AUTHOR>
 * @Date 2022/9/5 20:59
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "GetCustomerDistributionResult")
public class GetCustomerDistributionResult {

    @ApiModelProperty(value = "distribution code")
    private String distributionCode;

    @ApiModelProperty(value = "customer code")
    private String customerCode;

    @ApiModelProperty(value = "distribution type")
    private String distributionType;

    @ApiModelProperty(value = "email template code")
    private String emailTemplateCode;

    @ApiModelProperty(value = "email subject")
    private String emailSubject;

    @ApiModelProperty(value = "email rich text")
    private String emailRichText;

    @ApiModelProperty(value = "Status")
    private String status;

    @ApiModelProperty(value = "progress status")
    private String progressStatus;

    @ApiModelProperty(value = "create user")
    private String createUser;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    @ApiModelProperty(value = "confirm distribution time")
    private Date confirmDistributionTime;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    @ApiModelProperty(value = "distribute time")
    private Date distributeTime;

    @ApiModelProperty(value = "item list")
    private List<GetDistributionItemResult> itemList;

}