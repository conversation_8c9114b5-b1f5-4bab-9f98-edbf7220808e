package com.gtech.gvcore.common.response.authorize;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
@JsonPropertyOrder({"AuthToken,BatchId,DateAtServer,MerchantName,OutletAddress1,OutletAddress2,OutletCity,OutletState,OutletPinCode,OutletTelephone,MaskCardNumber,PrintMerchantCopy,InvoiceNumberMandatory,NumericUserPwd,IntegerAmounts,CultureName,CurrencySymbol,CurrencyPosition,CurrencyDecimalDigits,DisplayUnitForPoints,ReceiptFooterLine1,ReceiptFooterLine2,ReceiptFooterLine3,ReceiptFooterLine4,MerchantID,TransactionStatus,TransactionId,TransactionType,Notes,ApprovalCode,ResponseCode,ResponseMessage,ErrorCode,ErrorDescription,Result"})
public class AuthorizeResponseV3 implements Serializable {

	private static final long serialVersionUID = 9133159333800589022L;

	@JsonProperty("AuthToken")
	private String AuthToken;

	@JsonProperty("BatchId")
	private Integer BatchId;

	@JsonProperty("DateAtServer")
	private String DateAtServer;

	@JsonProperty("MerchantName")
	private String MerchantName;

	@JsonProperty("OutletAddress1")
	private String OutletAddress1;

	@JsonProperty("OutletAddress2")
	private String OutletAddress2;

	@JsonProperty("OutletCity")
	private String OutletCity;

	@JsonProperty("OutletState")
	private String OutletState;

	@JsonProperty("OutletPinCode")
	private String OutletPinCode;

	@JsonProperty("OutletTelephone")
	private String OutletTelephone;

	@JsonProperty("MaskCardNumber")
	private Boolean MaskCardNumber;

	@JsonProperty("PrintMerchantCopy")
	private Boolean PrintMerchantCopy;

	@JsonProperty("InvoiceNumberMandatory")
	private Boolean InvoiceNumberMandatory;

	@JsonProperty("NumericUserPwd")
	private Boolean NumericUserPwd;

	@JsonProperty("IntegerAmounts")
	private Boolean IntegerAmounts;

	@JsonProperty("CultureName")
	private String CultureName;

	@JsonProperty("CurrencySymbol")
	private String CurrencySymbol;

	@JsonProperty("CurrencyPosition")
	private Integer CurrencyPosition;

	@JsonProperty("CurrencyDecimalDigits")
	private Integer CurrencyDecimalDigits;

	@JsonProperty("DisplayUnitForPoints")
	private String DisplayUnitForPoints;

	@JsonProperty("ReceiptFooterLine1")
	private String ReceiptFooterLine1;

	@JsonProperty("ReceiptFooterLine2")
	private String ReceiptFooterLine2;

	@JsonProperty("ReceiptFooterLine3")
	private String ReceiptFooterLine3;

	@JsonProperty("ReceiptFooterLine4")
	private String ReceiptFooterLine4;

	@JsonProperty("MerchantID")
	private Integer MerchantId;

	@JsonProperty("TransactionStatus")
	private Boolean TransactionStatus;

	@JsonProperty("TransactionId")
	private Integer TransactionId;

	@JsonProperty("TransactionType")
	private String TransactionType;

	@JsonProperty("Notes")
	private String Notes;

	@JsonProperty("ApprovalCode")
	private String ApprovalCode;

	@JsonProperty("ResponseCode")
	private Integer ResponseCode;

	@JsonProperty("ResponseMessage")
	private String ResponseMessage;

	@JsonProperty("ErrorCode")
	private String ErrorCode;

	@JsonProperty("ErrorDescription")
	private String ErrorDescription;

	@JsonProperty("Result")
	private Boolean Result;
}
