package com.gtech.gvcore.common.request.exchangerate;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022年2月17日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class QueryExchangeRateByPageRequest extends PageBean {

    @ApiModelProperty(value = "currencyCode")
    private String currencyCode;

}
