package com.gtech.gvcore.common.request.releaseapprove;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 16:07
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("Check audit permission")
public class ReleaseApproveAbleRequest implements Serializable {

    private static final long serialVersionUID = -5245732043945075176L;
    @NotBlank(message = "businessCode cannot be empty")
    @ApiModelProperty(value = "Business code", required = true, example = "")
    private String businessCode;
    @NotBlank(message = "roleCode cannot be empty")
    @ApiModelProperty(value = "Role code", required = true, example = "admin")
    private String approveRoleCode;
    @ApiModelProperty(value = "User code", required = true)
    @NotBlank(message = "Approve User cannot be empty")
    private String approveUser;
    @JsonIgnore
    private BigDecimal voucherAmount;
    @JsonIgnore
    private String releaseApproveAmountType;
    @ApiModelProperty(hidden = true)
    private String releaseType;
	@ApiModelProperty(value = "Issuer code", required = true)
	@NotBlank(message = "Issuer code cannot be empty")
	private String issuerCode;


}
