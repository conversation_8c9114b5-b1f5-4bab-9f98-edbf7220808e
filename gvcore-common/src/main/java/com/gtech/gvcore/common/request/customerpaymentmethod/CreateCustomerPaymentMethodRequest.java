package com.gtech.gvcore.common.request.customerpaymentmethod;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateCustomerPaymentMethodRequest")
public class CreateCustomerPaymentMethodRequest {


    @ApiModelProperty(value = "Customer code", example = "112233",required = true)
    @NotEmpty(message = "customerCode can not be empty")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Mop group", example = "1",required = true)
    @NotEmpty(message = "mopGroup can not be empty")
    @Length(max = 100)
    private String mopGroup;

    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;







}
