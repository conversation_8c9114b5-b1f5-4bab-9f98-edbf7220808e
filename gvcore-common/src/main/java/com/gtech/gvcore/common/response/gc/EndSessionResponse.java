package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Batch Close Response
 */
@Data
@ApiModel(value = "BatchCloseResponse", description = "Batch Close Response")
public class EndSessionResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Notes", notes = "Reference text information")
    private String notes;

    private Integer voucherRedemptionCount;

    private BigDecimal voucherRedemptionAmount;

    private Integer voucherSalesCount;

    private BigDecimal voucherSalesAmount;

    private Integer giftCardRedemptionCount;

    private BigDecimal giftCardRedemptionAmount;

    private Integer giftCardSalesCount;

    private BigDecimal giftCardSalesAmount;

    private String newToken;

    private Integer newBatchNumber;
} 