package com.gtech.gvcore.common.request.customer;

import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.commons.utils.CheckUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/23 18:00
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Create incomplete customer request")
public class CreateIncompleteCustomerRequest implements Serializable {

    public static final String CORPORATE = "Corporate";
    public static final String INDIVIDUAL = "Individual";
    private static final long serialVersionUID = 8670045279262276901L;


    @ApiModelProperty(value = "Customer name.", example = "user1", required = true)
    @NotEmpty(message = "customerName can not be empty")
    @Length(max = 100)
    private String customerName;

    @ApiModelProperty(value = "Customer type.", example = "1", required = true)
    @NotEmpty(message = "customerType can not be empty")
    @Length(max = 100)
    private String customerType;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Company code.", example = "*********")
    @Length(max = 100)
    private String companyName;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Contact first name.", example = "user1")
    @Length(max = 100)
    private String contactFirstName;

    /**
     * 在customerType为corporate时必填
     */
    @ApiModelProperty(value = "Contact first name.", example = "user2")
    @Length(max = 100)
    private String contactLastName;

    @ApiModelProperty(value = "Contact phone.", example = "11223344", required = true)
    @NotEmpty(message = "contactPhone can not be empty")
    @Length(max = 100)
    private String contactPhone;

    @ApiModelProperty(value = "Contact email.", example = "<EMAIL>", required = true)
    @NotEmpty(message = "contactEmail can not be empty")
    @Length(max = 100)
    @Email(message = "email format error ")
    private String contactEmail;

    @ApiModelProperty(value = "Shipping address1.", example = "2nd Floor NO.23", required = true)
    @NotEmpty(message = "shippingAddress1 can not be empty")
    @Length(max = 100)
    private String shippingAddress1;

    public void validation() {

        if (customerType.equals(CORPORATE)) {

            CheckUtils.isNotBlank(this.contactFirstName, ErrorCodes.PARAM_EMPTY, "contactFirstName");
            CheckUtils.isNotBlank(this.contactLastName, ErrorCodes.PARAM_EMPTY, "contactLastName");

        }


    }

}
