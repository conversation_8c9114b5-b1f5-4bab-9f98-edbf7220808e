package com.gtech.gvcore.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
@Slf4j
public class HttpUtils {


    private static PoolingHttpClientConnectionManager connMgr;
    private static RequestConfig requestConfig;
    private static final int MAX_TIMEOUT = 7000;// 7秒
    private static final String UTF_8 = "UTF-8";
    private static final String JSON = "json";
    private static final String XML = "xml";
    private static final String APPLICATION_JSON = "application/json";
    private static final String APPLICATION_XML = "application/xml";
    private static final String TEXT_PLAIN = "text/plain";

    private HttpUtils(){
        throw new IllegalStateException("HttpUtils class");
    }


     static {
        // 设置连接池
        connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(100);
        connMgr.setDefaultMaxPerRoute(connMgr.getMaxTotal());

        requestConfig = RequestConfig.custom()

                .setConnectTimeout(MAX_TIMEOUT).setConnectionRequestTimeout(MAX_TIMEOUT)

                .setSocketTimeout(MAX_TIMEOUT).build();
    }


    /**
     * 发送 POST 请求（HTTP），JSON形式
     *
     * @param apiUrl
     * @param content 内容
     * @return
     * @throws IOException
     */
    public static String doPost(String apiUrl, String content, String type) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String httpStr = null;
        HttpPost httpPost = new HttpPost(apiUrl);
        CloseableHttpResponse response = null;

        try {
            httpPost.setConfig(requestConfig);
            StringEntity stringEntity = new StringEntity(content, UTF_8);// 解决中文乱码问题
            stringEntity.setContentEncoding(UTF_8);
            if (JSON.equals(type)) {
                stringEntity.setContentType(APPLICATION_JSON);
            } else if (XML.equals(type)) {
                stringEntity.setContentType(APPLICATION_XML);
            } else {
                stringEntity.setContentType(TEXT_PLAIN);
            }
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity, UTF_8);
        } finally {
            httpClient.close();
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    log.error(e.getMessage());
                }
            }
        }
        return httpStr;
    }






}
