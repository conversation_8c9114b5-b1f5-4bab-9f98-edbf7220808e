<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.OutletMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.Outlet">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="outlet_name" jdbcType="VARCHAR" property="outletName" />
<!--    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />-->
    <result column="merchant_code" jdbcType="VARCHAR" property="merchantCode" />
    <result column="business_outlet_code" jdbcType="VARCHAR" property="businessOutletCode" />
    <result column="outlet_type" jdbcType="VARCHAR" property="outletType" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="address1" jdbcType="VARCHAR" property="address1" />
    <result column="address2" jdbcType="VARCHAR" property="address2" />
    <result column="pin_code" jdbcType="VARCHAR" property="pinCode" />
    <result column="first_name" jdbcType="VARCHAR" property="firstName" />
    <result column="last_name" jdbcType="VARCHAR" property="lastName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="alertnate_email" jdbcType="VARCHAR" property="alertnateEmail" />
    <result column="alertnate_phone" jdbcType="VARCHAR" property="alertnatePhone" />
    <result column="descriptive" jdbcType="VARCHAR" property="descriptive" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, outlet_code, outlet_name, issuer_code, merchant_code, business_outlet_code, outlet_type, 
    state_code, city_code, district_code, address1, address2, pin_code, first_name, last_name, 
    email, phone, mobile, alertnate_email, alertnate_phone, descriptive, status, create_user, 
    create_time, update_user, update_time
  </sql>
  <sql id="gv_outlet_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="outletCode != null and outletCode.trim().length() != 0">
        AND (outlet_code = #{outletCode})
      </if>
      <if test="outletName != null and outletName.trim().length() != 0">
        AND (outlet_name = #{outletName})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="merchantCode != null and merchantCode.trim().length() != 0">
        AND (merchant_code = #{merchantCode})
      </if>
      <if test="businessOutletCode != null and businessOutletCode.trim().length() != 0">
        AND (business_outlet_code = #{businessOutletCode})
      </if>
      <if test="outletType != null and outletType.trim().length() != 0">
        AND (outlet_type = #{outletType})
      </if>
      <if test="stateCode != null and stateCode.trim().length() != 0">
        AND (state_code = #{stateCode})
      </if>
      <if test="cityCode != null and cityCode.trim().length() != 0">
        AND (city_code = #{cityCode})
      </if>
      <if test="districtCode != null and districtCode.trim().length() != 0">
        AND (district_code = #{districtCode})
      </if>
      <if test="address1 != null and address1.trim().length() != 0">
        AND (address1 = #{address1})
      </if>
      <if test="address2 != null and address2.trim().length() != 0">
        AND (address2 = #{address2})
      </if>
      <if test="pinCode != null and pinCode.trim().length() != 0">
        AND (pin_code = #{pinCode})
      </if>
      <if test="firstName != null and firstName.trim().length() != 0">
        AND (first_name = #{firstName})
      </if>
      <if test="lastName != null and lastName.trim().length() != 0">
        AND (last_name = #{lastName})
      </if>
      <if test="email != null and email.trim().length() != 0">
        AND (email = #{email})
      </if>
      <if test="phone != null and phone.trim().length() != 0">
        AND (phone = #{phone})
      </if>
      <if test="mobile != null and mobile.trim().length() != 0">
        AND (mobile = #{mobile})
      </if>
      <if test="alertnateEmail != null and alertnateEmail.trim().length() != 0">
        AND (alertnate_email = #{alertnateEmail})
      </if>
      <if test="alertnatePhone != null and alertnatePhone.trim().length() != 0">
        AND (alertnate_phone = #{alertnatePhone})
      </if>
      <if test="descriptive != null and descriptive.trim().length() != 0">
        AND (descriptive = #{descriptive})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>


    <select id="ifExist" resultType="java.lang.Boolean">
      select count(1) from gv_outlet where outlet_code = #{outletCode} and issuer_code = #{issuerCode};
    </select>
  
  <select id="queryByOutletCodeList" parameterType="list" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> 
    FROM gv_outlet 
    WHERE outlet_code IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
  </select>
  
  <select id="queryOutletIssuerNameInfo" parameterType="com.gtech.gvcore.dao.dto.OutletDto" resultType="com.gtech.gvcore.dao.dto.OutletIssuerNameInfo">
    SELECT outlet.outlet_code outletCode, outlet.outlet_name outletName<!--, outlet.issuer_code issuerCode,
        issuer.issuer_name issuerName -->
    FROM gv_outlet outlet 
    <!--LEFT JOIN gv_issuer issuer ON outlet.issuer_code = issuer.issuer_code -->
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test="outletCodeList != null and outletCodeList.size() > 0">
            AND outlet.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
<!--        <if test="issuerCode != null and issuerCode.trim().length() != 0">
            AND outlet.issuer_code = #{issuerCode}
        </if>-->
        <if test="outletType != null and outletType.trim().length() != 0">
            AND outlet.outlet_type = #{outletType} 
        </if>
        <if test="status != null">
            AND outlet.status = #{status}
        </if>
    </trim>
    ORDER BY outlet.id DESC 
  </select>
  
  <select id="queryOutletMerchantName" parameterType="list" resultType="com.gtech.gvcore.dto.OutletMerchantNameInfo">
    SELECT outlet.outlet_code outletCode, outlet.outlet_name outletName, merchant.merchant_name merchantName 
    FROM gv_outlet outlet 
    LEFT JOIN gv_merchant merchant ON merchant.merchant_code = outlet.merchant_code 
    WHERE outlet.outlet_code IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
  </select>
  
  <select id="queryAllOutletCodeByCompanyOrMerchant" parameterType="com.gtech.gvcore.dao.dto.OutletDto" resultType="string">
  	SELECT outlet.outlet_code 
  	FROM gv_outlet outlet
	LEFT JOIN gv_merchant merchant ON merchant.merchant_code = outlet.merchant_code 
	<trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test="companyCodeList != null and companyCodeList.size() > 0">
            AND merchant.company_code IN
            <foreach collection="companyCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() > 0">
            AND merchant.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </trim>
  </select>

  <select id="selectAllTables" resultType="java.lang.String">
    select table_name from information_schema.tables where table_schema ='test_gvcore_db'
  </select>
    <select id="getOutletInfo" resultType="com.gtech.gvcore.dao.model.Outlet">
      SELECT
      <include refid="Base_Column_List"/>
      FROM gv_outlet go
      WHERE go.outlet_code = #{newOutletCode};
    </select>
    <select id="selectAllColumns" resultType="java.lang.String">

      select COLUMN_NAME from information_schema.COLUMNS where table_name = #{allTableName} and table_schema = 'test_gvcore_db'

    </select>
  <select id="selectCountWhereColumnWithOutlet" resultType="java.lang.Integer">

        select count(1) from ${tableName} where ${column} like 'ReplaceTest_%'

  </select>

  <update id="updateOutletCode">
      update ${tableName}
      set ${statement} =
      CASE
      WHEN ${statement} = #{newOutlet.outletCode} THEN #{oldOutlet.outletCode}
      WHEN ${statement} = #{oldOutlet.outletCode} THEN #{newOutlet.outletCode}
      ELSE ${statement}
      END
      WHERE ${statement} = #{oldOutlet.outletCode} or ${statement} = #{newOutlet.outletCode}
    </update>

  <update id="updateParentCode">
    update
        gv_outlet
    set parent_outlet =
      case
      when parent_outlet = #{newOutlet.outletCode} then #{oldOutlet.outletCode}
      when parent_outlet = #{oldOutlet.outletCode} then #{newOutlet.outletCode}
      else parent_outlet
      end
    where parent_outlet = #{oldOutlet.outletCode} or parent_outlet = #{newOutlet.outletCode}
  </update>
  <update id="updateIssuerCode">
    update ${tableName}
    set issuer_code =
          CASE
            WHEN issuer_code = #{newOutlet.issuerCode} THEN #{oldOutlet.issuerCode}
            WHEN issuer_code = #{oldOutlet.issuerCode} THEN #{newOutlet.issuerCode}
            ELSE issuer_code
            END
       where ${issuerStatement} = #{oldOutlet.outletCode} or ${issuerStatement} = #{newOutlet.outletCode}
  </update>

  <update id="updateMerchantCode">
    update ${tableName}
    set merchant_code =
          CASE
            WHEN merchant_code = #{newOutlet.merchantCode} THEN #{oldOutlet.merchantCode}
            WHEN merchant_code = #{oldOutlet.merchantCode} THEN #{newOutlet.merchantCode}
            ELSE merchant_code
            END
    where ${merchantStatement} = #{oldOutlet.outletCode} or ${merchantStatement} = #{newOutlet.outletCode}
  </update>

  <update id="updateOutletParentCode">
    update
      gv_outlet
    set parent_outlet =
          case
            when parent_outlet = #{newOutlet.parentOutlet} then #{oldOutlet.parentOutlet}
            when parent_outlet = #{oldOutlet.parentOutlet} then #{newOutlet.parentOutlet}
            else parent_outlet
            end
    where outlet_code = #{oldOutlet.outletCode} or outlet_code = #{newOutlet.outletCode}
  </update>
  <update id="updateVoucherAllocationVoucherOwnerName">
    update gv_voucher_allocation
    set voucher_owner_name =
          case
            when voucher_owner_name = #{newOutlet.outletName} then #{oldOutlet.outletName}
            when voucher_owner_name = #{oldOutlet.outletName} then #{newOutlet.outletName}
            else voucher_owner_name
            end
    where voucher_owner_code = #{oldOutlet.outletCode} or voucher_owner_code = #{newOutlet.outletCode}
  </update>
  <update id="updateVoucherAllocationReceiverName">
    update gv_voucher_allocation
    set receiver_name =
          case
            when receiver_name = #{newOutlet.outletName} then #{oldOutlet.outletName}
            when receiver_name = #{oldOutlet.outletName} then #{newOutlet.outletName}
            else receiver_name
            end
    where receiver_code = #{oldOutlet.outletCode} or receiver_code = #{newOutlet.outletCode}
  </update>
  <update id="updateVoucherReceiveOutBound">
    update gv_voucher_receive
    set outbound =
          case
            when outbound = #{newOutlet.outletName} then #{oldOutlet.outletName}
            when outbound = #{oldOutlet.outletName} then #{newOutlet.outletName}
            else outbound
            end
    where outbound_code = #{oldOutlet.outletCode} or outbound_code = #{newOutlet.outletCode}
  </update>
  <update id="updateVoucherReceiveInBound">
    update gv_voucher_receive
    set inbound =
          case
            when inbound = #{newOutlet.outletName} then #{oldOutlet.outletName}
            when inbound = #{oldOutlet.outletName} then #{newOutlet.outletName}
            else inbound
            end
    where inbound_code = #{oldOutlet.outletCode} or inbound_code = #{newOutlet.outletCode}
  </update>
  <update id="updateVoucherRequestVoucherOwnerName">
    update gv_voucher_request
    set voucher_owner_name =
          case
            when voucher_owner_name = #{newOutlet.outletName} then #{oldOutlet.outletName}
            when voucher_owner_name = #{oldOutlet.outletName} then #{newOutlet.outletName}
            else voucher_owner_name
            end
    where voucher_owner_code = #{oldOutlet.outletCode} or voucher_owner_code = #{newOutlet.outletCode}
  </update>
  <update id="updateVoucherRequestReceiverName">
    update gv_voucher_request
    set receiver_name =
          case
            when receiver_name = #{newOutlet.outletName} then #{oldOutlet.outletName}
            when receiver_name = #{oldOutlet.outletName} then #{newOutlet.outletName}
            else receiver_name
            end
    where receiver_code = #{oldOutlet.outletCode} or receiver_code = #{newOutlet.outletCode}
  </update>


</mapper>