<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.TransactionDataMapper">
    <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.TransactionData">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="transaction_code" jdbcType="VARCHAR" property="transactionCode"/>
        <result column="transaction_id" jdbcType="VARCHAR" property="transactionId"/>
        <result column="transaction_type" jdbcType="VARCHAR" property="transactionType"/>
        <result column="merchant_code" jdbcType="VARCHAR" property="merchantCode"/>
        <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode"/>
        <result column="batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="bill_number" jdbcType="VARCHAR" property="billNumber"/>
        <result column="outlet_code" jdbcType="VARCHAR" property="outletCode"/>
        <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode"/>
        <result column="transaction_date" jdbcType="TIMESTAMP" property="transactionDate"/>
        <result column="voucher_code" jdbcType="VARCHAR" property="voucherCode"/>
        <result column="voucher_code_num" jdbcType="BIGINT" property="voucherCodeNum"/>
        <result column="initiated_by" jdbcType="VARCHAR" property="initiatedBy"/>
        <result column="pos_code" jdbcType="VARCHAR" property="posCode"/>
        <result column="batch_code" jdbcType="VARCHAR" property="batchCode"/>
        <result column="login_source" jdbcType="VARCHAR" property="loginSource"/>
        <result column="denomination" jdbcType="DECIMAL" property="denomination"/>
        <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount"/>
        <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod"/>
        <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount"/>
        <result column="actual_outlet" jdbcType="VARCHAR" property="actualOutlet"/>
        <result column="card_entry_mode" jdbcType="VARCHAR" property="cardEntryMode"/>
        <result column="forwarding_entity_id" jdbcType="VARCHAR" property="forwardingEntityId"/>
        <result column="response_message" jdbcType="VARCHAR" property="responseMessage"/>
        <result column="transaction_mode" jdbcType="VARCHAR" property="transactionMode"/>
        <result column="corporate_name" jdbcType="VARCHAR" property="corporateName"/>
        <result column="department_division_branch" jdbcType="VARCHAR" property="departmentDivisionBranch"/>
        <result column="customer_salutation" jdbcType="VARCHAR" property="customerSalutation"/>
        <result column="customer_first_name" jdbcType="VARCHAR" property="customerFirstName"/>
        <result column="customer_last_name" jdbcType="VARCHAR" property="customerLastName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber"/>
        <result column="other_input_parameter" jdbcType="CHAR" property="otherInputParameter"/>
        <result column="customer_type" jdbcType="VARCHAR" property="customerType"/>
        <result column="success_or_failure" jdbcType="VARCHAR" property="successOrFailure"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, transaction_code, transaction_id, transaction_type, merchant_code, issuer_code,
        batch_id, bill_number, outlet_code, cpg_code, transaction_date, voucher_code, voucher_code_num,
        initiated_by, pos_code, batch_code, login_source, denomination, paid_amount, payment_method,
        discount_amount, actual_outlet, card_entry_mode, forwarding_entity_id, response_message,
        transaction_mode, corporate_name, department_division_branch, customer_salutation,
        customer_first_name, customer_last_name, mobile, email, invoice_number, other_input_parameter,
        customer_type, success_or_failure, create_user, create_time, update_user, update_time,
        customer_code,reference_number,transaction_channel
    </sql>
    <sql id="gv_transaction_data_query_condition">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="transactionCode != null and transactionCode.trim().length() != 0">
                AND (transaction_code = #{transactionCode})
            </if>
            <if test="transactionId != null and transactionId.trim().length() != 0">
                AND (transaction_id = #{transactionId})
            </if>
            <if test="transactionType != null and transactionType.trim().length() != 0">
                AND (transaction_type = #{transactionType})
            </if>
            <if test="merchantCode != null and merchantCode.trim().length() != 0">
                AND (merchant_code = #{merchantCode})
            </if>
            <if test="issuerCode != null and issuerCode.trim().length() != 0">
                AND (issuer_code = #{issuerCode})
            </if>
            <if test="batchId != null and batchId.trim().length() != 0">
                AND (batch_id = #{batchId})
            </if>
            <if test="billNumber != null and billNumber.trim().length() != 0">
                AND (bill_number = #{billNumber})
            </if>
            <if test="outletCode != null and outletCode.trim().length() != 0">
                AND (outlet_code = #{outletCode})
            </if>
            <if test="cpgCode != null and cpgCode.trim().length() != 0">
                AND (cpg_code = #{cpgCode})
            </if>
            <if test="transactionDate != null">
                AND (transaction_date = #{transactionDate})
            </if>
            <if test="voucherCode != null and voucherCode.trim().length() != 0">
                AND (voucher_code = #{voucherCode})
            </if>
            <if test="voucherCodeNum != null">
                AND (voucher_code_num = #{voucherCodeNum})
            </if>
            <if test="initiatedBy != null and initiatedBy.trim().length() != 0">
                AND (initiated_by = #{initiatedBy})
            </if>
            <if test="posCode != null and posCode.trim().length() != 0">
                AND (pos_code = #{posCode})
            </if>
            <if test="batchCode != null and batchCode.trim().length() != 0">
                AND (batch_code = #{batchCode})
            </if>
            <if test="loginSource != null and loginSource.trim().length() != 0">
                AND (login_source = #{loginSource})
            </if>
            <if test="denomination != null">
                AND (denomination = #{denomination})
            </if>
            <if test="paidAmount != null">
                AND (paid_amount = #{paidAmount})
            </if>
            <if test="paymentMethod != null and paymentMethod.trim().length() != 0">
                AND (payment_method = #{paymentMethod})
            </if>
            <if test="discountAmount != null">
                AND (discount_amount = #{discountAmount})
            </if>
            <if test="actualOutlet != null and actualOutlet.trim().length() != 0">
                AND (actual_outlet = #{actualOutlet})
            </if>
            <if test="cardEntryMode != null and cardEntryMode.trim().length() != 0">
                AND (card_entry_mode = #{cardEntryMode})
            </if>
            <if test="forwardingEntityId != null and forwardingEntityId.trim().length() != 0">
                AND (forwarding_entity_id = #{forwardingEntityId})
            </if>
            <if test="responseMessage != null and responseMessage.trim().length() != 0">
                AND (response_message = #{responseMessage})
            </if>
            <if test="transactionMode != null and transactionMode.trim().length() != 0">
                AND (transaction_mode = #{transactionMode})
            </if>
            <if test="corporateName != null and corporateName.trim().length() != 0">
                AND (corporate_name = #{corporateName})
            </if>
            <if test="departmentDivisionBranch != null and departmentDivisionBranch.trim().length() != 0">
                AND (department_division_branch = #{departmentDivisionBranch})
            </if>
            <if test="customerSalutation != null and customerSalutation.trim().length() != 0">
                AND (customer_salutation = #{customerSalutation})
            </if>
            <if test="customerFirstName != null and customerFirstName.trim().length() != 0">
                AND (customer_first_name = #{customerFirstName})
            </if>
            <if test="customerLastName != null and customerLastName.trim().length() != 0">
                AND (customer_last_name = #{customerLastName})
            </if>
            <if test="mobile != null and mobile.trim().length() != 0">
                AND (mobile = #{mobile})
            </if>
            <if test="email != null and email.trim().length() != 0">
                AND (email = #{email})
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0">
                AND (invoice_number = #{invoiceNumber})
            </if>
            <if test="otherInputParameter != null">
                AND (other_input_parameter = #{otherInputParameter})
            </if>
            <if test="customerType != null and customerType.trim().length() != 0">
                AND (customer_type = #{customerType})
            </if>
            <if test="successOrFailure != null and successOrFailure.trim().length() != 0">
                AND (success_or_failure = #{successOrFailure})
            </if>
            <if test="createUser != null and createUser.trim().length() != 0">
                AND (create_user = #{createUser})
            </if>
            <if test="createTime != null">
                AND (create_time = #{createTime})
            </if>
            <if test="updateUser != null and updateUser.trim().length() != 0">
                AND (update_user = #{updateUser})
            </if>
            <if test="updateTime != null">
                AND (update_time = #{updateTime})
            </if>
            <if test="customerCode != null and customerCode.trim().length() != 0">
                AND (customer_code = #{customerCode})
            </if>
        </trim>
    </sql>

    <select id="sumAmountGroupByBillNumber" parameterType="list" resultMap="BaseResultMap">
        SELECT bill_number, IFNULL(SUM(denomination), 0) denomination
        FROM gv_transaction_data
        WHERE bill_number IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY bill_number
    </select>

    <select id="querySalesData" resultType="com.gtech.gvcore.dto.SalesDataResultDto">
        SELECT IFNULL(article.sap_article_code, '') article_code,
               sum(cod.voucher_amount)              voucherAmount,
               sum(cod.discount)                    discount,
               mp.mop_name,
               article.mop_code,
               sum(cod.voucher_num)                 voucherNum
        FROM gv_customer_order co
                 LEFT JOIN gv_means_of_payment mp ON co.means_of_payment_code = mp.means_of_payment_code
                 LEFT JOIN gv_customer_order_details cod ON cod.customer_order_code = co.customer_order_code
                 LEFT JOIN gv_cpg cpg ON cpg.cpg_code = cod.cpg_code
                 LEFT JOIN gv_article_mop article ON article.article_mop_code = cpg.article_mop_code

        where co.release_time &gt;= #{request.queryDateTimeStart}
          AND co.release_time &lt; #{request.queryDateTimeEnd}
          AND co.outlet_code = #{request.outletCode}
          AND (co.`status` = 'Completed'
            OR co.`status` = 'Source Api'
            OR co.`status` = 'Release'
            OR co.`status` = 'Receive'
            OR co.`status` = 'Deliver'
            )

          AND cod.delete_status &lt;&gt; '1'
        <if test="request.mopCode != null and request.mopCode != ''">
            AND article.mop_code = #{request.mopCode}
        </if>

        GROUP BY
        <if test="request.xmlType == 'WPUBON_C' and request.mvStore == 'MV04'">
            article.mop_code
        </if>
        <if test="request.xmlType == 'WPUBON_C'  and request.mvStore == 'MV03'">
            mp.means_of_payment_code

        </if>
        <if test="request.xmlType == 'WPUBON' and request.mvStore == 'MV04'">

            mp.means_of_payment_code

        </if>
        <if test="request.xmlType == 'WPUBON'  and request.mvStore == 'MV03'">

            mp.means_of_payment_code

        </if>
        <if test="request.xmlType == 'WPUUMS'">

            article.article_code
        </if>

    </select>
    <select id="selectTransactionList" resultType="com.gtech.gvcore.dao.model.TransactionData">
        select
            transaction_type,denomination

        from ${tableName}
        where batch_id = #{batchNumber} and success_or_failure = #{successOrFailure}
    </select>

    <select id="selectCancelSalesTransactionList" resultType="com.gtech.gvcore.service.report.impl.support.sales.bo.CancelSalesDataBo">
        select
        id transactionId,
        voucher_code voucherCode,
        create_time createTime
        from gv_transaction_data
        where success_or_failure = '0'
        and transaction_type = '20'
        <if test="maxCreateTime != null">
            and create_time &gt;= #{maxCreateTime}
        </if>

    </select>

</mapper>