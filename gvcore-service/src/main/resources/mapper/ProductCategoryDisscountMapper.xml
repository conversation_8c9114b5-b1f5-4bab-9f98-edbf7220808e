<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ProductCategoryDisscountMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.ProductCategoryDisscount">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_category_disscount_code" jdbcType="VARCHAR" property="productCategoryDisscountCode" />
    <result column="product_category_code" jdbcType="VARCHAR" property="productCategoryCode" />
    <result column="valid_from" jdbcType="DATE" property="validFrom" />
    <result column="valid_upto" jdbcType="DATE" property="validUpto" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, product_category_disscount_code, product_category_code, valid_from, valid_upto, 
    status, create_user, create_time, update_user, update_time
  </sql>
  <sql id="gv_product_category_disscount_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="productCategoryDisscountCode != null and productCategoryDisscountCode.trim().length() != 0">
        AND (product_category_disscount_code = #{productCategoryDisscountCode})
      </if>
      <if test="productCategoryCode != null and productCategoryCode.trim().length() != 0">
        AND (product_category_code = #{productCategoryCode})
      </if>
      <if test="validFrom != null">
        AND (valid_from = #{validFrom})
      </if>
      <if test="validUpto != null">
        AND (valid_upto = #{validUpto})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_product_category_disscount 
    <include refid="gv_product_category_disscount_query_condition" />
    <if test="order and orderStr == null">
      order by id desc
    </if>
  </select>
  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true">
    insert into gv_product_category_disscount ( 
    id,product_category_disscount_code,product_category_code,valid_from,valid_upto,status,create_user,create_time,update_user,update_time ) values 
    <foreach collection="list" index="index" item="item" separator=",">
       ( #{item.id,jdbcType=BIGINT},#{item.productCategoryDisscountCode,jdbcType=VARCHAR},#{item.productCategoryCode,jdbcType=VARCHAR},#{item.validFrom,jdbcType=DATE},#{item.validUpto,jdbcType=DATE},#{item.status,jdbcType=INTEGER},#{item.createUser,jdbcType=VARCHAR},#{item.createTime,jdbcType=TIMESTAMP},#{item.updateUser,jdbcType=VARCHAR},#{item.updateTime,jdbcType=TIMESTAMP} ) 
    </foreach>
  </insert>
  <select id="count" resultType="int">
    select count(*) from gv_product_category_disscount 
    <include refid="gv_product_category_disscount_query_condition" />
  </select>
  <select id="selectSysDate" resultType="java.util.Date">
     SELECT NOW() 
  </select>
  <update id="updateStatusByPrimaryKey" parameterType="Map">
    update gv_product_category_disscount 
     set STATUS = #{status,jdbcType=INTEGER},UPDATE_TIME=SYSDATE() 
    where  id = #{id,jdbcType=BIGINT}
    <if test="oldStatus != null">
       and STATUS = #{oldStatus,jdbcType=INTEGER} 
    </if>
  </update>
</mapper>