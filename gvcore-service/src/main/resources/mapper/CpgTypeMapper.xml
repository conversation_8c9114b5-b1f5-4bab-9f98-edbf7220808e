<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.CpgTypeMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.CpgType">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cpg_type_code" jdbcType="VARCHAR" property="cpgTypeCode" />
    <result column="cpg_type_name" jdbcType="VARCHAR" property="cpgTypeName" />
    <result column="prefix" jdbcType="VARCHAR" property="prefix" />
    <result column="automatic_activate" jdbcType="VARCHAR" property="automaticActivate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
     cpg_type_code, cpg_type_name, prefix, automatic_activate, status, create_user,
    create_time, update_user, update_time
  </sql>
  <sql id="gv_cpg_type_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="cpgTypeCode != null and cpgTypeCode.trim().length() != 0">
        AND (cpg_type_code = #{cpgTypeCode})
      </if>
      <if test="cpgTypeName != null and cpgTypeName.trim().length() != 0">
        AND (cpg_type_name = #{cpgTypeName})
      </if>
      <if test="prefix != null and prefix.trim().length() != 0">
        AND (prefix = #{prefix})
      </if>
      <if test="automaticActivate != null and automaticActivate.trim().length() != 0">
        AND (automatic_activate = #{automaticActivate})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <select id="queryByCpgTypeCodeList" parameterType="list" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List"/>
 	FROM gv_cpg_type
 	WHERE cpg_type_code IN
 	<foreach collection="list" item="item" open="(" separator="," close=")">
 		#{item}
 	</foreach>
  </select>
  
</mapper>