<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherBatchMapper">
    <select id="queryVoucherBatchList" resultType="com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse">
        SELECT
        vb.purchase_order_no,
        c.cpg_name,
        vb.issuer_code,
        i.issuer_name,
        vb.cpg_code,
        vb.voucher_batch_code,
        am.article_code_name articleName,
        vb.article_code,
        vb.mop_code,
        vb.printer_code,
        p.printer_name,
        vb.booklet_start_no,
        vb.booklet_end_no,
        vb.booklet_per_num,
        vb.booklet_num,
        vb.voucher_start_no,
        vb.voucher_end_no,
        vb.voucher_num,
        vb.denomination,
        vb.voucher_effective_date,
        vb.file_name,
        vb.file_format,
        vb.voucher_num_active,
        vb.voucher_num_used,
        vb.`status`,
        vb.permission_code,
        vb.create_time,
        vb.update_time,
        vb.create_user,
        vb.update_user
        FROM gv_voucher_batch vb
        LEFT JOIN gv_cpg c ON vb.cpg_code = c.cpg_code
        LEFT JOIN gv_issuer i ON vb.issuer_code = i.issuer_code
        LEFT JOIN gv_article_mop am ON vb.article_code = am.article_mop_code
        LEFT JOIN gv_printer p ON vb.printer_code = p.printer_code
        where
        vb.status &lt;&gt; 5
        and vb.status &lt;&gt; 9
        <if test=" request.cpgCode != null and request.cpgCode != '' ">
            AND vb.cpg_code = #{request.cpgCode}
        </if>
        <if test=" request.voucherStartNo != null and request.voucherStartNo != '' ">
            AND vb.voucher_start_no = #{request.voucherStartNo}
        </if>
        <if test=" request.voucherEndNo != null and request.voucherEndNo != '' ">
            AND vb.voucher_end_no = #{request.voucherEndNo}
        </if>
        <if test=" request.voucherBatchCode != null and request.voucherBatchCode != '' ">
            AND vb.voucher_batch_code like concat('%',#{request.voucherBatchCode},'%')
        </if>
        <if test=" request.status != null and request.status != '' ">
            AND vb.status = #{request.status}
        </if>
        <if test=" request.mopCode != null and request.mopCode != '' ">
            AND vb.mop_code = #{request.mopCode}
        </if>
        <if test=" request.issuerCode != null and request.issuerCode != '' ">
            AND vb.issuer_code = #{request.issuerCode}
        </if>
        GROUP BY vb.voucher_batch_code
        ORDER BY vb.create_time DESC
    </select>
</mapper>