<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherMergeMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherMerge">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_main_code" jdbcType="VARCHAR" property="voucherMainCode" />
    <result column="voucher_merge_code" jdbcType="VARCHAR" property="voucherMergeCode" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, voucher_main_code, voucher_merge_code, status, create_time, update_time, create_user, 
    update_user
  </sql>
  <sql id="gv_voucher_merge_query_fuzzy_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="voucherMainCode != null and voucherMainCode.trim().length() != 0">
         AND (voucher_main_code like concat('%',#{voucherMainCode},'%')) 
      </if>
      <if test="voucherMergeCode != null and voucherMergeCode.trim().length() != 0">
         AND (voucher_merge_code like concat('%',#{voucherMergeCode},'%')) 
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
         AND (create_user like concat('%',#{createUser},'%')) 
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
         AND (update_user like concat('%',#{updateUser},'%')) 
      </if>
    </trim>
  </sql>
  <sql id="gv_voucher_merge_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="voucherMainCode != null and voucherMainCode.trim().length() != 0">
        AND (voucher_main_code = #{voucherMainCode})
      </if>
      <if test="voucherMergeCode != null and voucherMergeCode.trim().length() != 0">
        AND (voucher_merge_code = #{voucherMergeCode})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_voucher_merge 
    <if test="fuzzy">
      <include refid="gv_voucher_merge_query_fuzzy_condition" />
    </if>
    <if test="!fuzzy">
      <include refid="gv_voucher_merge_query_condition" />
    </if>
     order by id desc 
  </select>
</mapper>