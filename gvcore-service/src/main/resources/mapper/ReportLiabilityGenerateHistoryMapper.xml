<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.gvcore.dao.mapper.ReportLiabilityGenerateHistoryMapper">

    <!--
   因使用$符号进行表操作 因此 必须最大程度的限制该mapper的操作范围 以免造成sql注入
-->
    <insert id="createLiabilityTable">
        CREATE TABLE gv_report_temp_liability_${tableType}_${tableCode}
            LIKE gv_report_temp_liability_${tableType}_structure
    </insert>

    <!--
      因使用$符号进行表操作 因此 必须最大程度的限制该mapper的操作范围 以免造成sql注入
    -->
    <delete id="truncateLiabilityTable">
        TRUNCATE gv_report_temp_liability_${tableType}_${tableCode}
    </delete>

    <delete id="clearTable">
        DELETE FROM gv_report_temp_liability_${tableType}_${tableCode}
    </delete>

    <select id="selectVoucher" resultType="com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityVoucherMode">
        SELECT id
             , voucher_code
             , cpg_code
             , mop_code
             , denomination
             , voucher_effective_date
             , status
             , voucher_status
        FROM gv_voucher_${tableIndex}
        WHERE create_time  <![CDATA[<]]>  #{createTime}
          AND !(status = 2 and voucher_status = 1)
          AND !(status = 0 and mop_code = 'VCR')
            <!--        排除销毁的券-->
          AND voucher_status <![CDATA[<>]]>  2
          AND voucher_effective_date IS NOT NULL
          AND id  <![CDATA[>]]>  #{lastId}
        ORDER BY id
        LIMIT #{pageSize}

    </select>



    <select id="selectVoucherBySalesTime" resultType="com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityVoucherMode">


        SELECT id
        , voucher_code
        , cpg_code
        , mop_code
        , denomination
        , voucher_effective_date
        , status
        , voucher_status
        FROM gv_voucher_${tableIndex}
        WHERE create_time  <![CDATA[<]]>  #{createTime}
        AND sales_outlet IS NOT NULL
        AND used_outlet IS NULL
        <!--        排除销毁的券-->
        AND voucher_status <![CDATA[<>]]>  2
        AND voucher_effective_date IS NOT NULL
        AND id  <![CDATA[>]]>  #{lastId}
        ORDER BY id
        LIMIT #{pageSize}
    </select>

    <select id="selectTransaction" resultType="com.gtech.gvcore.service.report.impl.support.liability.model.LiabilityTransactionModel">

        SELECT issuer_code
        , merchant_code
        , outlet_code
        , voucher_code
        FROM gv_transaction_data_${tableIndex}
        WHERE voucher_code IN
        <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND transaction_type = '10'
    </select>

    <select id="existTable" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM ${tableName}
    </select>

</mapper>