<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ReportManagementMapper">


    <select id="queryReportManagements"
            resultType="com.gtech.gvcore.common.response.reportmanagement.QueryReportManagementResponse">
        select
        grm.report_management_code as reportManagementCode,
        gm.merchant_name as merchant,
        ir.role_name as role,
        grm.report_type as reportType,
        grm.report_name as reportName,
        grm.export_pdf as exportPdf,
        grm.export_excel as exportExcel
        from gv_report_management as grm
        left join gv_merchant as gm on grm.merchant_code = gm.merchant_code
        left join idm_role as ir on grm.role_code=ir.role_code
        where ir.tenant_code='SYSTEM_DEFAULT' and ir.domain_code='SYSTEM_DEFAULT'
        <if test="reportManagementRequest.merchantCode!=null and reportManagementRequest.merchantCode.trim().length()!=0">
            and grm.merchant_code=#{reportManagementRequest.merchantCode}
        </if>
        <if test="reportManagementRequest.reportType!=null and reportManagementRequest.reportType.trim().length()!=0">
            and grm.report_type=#{reportManagementRequest.reportType}
        </if>
        <if test="reportManagementRequest.roleCode!=null and reportManagementRequest.roleCode.trim().length()!=0">
            and grm.role_code=#{reportManagementRequest.roleCode}
        </if>

    </select>

    <select id="selectByCode" resultType="com.gtech.gvcore.dao.model.ReportManagement">
        select id,
               report_management_code,
               merchant_code,
               report_type,
               report_name,
               export_pdf,
               export_excel,
               create_time,
               create_user,
               update_time,
               update_user
        from gv_report_management
        where report_management_code = #{reportManagementCode};
    </select>

</mapper>