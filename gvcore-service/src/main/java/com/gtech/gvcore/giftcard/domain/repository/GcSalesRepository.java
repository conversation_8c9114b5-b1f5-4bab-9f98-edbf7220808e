package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcSalesRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcSalesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Optional;

/**
 * 礼品卡销售记录仓储实现
 */
@Repository
public class GcSalesRepository {

    @Autowired
    private GcSalesMapper gcSalesMapper;

    /**
     * 保存销售记录
     */
    @Transactional
    public GcSalesRecord save(GcSalesRecord record) {
        GcSalesEntity entity = BeanCopyUtils.jsonCopyBean(record, GcSalesEntity.class);
        if (entity.getId() == null) {
            gcSalesMapper.insert(entity);
        } else {
            gcSalesMapper.updateByPrimaryKey(entity);
        }
        
        return record;
    }


    public int batchSave(List<GcSalesRecord> records) {
        List<GcSalesEntity> entities = BeanCopyUtils.jsonCopyList(records, GcSalesEntity.class);
        return  gcSalesMapper.insertList(entities);
    }
    
    /**
     * 根据销售编码查询销售记录
     */
    public Optional<GcSalesRecord> findBySalesCode(String salesCode) {
        GcSalesEntity entity = gcSalesMapper.selectBySalesCode(salesCode);
        return Optional.ofNullable(entity).map(x->BeanCopyUtils.jsonCopyBean(entity, GcSalesRecord.class));
    }

    /**
     * 根据销售编码查询销售记录
     */
    public Optional<List<GcSalesRecord>> findByPoNumber(String salesCode) {
        Weekend<GcSalesEntity> weekend = Weekend.of(GcSalesEntity.class);
        weekend.weekendCriteria().andEqualTo(GcSalesEntity::getCustomerOrderCode,salesCode);
        List<GcSalesEntity> gcSalesEntities = gcSalesMapper.selectByCondition(weekend);
        return Optional.ofNullable(gcSalesEntities).map(x->BeanCopyUtils.jsonCopyList(gcSalesEntities, GcSalesRecord.class));
    }
    
    /**
     * 根据卡号查询销售记录
     */
    public List<GcSalesRecord> findByCardNumber(String cardNumber) {
        List<GcSalesEntity> entities = gcSalesMapper.selectByCardNumber(cardNumber);
        return BeanCopyUtils.jsonCopyList(entities, GcSalesRecord.class);
    }


    /**
     * 根据batchNumber查询销售记录
     */
    public List<GcSalesRecord> findByBatchNumber(String batchNumber) {
        Weekend<GcSalesEntity> weekend = Weekend.of(GcSalesEntity.class);
        weekend.weekendCriteria().andEqualTo(GcSalesEntity::getBatchNumber,batchNumber);
        List<GcSalesEntity> entities = gcSalesMapper.selectByCondition(weekend);
        return BeanCopyUtils.jsonCopyList(entities, GcSalesRecord.class);
    }

    /**
     * 根据卡号查询销售记录
     */
    public List<GcSalesRecord> findByCardNumber(List<String> cardNumber) {
        List<GcSalesEntity> entities = gcSalesMapper.selectByCardNumbers(cardNumber);
        return BeanCopyUtils.jsonCopyList(entities, GcSalesRecord.class);
    }

    /**
     * 根据Invoice Number查询销售记录
     */
    public List<GcSalesRecord> findByInvoiceNumber(String invoice) {
        List<GcSalesEntity> entities = gcSalesMapper.selectByInvoiceNumber(invoice);
        return BeanCopyUtils.jsonCopyList(entities, GcSalesRecord.class);
    }

    

} 