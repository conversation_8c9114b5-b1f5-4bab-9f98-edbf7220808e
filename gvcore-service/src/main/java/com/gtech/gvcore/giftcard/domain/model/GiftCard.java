package com.gtech.gvcore.giftcard.domain.model;

import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvBusinessException;
import com.gtech.gvcore.giftcard.domain.service.BarcodeConverter;
import com.gtech.gvcore.giftcard.domain.service.CardNumberGenerator;
import com.gtech.gvcore.giftcard.util.PeriodCalculator;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡实体
 */
@Data
public class GiftCard {
    private String issuerCode;
    private String cardBatchCode;
    private String cardNumber;
    private String barCode;
    private GcCardStatus status;
    private GcMgtCardStatus managementStatus;
    private BigDecimal balance;
    private String cpgCode; 
    private BigDecimal denomination;
    private String activationCode;
    private String pinCode;
    private Date activationDeadline;
    private Integer activationExtensionCount;
    private Date activationTime;
    private Date expiryTime;
    private String salesOutlet;
    private Date salesTime;
    private String ownerCustomer;
    private String effectivePeriod;
    private String activationGracePeriod;
    private Date createTime;
    private Date updateTime;




    // 激活礼品卡
    public void activate() {
        validateCanActivate();
        this.activationTime = new Date();
        this.status = GcCardStatus.ACTIVATED;
        this.balance = this.denomination;
        //激活过期时间 = 当前时间 + 过期时间公式时间
        this.expiryTime = PeriodCalculator.calculateDate(effectivePeriod);
    }

    //延长过期时间
    public Date extendExpiryTime() {

        if (checkActivated()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_STATUS_NOT_ALLOWED);
        }

        if (!checkPurchased()){
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_STATUS_NOT_ALLOWED);
        }

        if (checkExpired()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_ALREADY_EXPIRED);
        }

        if (checkActivationExpired()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_NOT_YET_ACTIVATED_TIME);
        }
        
        if (activationExtensionCount >= 1) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_ALREADY_EXTENDED);
        }

        activationExtensionCount++;

        return this.activationDeadline = PeriodCalculator.calculateDate(activationGracePeriod,activationDeadline);
    }



    // 扣减余额
    public void deduct(BigDecimal amount) {
        if (!checkActivated()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NOT_ACTIVATED);
        }
        if (checkExpired()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_EXPIRED);
        }
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.DEDUCTION_AMOUNT_NOT_ALLOWED);
        }
        if (amount.compareTo(this.balance) > 0) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.INSUFFICIENT_BALANCE);
        }
        this.balance = this.balance.subtract(amount);
    }

    // 增加余额
    public void addBalance(BigDecimal amount) {
        if (!checkActivated()) {
            throw new IllegalStateException("礼品卡未激活，无法增加余额");
        }
        if (checkExpired()) {
            throw new IllegalStateException("礼品卡已过期，无法增加余额");
        }
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("增加金额必须大于零");
        }
        this.balance = this.balance.add(amount);
    }

    // 验证是否可以激活
    private void validateCanActivate() {
        if (this.status != GcCardStatus.PURCHASED) {
            throw new IllegalStateException("礼品卡状态不正确");
        }

        if (this.activationDeadline.compareTo(new Date()) < 0) {
            throw new IllegalStateException("礼品卡已过期");
        }
    }

    // 检查是否已过期
    // true 已过期 false 未过期
    public boolean checkExpired() {
        if (this.expiryTime == null) {
            throw new IllegalStateException("礼品卡没有设置过期时间");
        }
        return this.expiryTime.before(new Date());
    }

    // 检查是否超出激活过期时间
    public boolean checkActivationExpired() {
        if (this.activationDeadline == null) {
            throw new IllegalStateException("礼品卡没有设置激活过期时间");
        }
        return this.activationDeadline.after(new Date());
    }

    

    // 检查是否已激活
    public boolean checkActivated() {
        return status == GcCardStatus.ACTIVATED && managementStatus == GcMgtCardStatus.ENABLE;
    }


    // 检查是否可激活
    public boolean checkPurchased() {
        return status == GcCardStatus.PURCHASED && managementStatus == GcMgtCardStatus.ENABLE && !checkExpired();
    }

    // 验证卡号是否有效
    public boolean checkValidCardNumber(CardNumberGenerator cardNumberGenerator) {
        return cardNumberGenerator.isValid(this.cardNumber);
    }

    // 验证激活码是否有效
    public boolean isValidActivationCode(String activationCode) {
        return this.activationCode != null && this.activationCode.equals(activationCode);
    }


    // 从条形码提取卡号
    public static String extractCardNumberFromBarcode(String barcode, BarcodeConverter barcodeConverter) {
        return barcodeConverter.extractCardNumber(barcode);
    }

    /**
     * 卡券状态展示优先级
     * @return
     */
    public String computeStatusByApi(){
        if (managementStatus != GcMgtCardStatus.ENABLE){
            if (managementStatus == GcMgtCardStatus.DISABLE){
                return "DEACTIVATED";
            }
            return managementStatus.name();
        }else if (checkExpired()){
            if (status == GcCardStatus.ZERO_BALANCE){
                return status.name();
            }
            return GcCardStatus.EXPIRED.name();
        }else if (managementStatus == GcMgtCardStatus.ENABLE){
            return status.name();
        }
        return status.name();
    }
    

    // 批量创建用的无参构造函数
    public GiftCard() {
        // 初始化状态
        this.status = GcCardStatus.PURCHASED;
        this.managementStatus = GcMgtCardStatus.ENABLE;
        this.activationExtensionCount = 0;
        //this.balance = BigDecimal.ZERO;
    }
} 