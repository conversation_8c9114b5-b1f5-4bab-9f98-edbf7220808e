package com.gtech.gvcore.giftcard.masterdata.gcpg.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(value = "CreateGcCpgRequest")
public class CreateGcCpgRequest {
    
    @NotBlank(message = "cpgName cannot be null")
    private String cpgName;

    @NotBlank(message = "issuerCode cannot be null")
    private String issuerCode;
    
    @NotNull(message = "automaticActivate cannot be null")
    private Boolean automaticActivate;
    
    // Activation Period
    @NotNull(message = "activationPeriodYears cannot be null")
    private Integer activationPeriodYears;
    @NotNull(message = "activationPeriodMonths cannot be null")
    private Integer activationPeriodMonths;
    @NotNull(message = "activationPeriodDays cannot be null")
    private Integer activationPeriodDays;
    @NotNull(message = "activationPeriodHours cannot be null")
    private Integer activationPeriodHours;

    // Activation Grace Period
    @NotNull(message = "activationGracePeriodYears cannot be null")
    private Integer activationGracePeriodYears;
    @NotNull(message = "activationGracePeriodMonths cannot be null")
    private Integer activationGracePeriodMonths;
    @NotNull(message = "activationGracePeriodDays cannot be null")
    private Integer activationGracePeriodDays;
    @NotNull(message = "activationGracePeriodHours cannot be null")
    private Integer activationGracePeriodHours;

    // Effective Period
    @NotNull(message = "effectivePeriodYears cannot be null")
    private Integer effectivePeriodYears;
    @NotNull(message = "effectivePeriodMonths cannot be null")
    private Integer effectivePeriodMonths;
    @NotNull(message = "effectivePeriodDays cannot be null")
    private Integer effectivePeriodDays;
    @NotNull(message = "effectivePeriodHours cannot be null")
    private Integer effectivePeriodHours;
    
    @NotBlank(message = "currency cannot be null")
    private String currency;
    
    @NotNull(message = "denomination cannot be null")
    private BigDecimal denomination;
    
    @NotBlank(message = "articleMopCode cannot be null")
    private String articleMopCode;
    
    @NotBlank(message = "walletHost cannot be null")
    private String walletHost;
    
    @NotBlank(message = "conceptIdentifier cannot be null")
    private String conceptIdentifier;
    
    @NotBlank(message = "productBrand cannot be null")
    private String productBrand;
    
    private String coverFrontUrl;
    private String coverBackUrl;
    private String createUser;
} 