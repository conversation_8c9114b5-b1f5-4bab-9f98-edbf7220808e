package com.gtech.gvcore.giftcard.domain.model;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡延长激活期限记录
 */
@Data
@Builder
@AllArgsConstructor
public class GcActivationExtensionRecord {
    private final String cardNumber;
    private final String extensionCode;
    private final Date extensionTime;
    private final Date oldActivationDeadline;
    private final Date newActivationDeadline;
    private final String issuerCode;
    private final String outletCode;
    private final String cpgCode;
    private final BigDecimal denomination;
    private final String merchantCode;
    private final String invoiceNumber;
    private final String approvalCode;
    private final String notes;
    private final String batchNumber;
    private final String source;
    private final String posCode;
}
