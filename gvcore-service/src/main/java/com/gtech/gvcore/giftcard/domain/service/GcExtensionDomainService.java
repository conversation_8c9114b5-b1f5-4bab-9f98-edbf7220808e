package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.application.dto.ExtensionDTO;
import com.gtech.gvcore.giftcard.domain.model.GcActivationExtensionRecord;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.domain.repository.GcActivationExtensionRepository;
import com.gtech.gvcore.giftcard.domain.repository.GiftCardRepository;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.helper.GvCodeHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 礼品卡延长激活期限领域服务
 */
@Slf4j
@Service
public class GcExtensionDomainService {

    @Autowired
    private GiftCardRepository giftCardRepository;

    @Autowired
    private GcActivationExtensionRepository extensionRepository;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    /**
     * 延长礼品卡激活期限
     */
    @Transactional
    public GiftCard extendActivationPeriod(ExtensionDTO dto) {
        // 查找礼品卡
        GiftCard giftCard = giftCardRepository.findByCardNumber(dto.getCardNumber())
                .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在"));

        // 记录延长前的激活截止时间
        Date oldActivationDeadline = giftCard.getActivationDeadline();
        
        // 执行延长操作
        Date newActivationDeadline = giftCard.extendExpiryTime();
        
        // 获取网点信息
        Outlet outlet = null;
        if (dto.getOutletCode() != null) {
            outlet = masterDataCache.getOutlet(dto.getOutletCode());
        }

        // 创建延长记录
        GcActivationExtensionRecord record = GcActivationExtensionRecord.builder()
                .cardNumber(dto.getCardNumber())
                .extensionCode(gvCodeHelper.generateExtensionCode())
                .extensionTime(new Date())
                .oldActivationDeadline(oldActivationDeadline)
                .newActivationDeadline(newActivationDeadline)
                .issuerCode(giftCard.getIssuerCode())
                .cpgCode(giftCard.getCpgCode())
                .denomination(giftCard.getDenomination())
                .outletCode(dto.getOutletCode())
                .merchantCode(outlet != null ? outlet.getMerchantCode() : null)
                .invoiceNumber(dto.getInvoiceNumber())
                .approvalCode(dto.getApprovalCode())
                .notes(dto.getNotes())
                .batchNumber(dto.getBatchNumber())
                .source(dto.getSource())
                .posCode(dto.getPosCode())
                .build();

        // 保存延长记录
        extensionRepository.save(record);

        // 保存礼品卡
        giftCardRepository.updateCard(giftCard);

        log.info("礼品卡延长激活期限成功: 卡号={}, 延长码={}, 原截止时间={}, 新截止时间={}", 
                dto.getCardNumber(), record.getExtensionCode(), oldActivationDeadline, newActivationDeadline);

        return giftCard;
    }

    /**
     * 根据卡号查询延长激活期限记录
     */
    public List<GcActivationExtensionRecord> getExtensionRecordsByCardNumber(String cardNumber) {
        return extensionRepository.findByCardNumber(cardNumber)
                .orElse(new ArrayList<>());
    }
}
