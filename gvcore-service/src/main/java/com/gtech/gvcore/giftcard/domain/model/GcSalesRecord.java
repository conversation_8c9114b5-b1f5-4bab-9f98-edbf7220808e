package com.gtech.gvcore.giftcard.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡销售记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GcSalesRecord {

    private  String salesCode;
    private  String cardNumber;
    private  String cpgCode;
    private  String issuerCode;
    private  String merchantCode;
    private  String outletCode;
    private  String customerCode;
    private  String invoiceNumber;
    private  String approvalCode;
    private  String customerOrderCode;
    private  Date salesTime;
    private  BigDecimal denomination;
    private  String notes;
    private  String batchNumber;
    private  String posCode;

}