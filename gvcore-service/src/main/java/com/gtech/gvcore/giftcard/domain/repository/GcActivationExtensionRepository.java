package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcActivationExtensionRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcActivationExtensionEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcActivationExtensionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 礼品卡延长激活期限记录仓储实现
 */
@Repository
public class GcActivationExtensionRepository {

    @Autowired
    private GcActivationExtensionMapper gcActivationExtensionMapper;
    
    /**
     * 保存延长记录
     */
    @Transactional
    public GcActivationExtensionRecord save(GcActivationExtensionRecord record) {
        GcActivationExtensionEntity entity = BeanCopyUtils.jsonCopyBean(record, GcActivationExtensionEntity.class);

        if (entity.getId() == null) {
            // 新增记录时设置创建时间和更新时间
            entity.setCreateTime(new java.util.Date());
            entity.setUpdateTime(new java.util.Date());
            gcActivationExtensionMapper.insertSelective(entity);
        } else {
            // 更新记录时只设置更新时间
            entity.setUpdateTime(new java.util.Date());
            gcActivationExtensionMapper.updateByPrimaryKey(entity);
        }

        return record;
    }

    /**
     * 根据卡号查询延长记录
     */
    public Optional<List<GcActivationExtensionRecord>> findByCardNumber(String cardNumber) {
        List<GcActivationExtensionEntity> entities = gcActivationExtensionMapper.selectByCardNumber(cardNumber);
        return Optional.ofNullable(entities).map(x -> BeanCopyUtils.jsonCopyList(x, GcActivationExtensionRecord.class));
    }

    /**
     * 根据延长码查询延长记录
     */
    public Optional<GcActivationExtensionRecord> findByExtensionCode(String extensionCode) {
        GcActivationExtensionEntity entity = gcActivationExtensionMapper.selectByExtensionCode(extensionCode);
        return Optional.ofNullable(entity).map(x -> BeanCopyUtils.jsonCopyBean(x, GcActivationExtensionRecord.class));
    }

    /**
     * 根据批次号查询延长记录
     */
    public Optional<List<GcActivationExtensionRecord>> findByBatchNumber(String batchNumber) {
        List<GcActivationExtensionEntity> entities = gcActivationExtensionMapper.selectByBatchNumber(batchNumber);
        return Optional.ofNullable(entities).map(x -> BeanCopyUtils.jsonCopyList(x, GcActivationExtensionRecord.class));
    }
}
