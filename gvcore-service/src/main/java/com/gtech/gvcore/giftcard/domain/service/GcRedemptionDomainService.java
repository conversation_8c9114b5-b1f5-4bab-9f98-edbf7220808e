package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.application.dto.RedemptionDTO;
import com.gtech.gvcore.giftcard.domain.model.GcRedemptionRecord;
import com.gtech.gvcore.giftcard.domain.model.GcRedemptionTransactionType;
import com.gtech.gvcore.giftcard.domain.repository.GcRedemptionRepository;
import com.gtech.gvcore.helper.GvCodeHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class GcRedemptionDomainService {

    @Autowired
    private GcRedemptionRepository redemptionRepository;

    @Autowired
    private GvCodeHelper gvCodeHelper;


    @Transactional
    public int recordRedemption(List<RedemptionDTO> redemptionDTOs) {
        if (redemptionDTOs == null || redemptionDTOs.isEmpty()) {
            return 0;
        }
        List<GcRedemptionRecord> recordArrayList = new ArrayList<>();
        for (RedemptionDTO redemptionDTO : redemptionDTOs) {
            // 创建使用记录
            GcRedemptionRecord gcRedemptionRecord = new GcRedemptionRecord(
                    gvCodeHelper.generateRedemptionCode(),
                    null,
                    redemptionDTO.getCardNumber(),
                    redemptionDTO.getCpgCode(),
                    redemptionDTO.getAmount(),
                    redemptionDTO.getBalanceBefore(),
                    redemptionDTO.getOutletCode(),
                    redemptionDTO.getIssuerCode(),
                    redemptionDTO.getMerchantCode(),
                    redemptionDTO.getInvoiceNumber(),
                    redemptionDTO.getApprovalCode(),
                    redemptionDTO.getNotes(),
                    redemptionDTO.getBatchNumber(),
                    redemptionDTO.getRedemptionChannel(),
                    redemptionDTO.getTerminalId(),
                    redemptionDTO.getDenomination(),
                    0,
                    new Date(),
                    GcRedemptionTransactionType.REDEEMED,
                    redemptionDTO.getPosCode()
            );
            recordArrayList.add(gcRedemptionRecord);
        }
        return redemptionRepository.insertList(recordArrayList);
    }

    public List<GcRedemptionRecord> getRedemptionsByCardNumber(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) return Collections.emptyList();
        return redemptionRepository.findByCardNumber(cardNumber);
    }

    public GcRedemptionRecord getRedemption(String cardNumber, String invoiceNumber, String approvalCode) {
        if (StringUtils.isBlank(cardNumber) || StringUtils.isBlank(invoiceNumber)) return null;
        return redemptionRepository.getRedemption(cardNumber, invoiceNumber, approvalCode);
    }

    public List<GcRedemptionRecord> getRedemptionsByBatchNumber(String batchNumberInteger) {
        if (batchNumberInteger == null) return Collections.emptyList();
        return redemptionRepository.findByBatchNumber(batchNumberInteger);
    }
}
