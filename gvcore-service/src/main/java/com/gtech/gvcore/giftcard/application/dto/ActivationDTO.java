package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 礼品卡激活DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivationDTO {
    private String cardNumber;
    private String outletCode;
    private String activationCode;
    private String activationChannel;
    private String invoiceNumber;
    private String approvalCode;
    private String ownerCustomer;
    private String notes;
    private String posCode;

    public void validate() {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or blank");
        }
        if (activationCode == null || activationCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Activation code cannot be null or blank");
        }
        if (activationChannel == null || activationChannel.trim().isEmpty()) {
            throw new IllegalArgumentException("Activation channel cannot be null or blank");
        }
    }
} 