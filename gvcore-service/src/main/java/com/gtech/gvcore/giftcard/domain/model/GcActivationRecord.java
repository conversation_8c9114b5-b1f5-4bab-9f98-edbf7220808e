package com.gtech.gvcore.giftcard.domain.model;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * 礼品卡激活记录
 */
@Data
@Builder
@AllArgsConstructor
public class GcActivationRecord {
    private final String cardNumber;
    private final String activationCode;
    private final Date activationTime;
    private final String outletCode;
    private final String issuerCode;
    private final String amount;
    private final String merchantCode;
    private final String invoiceNumber;
    private final String approvalCode;
    private final String ownerCustomer;
    private final String notes;
    private final String posCode;




}