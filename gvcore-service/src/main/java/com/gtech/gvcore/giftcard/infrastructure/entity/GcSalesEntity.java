package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.*;

/**
 * 礼品卡销售记录实体
 */
@Data
@Entity
@Table(name = "gc_sales")
public class GcSalesEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "sales_code", nullable = false, unique = true)
    private String salesCode;

    @Column(name = "batch_number", nullable = false)
    private String batchNumber;
    
    @Column(name = "card_number", nullable = false)
    private String cardNumber;
    
    @Column(name = "cpg_code", nullable = false)
    private String cpgCode;
    
    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "denomination", nullable = false)
    private BigDecimal denomination;
    
    @Column(name = "merchant_code", nullable = false)
    private String merchantCode;
    
    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "customer_Code")
    private String customerCode;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "approval_code")
    private String approvalCode;
    
    @Column(name = "customer_order_code")
    private String customerOrderCode;

    @Column(name = "notes")
    private String notes;
    
    @Column(name = "sales_time", nullable = false)
    private Date salesTime;
    
    @Column(name = "create_time", nullable = false)
    private Date createTime;
    
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

    @Column(name = "pos_code")
    private String posCode;

}