package com.gtech.gvcore.giftcard.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.util.Calendar;
import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 礼品卡时间周期计算工具类
 * 用于处理相对时间格式"YYMMDDHH"，表示从当前时间起增加的年月日时
 * 例如：
 * - "01000000" 表示增加1年
 * - "00030000" 表示增加3个月
 * - "00000200" 表示增加2天
 * - "00000008" 表示增加8小时
 */
@Slf4j
public class PeriodCalculator {

    private static final int PERIOD_LENGTH = 8;
    private static final String PERIOD_FORMAT = "%08d"; // 用于格式化输出，确保长度为8位
    
    /**
     * 计算指定周期后的日期时间
     * 根据给定的YYMMDDHH格式字符串，计算相对于基准时间的目标日期
     *
     * @param periodStr 格式为"YYMMDDHH"的字符串，表示要增加的年月日时
     * @param baseDate 基准日期，如为null则使用当前时间
     * @return 计算后的目标日期
     */
    public static Date calculateDate(String periodStr, Date baseDate) {
        if (periodStr == null || periodStr.length() != PERIOD_LENGTH) {
            log.warn("无效的周期格式: {}，应为YYMMDDHH格式", periodStr);
            return baseDate != null ? baseDate : new Date();
        }
        
        try {
            // 解析周期字符串中的年月日时
            int years = Integer.parseInt(periodStr.substring(0, 2));
            int months = Integer.parseInt(periodStr.substring(2, 4));
            int days = Integer.parseInt(periodStr.substring(4, 6));
            int hours = Integer.parseInt(periodStr.substring(6, 8));
            
            // 使用Calendar计算目标日期
            Calendar calendar = Calendar.getInstance();
            if (baseDate != null) {
                calendar.setTime(baseDate);
            }
            
            calendar.add(Calendar.YEAR, years);
            calendar.add(Calendar.MONTH, months);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            calendar.add(Calendar.HOUR_OF_DAY, hours);
            
            return calendar.getTime();
        } catch (NumberFormatException | IndexOutOfBoundsException e) {
            log.error("解析周期字符串失败: {}", periodStr, e);
            return baseDate != null ? baseDate : new Date();
        }
    }

    public static Date calculateSubDate(String periodStr, Date baseDate) {
        if (periodStr == null || periodStr.length() != PERIOD_LENGTH) {
            log.warn("无效的周期格式: {}，应为YYMMDDHH格式", periodStr);
            return baseDate != null ? baseDate : new Date();
        }

        try {
            // 解析周期字符串中的年月日时
            int years = Integer.parseInt(periodStr.substring(0, 2));
            int months = Integer.parseInt(periodStr.substring(2, 4));
            int days = Integer.parseInt(periodStr.substring(4, 6));
            int hours = Integer.parseInt(periodStr.substring(6, 8));

            // 使用Calendar计算目标日期
            Calendar calendar = Calendar.getInstance();
            if (baseDate != null) {
                calendar.setTime(baseDate);
            }

            calendar.add(Calendar.YEAR, -years);
            calendar.add(Calendar.MONTH, -months);
            calendar.add(Calendar.DAY_OF_MONTH, -days);
            calendar.add(Calendar.HOUR_OF_DAY, -hours);

            return calendar.getTime();
        } catch (NumberFormatException | IndexOutOfBoundsException e) {
            log.error("解析周期字符串失败: {}", periodStr, e);
            return baseDate != null ? baseDate : new Date();
        }
    }
    
    /**
     * 计算相对于当前时间的目标日期
     *
     * @param periodStr 格式为"YYMMDDHH"的字符串
     * @return 相对于当前时间的目标日期
     */
    public static Date calculateDate(String periodStr) {
        return calculateDate(periodStr, new Date());
    }
    
    /**
     * 计算两个日期之间的周期差值，生成YYMMDDHH格式的字符串
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return YYMMDDHH格式的周期字符串
     */
    public static String calculatePeriod(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return String.format(PERIOD_FORMAT, 0);
        }
        
        if (endDate.before(startDate)) {
            log.warn("结束日期早于开始日期，将返回0周期");
            return String.format(PERIOD_FORMAT, 0);
        }
        
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);
        
        int years = endCal.get(Calendar.YEAR) - startCal.get(Calendar.YEAR);
        int months = endCal.get(Calendar.MONTH) - startCal.get(Calendar.MONTH);
        int days = endCal.get(Calendar.DAY_OF_MONTH) - startCal.get(Calendar.DAY_OF_MONTH);
        int hours = endCal.get(Calendar.HOUR_OF_DAY) - startCal.get(Calendar.HOUR_OF_DAY);
        
        // 处理借位情况
        if (hours < 0) {
            hours += 24;
            days--;
        }
        
        if (days < 0) {
            // 获取上一个月的天数
            Calendar tempCal = (Calendar) startCal.clone();
            tempCal.add(Calendar.MONTH, 1);
            tempCal.set(Calendar.DAY_OF_MONTH, 1);
            tempCal.add(Calendar.DATE, -1);
            int lastMonthDays = tempCal.get(Calendar.DAY_OF_MONTH);
            
            days += lastMonthDays;
            months--;
        }
        
        if (months < 0) {
            months += 12;
            years--;
        }
        
        // 限制年份不超过99
        if (years > 99) {
            years = 99;
        }
        
        // 重要：这里的计算仅是近似值，不考虑润年等复杂情况
        String periodStr = String.format("%02d%02d%02d%02d", years, months, days, hours);
        return periodStr;
    }
    
    /**
     * 格式化日期为易读形式
     *
     * @param date 需要格式化的日期
     * @return 格式化后的字符串，如"2023-05-16 14:30:00"
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return "N/A";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
    
    /**
     * 将年月日时转换为周期字符串
     *
     * @param years 年数 (0-99)
     * @param months 月数 (0-99)
     * @param days 天数 (0-99)
     * @param hours 小时数 (0-99)
     * @return YYMMDDHH格式的字符串
     */
    public static String formatPeriod(int years, int months, int days, int hours) {
        // 确保值在合理范围内
        years = Math.min(Math.max(years, 0), 99);
        months = Math.min(Math.max(months, 0), 99); 
        days = Math.min(Math.max(days, 0), 99);
        hours = Math.min(Math.max(hours, 0), 99);
        
        return String.format("%02d%02d%02d%02d", years, months, days, hours);
    }

    /**
     * 将周期字符串转换为年月日时
     */
    public static Period parsePeriod(String periodStr) {
        if (periodStr == null || periodStr.length() != PERIOD_LENGTH) {
            log.warn("无效的周期格式: {}，应为YYMMDDHH格式", periodStr);
            return new Period(0, 0, 0, 0);
        }

        try {
            int years = Integer.parseInt(periodStr.substring(0, 2));
            int months = Integer.parseInt(periodStr.substring(2, 4));
            int days = Integer.parseInt(periodStr.substring(4, 6));
            int hours = Integer.parseInt(periodStr.substring(6, 8));

            return new Period(years, months, days, hours);
        } catch (NumberFormatException | IndexOutOfBoundsException e) {
            log.error("解析周期字符串失败: {}", periodStr, e);
            return new Period(0, 0, 0, 0);
        }
    }

    @Data
    public static class Period {
        private int years;
        private int months;
        private int days;
        private int hours;

        public Period(int years, int months, int days, int hours) {
            this.years = years;
            this.months = months;
            this.days = days;
            this.hours = hours;
        }
    }
} 