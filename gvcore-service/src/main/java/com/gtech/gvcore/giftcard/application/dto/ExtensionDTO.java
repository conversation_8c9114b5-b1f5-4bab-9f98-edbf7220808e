package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 礼品卡延长激活期限DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtensionDTO {
    private String cardNumber;
    private String outletCode;
    private String invoiceNumber;
    private String approvalCode;
    private String notes;
    private String batchNumber;
    private String source;
    private String posCode;

    public void validate() {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or blank");
        }
    }
}
