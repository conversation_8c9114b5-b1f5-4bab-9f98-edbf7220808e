package com.gtech.gvcore.giftcard.domain.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡使用记录领域模型
 */
@Data
public class GcRedemptionRecord {
    private final String cardNumber;
    private final String redemptionCode;
    private final String originalRedemptionCode;
    private final BigDecimal amount;
    private final BigDecimal balanceBefore;
    private final BigDecimal balanceAfter;
    private final String outletCode;
    private final String issuerCode;
    private final String cpgCode;
    private final String merchantCode;
    private final String invoiceNumber;
    private final String approvalCode;
    private final String notes;
    private final Date redemptionTime;
    private final String batchNumber;
    private final String redemptionChannel;
    private final String terminalId;
    private final BigDecimal denomination;
    private GcRedemptionTransactionType transactionType;
    private Integer redemptionCanceled;
    private final String posCode;

    /**
     * 创建使用记录
     */
    public GcRedemptionRecord(
            String redemptionCode,
            String originalRedemptionCode,
            String cardNumber,
            String cpgCode,
            BigDecimal amount,
            BigDecimal balanceBefore,
            String outletCode,
            String issuerCode,
            String merchantCode,
            String invoiceNumber,
            String approvalCode,
            String notes,
            String batchNumber,
            String redemptionChannel,
            String terminalId,
            BigDecimal denomination,
            Integer redemptionCanceled,
            Date redemptionTime,
            GcRedemptionTransactionType transactionType,
            String posCode
    ) {
        this.redemptionCode = redemptionCode;
        this.originalRedemptionCode = originalRedemptionCode;
        this.cardNumber = cardNumber;
        this.amount = amount;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = calculateBalanceAfter(balanceBefore, amount,transactionType);
        this.invoiceNumber = invoiceNumber;
        this.redemptionTime = redemptionTime;
        this.approvalCode = approvalCode;
        this.outletCode = outletCode;
        this.issuerCode = issuerCode;
        this.merchantCode = merchantCode;
        this.cpgCode = cpgCode;
        this.notes = notes;
        this.transactionType = transactionType;
        this.batchNumber = batchNumber;
        this.redemptionChannel = redemptionChannel;
        this.terminalId = terminalId;
        this.denomination = denomination;
        this.redemptionCanceled = redemptionCanceled;
        this.posCode = posCode;
    }

    /**
     * 取消使用记录
     */
    public void markAsCancelled() {
        if (this.transactionType == GcRedemptionTransactionType.CANCELLED_REDEEM) {
            throw new IllegalStateException("使用记录已经被取消");
        }
        this.transactionType = GcRedemptionTransactionType.CANCELLED_REDEEM;
    }

    /**
     * 计算交易后余额
     */
    private static BigDecimal calculateBalanceAfter(BigDecimal balanceBefore, BigDecimal transactionAmount,GcRedemptionTransactionType transactionType) {
        if (balanceBefore.compareTo(transactionAmount) < 0) {
            throw new IllegalArgumentException("余额不足，无法完成交易");
        }
        if (transactionType == GcRedemptionTransactionType.CANCELLED_REDEEM) {
            return balanceBefore.add(transactionAmount);
        }else if (transactionType == GcRedemptionTransactionType.REDEEMED) {
            return balanceBefore.subtract(transactionAmount);
        }
        return balanceBefore;
    }

} 