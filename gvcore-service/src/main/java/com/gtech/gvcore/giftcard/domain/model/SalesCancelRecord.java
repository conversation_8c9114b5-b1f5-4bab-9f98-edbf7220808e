package com.gtech.gvcore.giftcard.domain.model;

import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * 礼品卡销售取消记录
 */
@Data
public class SalesCancelRecord {
    private String issuerCode;
    private String cpgCode;
    private String merchantCode;
    private String invoiceNumber;
    private String approvalCode;
    private String cancelCode;
    private String outletCode;
    private String cardNumber;
    private String cancelReason;
    private BigDecimal denomination;
    private String customerCode;
    private String create_user;
    private Date cancelTime;
} 