package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvBusinessException;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.application.dto.ActivationDTO;
import com.gtech.gvcore.giftcard.domain.model.GcActivationRecord;
import com.gtech.gvcore.giftcard.domain.model.GcCardStatus;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.domain.repository.GcActivationRepository;
import com.gtech.gvcore.giftcard.domain.repository.GiftCardRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class GcActivateDomainService {

    @Autowired
    private GiftCardRepository giftCardRepository;

    
    @Autowired
    private GcActivationRepository activationRecordRepository;

    @Autowired
    private MasterDataCache masterDataCache;

    /**
     * 激活礼品卡
     */
    @Transactional
    public GiftCard activateGiftCard(ActivationDTO dto) {
        // 查找礼品卡
        GiftCard giftCard = giftCardRepository.findByCardNumber(dto.getCardNumber())
                .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在"));

        //验证礼品卡状态
        if (!giftCard.checkPurchased()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_STATUS_NOT_ALLOWED);
        }

        //验证礼品卡激活日期
        if (giftCard.getActivationDeadline().compareTo(new Date()) < 0 ) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_ALREADY_EXPIRED);
        }
        Outlet outlet = masterDataCache.getOutlet(dto.getOutletCode());

       //验证激活码是否正确
        if (!giftCard.isValidActivationCode(dto.getActivationCode())) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.ACTIVATION_CODE_NOT_MATCHING);
        }

        GcActivationRecord record = GcActivationRecord.builder()
                .issuerCode(giftCard.getIssuerCode())
                .merchantCode(outlet.getMerchantCode())
                .cardNumber(dto.getCardNumber())
                .activationCode(dto.getActivationCode())
                .activationTime(new Date())
                .outletCode(dto.getOutletCode())
                .notes(dto.getNotes())
                .invoiceNumber(dto.getInvoiceNumber())
                .approvalCode(dto.getApprovalCode())
                .ownerCustomer(dto.getOwnerCustomer())
                .posCode(dto.getPosCode())
                .build();

        // 保存激活记录
        activationRecordRepository.save(record);

        giftCard.activate();

        // 保存礼品卡
        giftCardRepository.updateCard(giftCard);

        return giftCard;
    }


    public List<GcActivationRecord> getActivationRecord(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) {
            return Collections.emptyList();
        }
        return activationRecordRepository.findByCardNumber(cardNumber)
                .orElse(Collections.emptyList());
    }

    public List<GcActivationRecord> getActivationByBatchNumber(Integer batchNumberInteger) {
        if (null == batchNumberInteger) {
            return Collections.emptyList();
        }
        return activationRecordRepository.findByBatchNumber(batchNumberInteger)
                .orElse(Collections.emptyList());
    }
}
