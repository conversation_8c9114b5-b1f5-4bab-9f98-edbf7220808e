package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 礼品卡销售记录DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GcSalesDTO {
    private String salesCode;
    private List<String> cardNumber;
    private String cpgCode;
    private String issuerCode;
    private String merchantCode;
    private String outletCode;
    private String customerCode;
    private String invoiceNumber;
    private String approvalCode;
    private String customerOrderCode;
    private Date salesTime;
    private BigDecimal denomination;
    private String notes;
    private String batchNumber;
    private String posCode;

    public void validate() {

        if (cardNumber == null || cardNumber.isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or empty");
        }

        if (cpgCode == null || cpgCode.trim().isEmpty()) {
            throw new IllegalArgumentException("CPG code cannot be null or blank");
        }
        if (merchantCode == null || merchantCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Merchant code cannot be null or blank");
        }
    }
} 