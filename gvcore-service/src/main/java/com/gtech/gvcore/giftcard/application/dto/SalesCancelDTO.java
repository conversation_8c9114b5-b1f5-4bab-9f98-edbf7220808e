package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 礼品卡销售取消记录DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesCancelDTO {

    private String issuerCode;
    private String merchantCode;
    private String outletCode;
    private String invoiceNumber;
    private String approvalNumber;
    private String salesCode;
    private String cardNumber;
    private String cpgCode;
    private String cancelReason;
    private String cancelledBy;
    private Date cancelTime;
    
    public void validate() {
        if (salesCode == null || salesCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Sales code cannot be null or blank");
        }
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or blank");
        }
        if (cancelReason == null || cancelReason.trim().isEmpty()) {
            throw new IllegalArgumentException("Cancel reason cannot be null or blank");
        }
        if (cancelledBy == null || cancelledBy.trim().isEmpty()) {
            throw new IllegalArgumentException("Cancelled by cannot be null or blank");
        }
    }
} 