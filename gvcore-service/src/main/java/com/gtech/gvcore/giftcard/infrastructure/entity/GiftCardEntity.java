package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.*;

/**
 * 礼品卡实体
 */
@Data
@Entity
@Table(name = "gc_gift_card")
public class GiftCardEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "issuer_code", nullable = false)
    private String issuerCode;
    
    @Column(name = "card_number", nullable = false, unique = true)
    private String cardNumber;

    @Column(name = "card_batch_code")
    private String cardBatchCode;
    
    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "management_status", nullable = false)
    private String managementStatus;
    
    @Column(name = "balance", nullable = false)
    private BigDecimal balance;

    @Column(name = "pin_code", nullable = false)
    private String pinCode;
    
    @Column(name = "cpg_code", nullable = false)
    private String cpgCode;

    @Column(name = "bar_code", nullable = false)
    private String barCode;
    
    @Column(name = "denomination", nullable = false)
    private BigDecimal denomination;

    @Column(name = "sales_outlet")
    private String salesOutlet;

    @Column(name = "sales_time")
    private Date salesTime;

    @Column(name = "owner_customer")
    private String ownerCustomer;
    
    @Column(name = "activation_code")
    private String activationCode;
    
    @Column(name = "activation_deadline")
    private Date activationDeadline;

    @Column(name = "effective_period")
    private String effectivePeriod;

    @Column(name = "activation_grace_period")
    private String activationGracePeriod;
    
    @Column(name = "activation_extension_count")
    private Integer activationExtensionCount;

    @Column(name = "activation_time")
    private Date activationTime;
    
    @Column(name = "expiry_time")
    private Date expiryTime;

    @Column(name = "create_time", nullable = false)
    private Date createTime;
    
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

} 