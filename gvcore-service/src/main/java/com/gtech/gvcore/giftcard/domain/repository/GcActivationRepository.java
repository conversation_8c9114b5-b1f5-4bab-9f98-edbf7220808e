package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcActivationRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcActivationEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcActivationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 礼品卡激活记录仓储实现
 */
@Repository
public class GcActivationRepository {

    @Autowired
    private GcActivationMapper gcActivationMapper;
    

    
    /**
     * 保存激活记录
     */
    @Transactional
    public GcActivationRecord save(GcActivationRecord record) {
        GcActivationEntity entity = BeanCopyUtils.jsonCopyBean(record, GcActivationEntity.class);
        
        if (entity.getId() == null) {
            gcActivationMapper.insertSelective(entity);
        } else {
            gcActivationMapper.updateByPrimaryKey(entity);
        }
        
        return record;
    }

    /**
     * 根据卡号查询激活记录
     */
    public Optional<List<GcActivationRecord>> findByCardNumber(String cardNumber) {
        Weekend<GcActivationEntity> gcActivationEntityWeekend = Weekend.of(GcActivationEntity.class);
        gcActivationEntityWeekend.weekendCriteria().andEqualTo(GcActivationEntity::getCardNumber, cardNumber);
        List<GcActivationEntity> entity = gcActivationMapper.selectByCondition(gcActivationEntityWeekend);
        return Optional.ofNullable(entity).map(x -> BeanCopyUtils.jsonCopyList(x, GcActivationRecord.class));
    }


    public Optional<List<GcActivationRecord>> findByBatchNumber(Integer batchNumberInteger) {
        Weekend<GcActivationEntity> gcActivationEntityWeekend = Weekend.of(GcActivationEntity.class);
        gcActivationEntityWeekend.weekendCriteria().andEqualTo(GcActivationEntity::getBatchNumber, batchNumberInteger);
        List<GcActivationEntity> entity = gcActivationMapper.selectByCondition(gcActivationEntityWeekend);
        return Optional.ofNullable(entity).map(x -> BeanCopyUtils.jsonCopyList(x, GcActivationRecord.class));
    }
}