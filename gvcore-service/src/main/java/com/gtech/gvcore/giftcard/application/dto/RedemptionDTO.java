package com.gtech.gvcore.giftcard.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡使用记录DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedemptionDTO {
    private String redemptionCode;
    private String cardNumber;
    private BigDecimal denomination;
    private BigDecimal amount;
    private BigDecimal balanceBefore;
    private BigDecimal balanceAfter;
    private String outletCode;
    private String issuerCode;
    private String merchantCode;
    private String terminalId;
    private String invoiceNumber;
    private String approvalCode;
    private String notes;
    private Date redemptionTime;
    private String transactionType;
    private String cpgCode;
    private String redemptionChannel;
    private String batchNumber;
    private String posCode;
    
    public void validate() {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Card number cannot be null or blank");
        }
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Amount must be greater than zero");
        }


    }
} 