package com.gtech.gvcore.giftcard.masterdata.gcpg.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
@ApiModel(value = "QueryGcCpgListResponse")
public class QueryGcCpgListResponse {
    private String issuerCode;
    private String cpgCode;
    private String cpgName;
    private Boolean automaticActivate;
    private String activationPeriod;
    private String activationGracePeriod;
    private String effectivePeriod;
    private String currency;
    private BigDecimal denomination;
    private String articleCode;
    private String walletHost;
    private String conceptIdentifier;
    private String productBrand;
    private String coverFrontUrl;
    private String coverBackUrl;
    private String status;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
}
