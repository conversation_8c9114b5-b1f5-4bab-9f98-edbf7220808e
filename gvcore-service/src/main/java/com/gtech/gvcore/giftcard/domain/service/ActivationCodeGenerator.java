package com.gtech.gvcore.giftcard.domain.service;

import java.security.SecureRandom;

public class ActivationCodeGenerator {

    private static final String ALPHA_NUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
    private static final String ALPHA_NUMERIC_NO_ZERO = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
    private static final SecureRandom random = new SecureRandom();
    /**
     * 生成8位激活码
     * 规则：
     * 1. 8位字母数字组合
     * 2. 第1-2位：随机字母数字（第1位避免使用0）
     * 3. 第3位：校验位
     * 4. 第4-8位：随机字母数字
     * 5. 校验位计算规则 = (第5位值+第7位值)*3，根据结果类型确定
     *
     * 新流程：先生成非校验位，然后计算校验位，最后组装。
     *
     * @return 8位激活码
     */
    public static String generate() {
        StringBuilder tempCode = new StringBuilder();

        // 1. 生成第1位 (最终C1)
        tempCode.append(ALPHA_NUMERIC_NO_ZERO.charAt(random.nextInt(ALPHA_NUMERIC_NO_ZERO.length())));
        // 2. 生成第2位 (最终C2)
        tempCode.append(ALPHA_NUMERIC.charAt(random.nextInt(ALPHA_NUMERIC.length())));

        // 3. 生成第4位到第8位的5个随机字符 (最终C4-C8)
        for (int i = 0; i < 5; i++) {
            tempCode.append(ALPHA_NUMERIC.charAt(random.nextInt(ALPHA_NUMERIC.length())));
        }



        // 4. 提取用于计算校验码的字符 (最终激活码的第5位和第7位)
        char charForFifthPos = tempCode.charAt(3); // C5
        char charForSeventhPos = tempCode.charAt(5); // C7

        // 5. 计算校验位
        String checkDigit = calculateCheckDigit(charForFifthPos, charForSeventhPos);

        // 6. 组装最终激活码
        StringBuilder finalCode = new StringBuilder();
        finalCode.append(tempCode.substring(0, 2)); // Append C1, C2
        finalCode.append(checkDigit); // Append C3 (校验位)
        finalCode.append(tempCode.substring(2)); // Append C4, C5, C6, C7, C8

        return finalCode.toString();
    }

    /**
     * 计算校验位
     * 参数 fifthChar: 对应最终激活码的第5位字符
     * 参数 seventhChar: 对应最终激活码的第7位字符
     *
     * 新规则解释：
     * 1. 字符类型优先判断：
     *    - 规则1 (1字母+1数字): 若(C5是字母&&C7是数字)则取C5; 若(C5是数字&&C7是字母)则取C7。
     *    - 规则2 (2字母): 若(C5是字母&&C7是字母)则取 "9"。
     * 2. 算术结果判断 (若字符规则不符)：
     *    - 计算 S = (getNumericValue(C5) + getNumericValue(C7)) * 3
     *    - 规则3 (S是1位数字): 取 S。
     *    - 规则4 (S是2位数字): 取 S % 10。
     *    - (S >= 100): 取 "9"。
     *
     * @param fifthChar 对应最终激活码的第5位字符
     * @param seventhChar 对应最终激活码的第7位字符
     * @return 校验位
     */
    private static String calculateCheckDigit(char fifthChar, char seventhChar) {
        boolean isFifthLetter = Character.isLetter(fifthChar);
        boolean isFifthDigit = Character.isDigit(fifthChar);
        boolean isSeventhLetter = Character.isLetter(seventhChar);
        boolean isSeventhDigit = Character.isDigit(seventhChar);

        // 新规则1: (1个数字+1个字母, 取字母)
        if (isFifthLetter && isSeventhDigit) { // C5是字母, C7是数字
            return String.valueOf(fifthChar);
        }
        if (isFifthDigit && isSeventhLetter) { // C5是数字, C7是字母
            return String.valueOf(seventhChar);
        }

        // 新规则2: (2个字母, 取数字"9")
        if (isFifthLetter && isSeventhLetter) { // C5是字母, C7是字母
            return "9";
        }

        // 如果上述字符类型规则不适用，则进行算术结果判断
        int value5 = getNumericValue(fifthChar);
        int value7 = getNumericValue(seventhChar);
        int arithmeticSum = (value5 + value7) * 3;

        // 新规则3: (结果是1位数字, 直接取这个数字)
        if (arithmeticSum < 10) {
            return String.valueOf(arithmeticSum);
        }

        // 新规则4: (结果是2位数字, 取个位数字)
        // Implies 10 <= arithmeticSum < 100, because previous if (sum < 10) was false
        if (arithmeticSum < 100) {
            return String.valueOf(arithmeticSum % 10);
        }

        // 对于 arithmeticSum >= 100 (新规则未明确覆盖，或可视为其他复杂情况)
        // 以及其他未被以上规则覆盖的 fifthChar 和 seventhChar 的数字/非字母组合（例如两个都是数字）
        // 根据原有代码及新规则表格外的普遍回退逻辑，默认为 "9"。
        // 实际上，如果两个都是数字，会落到 arithmeticSum 的判断里。
        // 此处 "9" 是针对 arithmeticSum >= 100 的。
        return "9";
    }

    private static int getNumericValue(char c) {
        if (Character.isDigit(c)) {
            return Character.getNumericValue(c);
        } else if (Character.isLetter(c)) {
            return Character.toUpperCase(c) - 'A' + 1;
        } else {
            return 0;
        }
    }


} 