package com.gtech.gvcore.dao.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_release_approve_amount")
public class ReleaseApproveAmount implements Serializable {

    private static final long serialVersionUID = -3799249114097356924L;

    public static final String C_TYPE = "type";

    @Id
    private Long id;
    @Column(name = "release_approve_amount_code")
    private String releaseApproveAmountCode;
    @Column(name = "type")
    private String type;
    @Column(name = "range_name")
    private Integer rangeName;
    @Column(name = "start_num")
    private BigDecimal startNum;
    @Column(name = "end_num")
    private BigDecimal endNum;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "issuer_code")
    private String issuerCode;


}
