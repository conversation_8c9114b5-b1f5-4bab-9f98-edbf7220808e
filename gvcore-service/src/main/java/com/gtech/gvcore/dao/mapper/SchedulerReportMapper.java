package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.schedulerreport.QuerySchedulerReportRequest;
import com.gtech.gvcore.common.response.schedulerreport.QuerySchedulerReportResponse;
import com.gtech.gvcore.dao.model.SchedulerReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/2 12:06
 */

@Mapper
public interface SchedulerReportMapper extends GTechBaseMapper<SchedulerReport> {

    List<QuerySchedulerReportResponse> queryList(@Param(value = "querySchedulerReportRequest") QuerySchedulerReportRequest querySchedulerReportRequest);

    int updateSchedulerReportStatus(@Param(value = "codeList") List<String> codeList, @Param(value = "status") boolean status);

    /**
     * 乐观锁更新执行次数 与最后执行时间
     * ps: 如果为一次性数据则还会更新数据状态
     * @param id
     * @param lastExecutionTime
     * @param sourceNumberOfExecutions
     * @return
     */
    int updateIncreasingNumberOfExecutions(@Param(value = "id") Long id,
                                           @Param(value = "lastExecutionTime") Date lastExecutionTime,
                                           @Param(value = "sourceNumberOfExecutions") int sourceNumberOfExecutions);

}
