package com.gtech.gvcore.dao.model;

import com.gtech.commons.dao.entity.GTechBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SalesOrderEntity", description = "礼品卡对外销售订单")
public class SalesOrderEntity extends GTechBaseEntity {
    
    private String orderNumber;      // 订单编号
    private String customerId;       // 客户ID
    private Integer status;          // 订单状态
    private BigDecimal totalAmount;  // 订单总金额
    private LocalDateTime createdAt; // 创建时间
    private LocalDateTime completedAt; // 完成时间
    private String channel;          // 销售渠道
    private String remark;           // 备注
    
    // 订单状态
    public static final int STATUS_CREATED = 0;    // 已创建
    public static final int STATUS_PAID = 1;       // 已支付
    public static final int STATUS_COMPLETED = 2;  // 已完成
    public static final int STATUS_CANCELLED = 3;  // 已取消
} 