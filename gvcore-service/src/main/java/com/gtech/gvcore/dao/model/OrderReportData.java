package com.gtech.gvcore.dao.model;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.time.DateUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @ClassName OrderReportData
 * @Description 订单报表执行数据结果
 * <AUTHOR>
 * @Date 2022/8/22 16:30:00
 * @Version V1.0
 **/
@Data
@Table(name = "gv_order_report_data")
@Accessors(chain = true)
public class OrderReportData {

    @Id
    private Long id;

    @Column(name = "order_report_code")
    private String orderReportCode;

    @Column(name = "report_type")
    private Integer reportType;

    @Column(name = "report_index")
    private Integer reportIndex;

    @Column(name = "report_data")
    private String reportData;

}
