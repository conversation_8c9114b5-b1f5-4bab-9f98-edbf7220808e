/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.dao.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "idm_user_account")
public class UserAccount implements Serializable {

	// serialVersionUID
	private static final long serialVersionUID = -1662965879270776314L;

	public static final String C_ID = "id";
	public static final String C_DOMAIN_CODE = "domainCode";
	public static final String C_TENANT_CODE = "tenantCode";
	public static final String C_USER_CODE = "userCode";
	public static final String C_USER_TYPE = "userType";
	public static final String C_ACCOUNT = "account";
	public static final String C_PASS_WORD = "password";
	public static final String C_LAST_NAME = "lastName";
	public static final String C_FIRST_NAME = "firstName";
	public static final String C_EMAIL = "email";
	public static final String C_EMAIL_VERIFIED = "emailVerified";
	public static final String C_MOBILE = "mobile";
	public static final String C_MOBILE_VERIFIED = "mobileVerified";
	public static final String C_STATUS = "status";
	public static final String C_ORG_CODE = "orgCode";
	public static final String C_SOURCE = "source";
	public static final String C_CREATE_USER = "createUser";
	public static final String C_CREATE_TIME = "createTime";
	public static final String C_UPDATE_USER = "updateUser";
	public static final String C_UPDATE_TIME = "updateTime";
	public static final String C_MOBILE_COUNTRY = "mobileCountry";

	// Primary key.
	@Id
	private Long id;

	// 域编码
	@Column(name = "domain_code")
	private String domainCode;

	// 租户编码
	@Column(name = "tenant_code")
	private String tenantCode;

	// 用户编码
	@Column(name = "user_code")
	private String userCode;

	// 用户类型
	@Column(name = "user_type")
	private Integer userType;

	// 用户账号(30)
	@Column(name = "account")
	private String account;

	// 用户密码
	@Column(name = "password")
	private String password;

	// 用户支付密码
	@Column(name = "pay_password")
	private String payPassword;

	@Column(name = "full_name")
	private String fullName;

	// 姓(100)
	@Column(name = "last_name")
	private String lastName;

	// 名(100)
	@Column(name = "first_name")
	private String firstName;

	// 邮件地址(100)
	@Column(name = "email")
	private String email;

	// 邮件地址验证
	@Column(name = "email_verified")
	private Integer emailVerified;

	// Mobile number 前面的国家代码
	@Column(name = "mobile_country")
	private String mobileCountry;

	// 手机号码(30)
	@Column(name = "mobile")
	private String mobile;

	// 手机号码验证
	@Column(name = "mobile_verified")
	private Integer mobileVerified;

	// 组织编码
	@Column(name = "org_code")
	private String orgCode;

	// Source of user registration
	@Column(name = "source")
	private String source;

	// 用户状态
	@Column(name = "status")
	private Integer status;

	// 用户状态
	@Column(name = "user_version")
	private Integer userVersion;

	// Create user code
	@Column(name = "create_user")
	private String createUser;

	// Create time.
	@Column(name = "create_time")
	private Date createTime;

	// Lastest update user
	@Column(name = "update_user")
	private String updateUser;

	// Lastest update time
	@Column(name = "update_time")
	private Date updateTime;

	@Column(name = "password_salt")
	private String passwordSalt;

	@Column(name = "encry_type")
	private Integer encryType;

	@Column(name = "logic_delete")
	private Integer logicDelete;

	@Column(name = "issuer_code")
	private String issuerCode;

	@Column(name = "double_check")
	private Integer doubleCheck;

	@Column(name = "extend_params")
	private String extendParams;
}
