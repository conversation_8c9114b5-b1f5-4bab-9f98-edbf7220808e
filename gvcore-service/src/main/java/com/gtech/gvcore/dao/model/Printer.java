package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_printer")
public class Printer {

    public static final String C_PRINTER_NAME = "printerName";
    public static final String C_PRINTER_CODE = "printerCode";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_ID = "id";
    public static final String C_ISSUER_CODE = "issuerCode";

    public static final String C_MASTER_DATA_DISTRICT_DISTRICT_CODE = "districtCode";

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * printer data UNIQUE KEY
     */
    @Column(name = "printer_code")
    private String printerCode;

    /**
     * outlet name
     */
    @Column(name = "printer_name")
    private String printerName;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * stateCode
     */
    @Column(name = "state_code")
    private String stateCode;

    /**
     * cityCode
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * districtCode
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * address
     */
    private String address;

    /**
     * longitude
     */
    private String longitude;

    /**
     * latitude
     */
    private String latitude;

    /**
     * printer first name
     */
    @Column(name = "first_name")
    private String firstName;

    /**
     * printer last name
     */
    @Column(name = "last_name")
    private String lastName;

    /**
     * printer mobile
     */
    private String mobile;

    /**
     * printer email
     */
    private String email;


    @Column(name = "receiving_method")
    private String receivingMethod;

    /**
     * authorization type
     */
    @Column(name = "ftp_authorization_type")
    private String ftpAuthorizationType;

    /**
     * FTP Url
     */
    @Column(name = "ftp_url")
    private String ftpUrl;

    /**
     * FTP username
     */
    @Column(name = "ftp_username")
    private String ftpUsername;

    /**
     * FTP password
     */
    @Column(name = "ftp_password")
    private String ftpPassword;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * key file
     */
    @Column(name = "ftp_key_file")
    private byte[] ftpKeyFile;

    @Column(name = "ftp_key_file_url")
    private String ftpKeyFileUrl;
}