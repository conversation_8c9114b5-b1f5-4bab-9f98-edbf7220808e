package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.IssueHandlingProof;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IssueHandlingProofMapper extends GTechBaseMapper<IssueHandlingProof> {
    List<IssueHandlingProof> query(Map<String, Object> parameters);

    int count(Map<String, Object> parameters);

    int updateStatusByPrimaryKey(Map<String, Object> map);

    IssueHandlingProof getByCode(Map<String, Object> map);

    int delByCode(Map<String, Object> map);

    void insertBatch(List<IssueHandlingProof> list);
}