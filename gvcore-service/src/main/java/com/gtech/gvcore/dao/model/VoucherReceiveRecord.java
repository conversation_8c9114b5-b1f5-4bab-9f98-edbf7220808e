package com.gtech.gvcore.dao.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Table(name = "gv_voucher_receive_record")
@Data
public class VoucherReceiveRecord {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * voucher receive code
     */
    @Column(name = "voucher_receive_code")
    private String voucherReceiveCode;

    /**
     * cpg code 
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * voucher start NO
     */
    @Column(name = "voucher_start_no")
    private String voucherStartNo;

    /**
     * voucher end NO
     */
    @Column(name = "voucher_end_no")
    private String voucherEndNo;

    /**
     * denomination
     */
    private BigDecimal denomination;

    /**
     * received number of vouchers
     */
    @Column(name = "received_num")
    private Integer receivedNum;

    /**
     * booklet start NO
     */
    @Column(name = "booklet_start_no")
    private String bookletStartNo;

    /**
     * booklet end NO
     */
    @Column(name = "booklet_end_no")
    private String bookletEndNo;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;
}