package com.gtech.gvcore.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * exchange rate(GvExchangeRate)实体类
 *
 * <AUTHOR>
 * @since 2022-02-25 15:03:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_exchange_rate")
public class GvExchangeRateEntity implements Serializable {

    private static final long serialVersionUID = -87579959556707866L;

    public static final String C_EXCHANGE_RATE_CODE = "exchangeRateCode";

    public static final String C_CURRENCY_CODE = "currencyCode";

    public static final String C_UPDATE_TIME = "updateTime";

    /**
    * id
    */
    @Id
    private Long id;
    /**
    * exchange rate code,data UNIQUE KEY
    */
    @Column(name = "exchange_rate_code")
    private String exchangeRateCode;
    /**
    * currency code
    */
    @Column(name = "currency_code")
    private String currencyCode;
    /**
    * exchange rate
    */
    @Column(name = "exchange_rate")
    private BigDecimal exchangeRate;
    /**
    * exchange currency code
    */
    @Column(name = "exchange_currency_code")
    private String exchangeCurrencyCode;

    /**
     * exchange exchange_rate_date
     */
    @Column(name = "exchange_rate_date")
    private Date exchangeRateDate;

    /**
    * status,0:disable,1:enable
    */
    private Integer status;
    /**
    * create user
    */
    @Column(name = "create_user")
    private String createUser;
    /**
    * create time
    */
    @Column(name = "create_time")
    private Date createTime;
    /**
    * update user
    */
    @Column(name = "update_user")
    private String updateUser;
    /**
    * update time
    */
    @Column(name = "update_time")
    private Date updateTime;
}