package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.GvDashboard;
import com.gtech.gvcore.dto.DashboardDateDto;
import com.gtech.gvcore.dto.HistogramDto;
import com.gtech.gvcore.dto.PieChartDto;
import com.gtech.gvcore.dto.RedemptionOutletDataDto;
import com.gtech.gvcore.dto.SalesBarChartDto;
import com.gtech.gvcore.dto.VcrInStockDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/29 13:44
 */
@Mapper
public interface DashboardMapper extends GTechBaseMapper<GvDashboard> {



    @Select("<script>" +
            "SELECT " +
            " SUM(d.amount) amount, " +
            " IFNULL(SUM(d.discount),0.0) discount, " +
            " SUM(d.voucher_count) voucherCount, " +
            " date_format( d.dateTime, '%Y-%m' ) date " +
            "FROM " +
            " gv_dashboard d " +
            "WHERE " +
            " d.dashboard_type = #{type}  " +
            " AND d.dateTime &gt;= date_format( #{request.startDate}, '%Y-%m-%d' )  " +
            " AND d.dateTime &lt;= date_format(#{request.endDate},'%Y-%m-%d')" +
            " AND d.issuer_code = #{issuerCode}" +
            " GROUP BY " +
            " MONTH (d.dateTime) " +
            "</script>")
    List<HistogramDto> histogram(@Param("request") DashboardDateDto year, @Param("type")String type,@Param("issuerCode") String issuerCode);
    
    
    
    
    @Select("<script>" +
            "SELECT " +
            " d.outlet_code, " +
            " d.outlet_type, " +
            " o.outlet_name, " +
            " SUM( d.amount ) amount, " +
            //" IFNULL( SUM( d.discount ), 0.0 ) discount, " +
            " SUM( d.voucher_count ) voucherCount " +
            "FROM " +
            " gv_dashboard_detail d  " +
            " LEFT JOIN gv_outlet o ON o.outlet_code = d.outlet_code " +
            "WHERE " +
            " d.dashboard_type = '0'  " +
            " AND d.dateTime &gt;= date_format( #{request.startDate}, '%Y-%m-%d' )  " +
            " AND d.dateTime &lt;= date_format(#{request.endDate},'%Y-%m-%d')" +
            " AND d.issuer_code = #{issuerCode} " +
            "GROUP BY " +
            " outlet_code " +
            "</script>")
    List<PieChartDto> pieChart(@Param("request") DashboardDateDto year,@Param("issuerCode")String  issuerCode);




    @Select("<script>" +
            "SELECT " +
            " IFNULL(SUM(d.amount),0) amount, " +
            " IFNULL(SUM(d.discount),0.0) discount, " +
            " IFNULL(SUM(d.voucher_count),0) voucherCount " +

            "FROM " +
            " gv_dashboard d " +
            "WHERE " +
            " d.dashboard_type = '0'  " +
            //按小时维度查询,数据用于增长比例
            " <if test=\" type=='hour' \"> " +
                " AND d.dateTime &gt;= date_format( #{startDate}, '%Y-%m-%d' )  " +
                " AND d.dateTime &lt; date_format(#{endDate},'%Y-%m-%d %H')" +
            " </if>" +
            //数据用于某年某月某日的交易数据
            " <if test=\" type=='day' \"> " +
                " AND date_format(d.dateTime, '%Y-%m-%d' ) &gt;= date_format( #{startDate}, '%Y-%m-%d' )  " +
                " AND date_format(d.dateTime, '%Y-%m-%d' ) &lt;= date_format(#{endDate},'%Y-%m-%d')" +
            " </if>" +
            " AND d.issuer_code = #{issuerCode}" +

            "</script>")
    SalesBarChartDto salesBarChart(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("type")String type, @Param("issuerCode")String issuerCode);





    @Select("<script>" +
            "SELECT " +
            " c.sbu, " +
            " SUM( d.amount ) amount, " +
            " SUM( d.voucher_count ) voucherCount " +
            "FROM " +
            " gv_dashboard_detail d  " +
            " LEFT JOIN gv_outlet o ON o.outlet_code = d.outlet_code " +
            " LEFT JOIN gv_merchant m ON m.merchant_code = o.merchant_code " +
            " LEFT JOIN gv_company c ON c.company_code = m.company_code " +
            " WHERE " +
            " d.dashboard_type = '1'  " +
            " AND d.dateTime &gt;= date_format( #{request.startDate}, '%Y-%m-%d' )  " +
            " AND d.dateTime &lt;= date_format(#{request.endDate},'%Y-%m-%d')" +
            " AND d.issuer_code = #{issuerCode} " +
            "GROUP BY " +
            " c.sbu " +
            "</script>")
    List<RedemptionOutletDataDto> redemptionOutletData(@Param("request") DashboardDateDto year, @Param("issuerCode")String  issuerCode);



    @Select("<script>" +
            "SELECT  " +
            "  o.outlet_type name,  " +
            "  COUNT( v.voucher_code ) voucherCount,  " +
            "  v.denomination   " +
            "FROM  " +
            "  gv_voucher v  " +
            "  LEFT JOIN gv_outlet o ON o.outlet_code = v.voucher_owner_code   " +
            "WHERE  " +
            "  v.voucher_owner_type = #{ownerType}   " +
            "  AND v.mop_code = 'VCR'   " +
            " AND v.issuer_code = #{issuerCode} " +
            "GROUP BY  " +
            "  o.outlet_type,  " +
            "  v.denomination " +
            " order by v.denomination " +
            "</script>")
    List<VcrInStockDto> vcrInStock(@Param("issuerCode")String  issuerCode,@Param("ownerType")String ownerType);



    /*@Select("<script>" +
            " SELECT " +
            "  v.denomination ," +

            " v.voucher_effective_date," +
            " v.`status`," +
            " v.voucher_status " +
            " " +
            " FROM " +
            "  gv_voucher v  " +
            " WHERE " +
            "  v.issuer_code = #{issuerCode}  "+
            " AND v.create_time &gt;= #{request.startDate}   " +
            " AND v.create_time &lt;= #{request.endDate}" +
            "</script>")
                List<RedemptionOtherDto> otherSum(@Param("request")DashboardDateDto request, @Param("issuerCode")String issuerCode, @Param("endDate") Date endDate, @Param("status")String status, @Param("voucherStatus")String voucherStatus, @Param("transactionType")String transactionType);

            */

    @Select("<script>" +
            " " +
            " SELECT " +
            "  SUM(IFNULL(t.denomination,0.0)) " +
            " FROM " +
            "  gv_transaction_data t  " +

            " LEFT JOIN gv_voucher v ON v.voucher_code = t.voucher_code " +

            " WHERE " +
            "  t.issuer_code = #{issuerCode}  " +

            " <if test=\" transactionType != null and transactionType != '' \"> " +
            "  AND t.transaction_type = #{transactionType} " +
            " </if>"+

            " <if test=\" endDate != null  \"> " +
            "  AND date_format(v.voucher_effective_date,'%Y-%m-%d') &lt;= date_format(#{endDate},'%Y-%m-%d')" +
            " </if>"+

            " <if test=\" status != null and status != '' \"> " +
            " AND v.`status` = #{status} " +
            " </if>"+

            " <if test=\" voucherStatus != null and voucherStatus != '' \"> " +
            "  AND v.voucher_status = #{voucherStatus}  " +
            " </if>"+

            " AND t.create_time &gt;= date_format( #{request.startDate}, '%Y-%m-%d' )  " +
            " AND t.create_time &lt;= date_format(#{request.endDate},'%Y-%m-%d')" +
            "</script>")
    BigDecimal otherSum(@Param("request")DashboardDateDto request, @Param("issuerCode")String issuerCode, @Param("endDate") Date endDate, @Param("status")String status, @Param("voucherStatus")String voucherStatus, @Param("transactionType")String transactionType);

}
