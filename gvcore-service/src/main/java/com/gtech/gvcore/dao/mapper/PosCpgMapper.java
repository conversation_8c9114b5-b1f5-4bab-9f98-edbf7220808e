package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;
import com.gtech.gvcore.dao.model.PosCpg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PosCpgMapper extends GTechBaseMapper<PosCpg> {


    @Select("SELECT " +
            " oc.id, " +
            " oc.pos_cpg_code, " +
            " oc.pos_code, " +
            //gv_cpg 和 gc_cpg 二选一,根据是否为空 判断
            " CASE " +
            " WHEN  c.cpg_code IS NOT NULL THEN c.cpg_name " +
            " WHEN  gc.cpg_code IS NOT NULL THEN gc.cpg_name " +
            " ELSE '' " +
            " END AS cpgName, " +
            " CASE " +
            " WHEN  c.cpg_code IS NOT NULL THEN c.cpg_code " +
            " WHEN  gc.cpg_code IS NOT NULL THEN gc.cpg_code " +
            " ELSE '' " +
            " END AS cpgCode, " +

            " oc.`status`, " +
            " oc.create_user, " +
            " oc.create_time, " +
            " oc.update_user, " +
            " oc.update_time  " +
            "FROM " +
            " gv_pos_cpg oc " +
            " LEFT JOIN gv_cpg c ON oc.cpg_code = c.cpg_code" +
            " LEFT JOIN gc_cpg gc ON oc.cpg_code = gc.cpg_code" +
            " WHERE oc.pos_code = #{posCode} ")
    List<PosCpgResponse> queryPosCpgListByPos(String posCode);


    @Select("SELECT " +
            " oc.id, " +
            " oc.pos_cpg_code, " +
            " oc.pos_code, " +
            " CASE " +
            " WHEN  c.cpg_code IS NOT NULL THEN c.cpg_name " +
            " WHEN  gc.cpg_code IS NOT NULL THEN gc.cpg_name " +
            " ELSE '' " +
            " END AS cpgName, " +
            " CASE " +
            " WHEN  c.cpg_code IS NOT NULL THEN c.cpg_code " +
            " WHEN  gc.cpg_code IS NOT NULL THEN gc.cpg_code " +
            " ELSE '' " +
            " END AS cpgCode, " +

            " oc.`status`, " +
            " oc.create_user, " +
            " oc.create_time, " +
            " oc.update_user, " +
            " oc.update_time  " +
            "FROM " +
            " gv_pos_cpg oc " +
            " LEFT JOIN gv_cpg c ON oc.cpg_code = c.cpg_code" +
            " LEFT JOIN gc_cpg gc ON oc.cpg_code = gc.cpg_code" +
            " WHERE oc.pos_code = #{machineId} ")
    List<PosCpgResponse> queryPosCpgListByMachineId(String machineId);
}
