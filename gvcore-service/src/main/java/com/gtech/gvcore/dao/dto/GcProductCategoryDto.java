package com.gtech.gvcore.dao.dto;

import java.util.Date;

import lombok.Data;

@Data
public class GcProductCategoryDto {
    /**
     * id
     */
    private Long id;

    /**
     * 商品类别编码
     */
    private String productCategoryCode;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 发行方编码
     */
    private String issuerCode;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 状态,0:禁用,1:启用
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
} 