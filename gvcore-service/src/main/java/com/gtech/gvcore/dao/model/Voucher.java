
package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Table(name = "gv_voucher")
@ApiModel(value = "Voucher)", description = "")
public class Voucher implements Serializable {
    private static final long serialVersionUID = 388278488873621210L;

    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_VOUCHER_BATCH_CODE = "voucherBatchCode";
    public static final String C_BOOKLET_CODE = "bookletCode";
    public static final String C_VOUCHER_CODE = "voucherCode";
    public static final String C_VOUCHER_CODE_NUM = "voucherCodeNum";
    public static final String C_CPG_CODE = "cpgCode";
    public static final String C_MOP_CODE = "mopCode";
    public static final String C_DENOMINATION = "denomination";
    public static final String C_VOUCHER_PIN = "voucherPin";
    public static final String C_VOUCHER_BARCODE = "voucherBarcode";
    public static final String C_VOUCHER_EFFECTIVE_DATE = "voucherEffectiveDate";
    public static final String C_STATUS = "status";
    public static final String C_VOUCHER_STATUS = "voucherStatus";
    public static final String C_CIRCULATION_STATUS = "circulationStatus";
    public static final String C_VOUCHER_ACTIVE_CODE = "voucherActiveCode";
    public static final String C_VOUCHER_ACTIVE_URL = "voucherActiveUrl";
    public static final String C_VOUCHER_USED_TIME = "voucherUsedTime";
    public static final String C_VOUCHER_OWNER_TYPE = "voucherOwnerType";
    public static final String C_VOUCHER_OWNER_CODE = "voucherOwnerCode";
    public static final String C_PERMISSION_CODE = "permissionCode";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_SALES_OUTLET = "salesOutlet";
    public static final String C_USED_OUTLET = "usedOutlet";
    public static final String C_SALES_TIME = "salesTime";
    public static final String C_USED_TIME = "usedTime";

    @Id
    private Long id;

    @Column(name = "issuer_code")
    private String issuerCode;
    
    @Column(name = "voucher_batch_code")
    private String voucherBatchCode;
    
    @Column(name = "booklet_code")
    private String bookletCode;

    @Column(name = "booklet_code_num")
    private Long bookletCodeNum;
    
    @Column(name = "voucher_code")
    private String voucherCode;

    @Column(name = "voucher_code_num")
    private Long voucherCodeNum;
    
    @Column(name = "cpg_code")
    private String cpgCode;


    @Column(name = "mop_code")
    private String mopCode;
    
    @Column(name = "denomination")
    private BigDecimal denomination;
    
    @Column(name = "voucher_pin")
    private String voucherPin;
    
    @Column(name = "voucher_barcode")
    private String voucherBarcode;
    
    @Column(name = "voucher_effective_date")
    private Date voucherEffectiveDate;
    
    @Column(name = "`status`")
    private Integer status;

    @Column(name = "voucher_status")
    private Integer voucherStatus;

    @Column(name = "circulation_status")
    private Integer circulationStatus;

    @Column(name = "voucher_active_code")
    private String voucherActiveCode;
    
    @Column(name = "voucher_active_url")
    private String voucherActiveUrl;
    
    @Column(name = "voucher_used_time")
    private Date voucherUsedTime;

    @Column(name = "voucher_owner_code")
    private String voucherOwnerCode;

    @Column(name = "voucher_owner_type")
    private String voucherOwnerType;
    
    @Column(name = "permission_code")
    private String permissionCode;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "update_time")
    private Date updateTime;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "sales_time")
    private Date salesTime;

    @Column(name = "sales_outlet")
    private String salesOutlet;

    @Column(name = "used_time")
    private Date usedTime;

    @Column(name = "used_outlet")
    private String usedOutlet;

}
