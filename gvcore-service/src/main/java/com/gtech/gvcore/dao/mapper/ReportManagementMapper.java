package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.reportmanagement.QueryReportManagementRequest;
import com.gtech.gvcore.common.response.reportmanagement.QueryReportManagementResponse;
import com.gtech.gvcore.dao.model.ReportManagement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/11 17:42
 */

@Mapper
public interface ReportManagementMapper extends GTechBaseMapper<ReportManagement> {

    List<QueryReportManagementResponse> queryReportManagements(@Param("reportManagementRequest") QueryReportManagementRequest reportManagementRequest);

    ReportManagement selectByCode(@Param("reportManagementCode") String reportManagementCode);

}
