package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/8 15:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_cancel_voucher_receive")
@ApiModel(value = "CancelVoucherReceive)", description = "")
public class CancelVoucherReceive {

    @Id
    private Long id;
    @Column(name = "cancel_receive_code")
    private String cancelReceiveCode;
    @Column(name = "receive_code")
    private String receiveCode;
    @Column(name = "cpg_code")
    private String cpgCode;
    @Column(name = "voucher_start_no")
    private String voucherStartNo;
    @Column(name = "voucher_end_no")
    private String voucherEndNo;
    @Column(name = "booklet_start_no")
    private String bookletStartNo;
    @Column(name = "booklet_end_no")
    private String bookletEndNo;
    @Column(name = "outbound_code")
    private String outboundCode;
    @Column(name = "inbound_code")
    private String inboundCode;
    @Column(name = "denomination")
    private BigDecimal denomination;
    @Column(name = "received_num")
    private Integer receivedNum;
    @Column(name = "cancel_date")
    private Date cancelDate;

}
