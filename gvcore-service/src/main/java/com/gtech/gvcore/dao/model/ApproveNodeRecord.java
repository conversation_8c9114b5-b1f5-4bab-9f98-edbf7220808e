package com.gtech.gvcore.dao.model;


import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gv_approve_node_record")
public class ApproveNodeRecord implements Serializable {

    private static final long serialVersionUID = -7179756915734340614L;

    public static final String C_BUSINESS_CODE = "businessCode";
    public static final String C_APPROVE_TYPE = "releaseApproveAmountType";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_ID = "id";


    @Id
    private Long id;
    @Column(name = "approve_node_record_code")
    private String approveNodeRecordCode;
    @Column(name = "release_approve_amount_type")
    private String releaseApproveAmountType;
    @Column(name = "business_code")
    private String businessCode;
    @Column(name = "note")
    private String note;
    @Column(name = "status")
    private Boolean status;
    @Column(name = "release_approve_node_name")
    private Integer releaseApproveNodeName;
    @Column(name = "approve_user")
    private String approveUser;
    @Column(name = "approve_role_code")
    private String approveRoleCode;
    @Column(name = "next_role_code")
    private String nextRoleCode;

    /**
     * delete status, 0:not deleted, 1: deleted
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "issuer_code")
    private String issuerCode;


    public ApproveNodeRecord(String approveNodeRecordCode) {
        this.approveNodeRecordCode = approveNodeRecordCode;
    }
}
