package com.gtech.gvcore.dao.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@Table(name = "gv_cpg")
public class Cpg {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * cpg code,data UNIQUE KEY
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * cpg name
     */
    @Column(name = "cpg_name")
    private String cpgName;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * cpg type code
     */
    @Column(name = "cpg_type_code")
    private String cpgTypeCode;

    /**
     * grace periods
     */
    @Column(name = "grace_periods")
    private Integer gracePeriods;

    /**
     * effective years
     */
    @Column(name = "effective_years")
    private Integer effectiveYears;

    /**
     * effective month
     */
    @Column(name = "effective_month")
    private Integer effectiveMonth;

    /**
     * effective day
     */
    @Column(name = "effective_day")
    private Integer effectiveDay;

    /**
     * effective hour
     */
    @Column(name = "effective_hour")
    private Integer effectiveHour;

    /**
     * currency code
     */
    @Column(name = "currency_code")
    private String currencyCode;

    /**
     * denomination
     */
    private BigDecimal denomination;

    /**
     * article mop code
     */
    @Column(name = "article_mop_code")
    private String articleMopCode;

    /**
     * Number of Vouchers per Booklet
     */
    @Column(name = "booklet_voucher_num")
    private Integer bookletVoucherNum;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;


    @Column(name = "disable_generation")
    private String disableGeneration;

    public static final String C_CPG_CODE = "cpgCode";


}