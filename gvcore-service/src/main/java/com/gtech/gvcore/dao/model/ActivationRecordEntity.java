package com.gtech.gvcore.dao.model;

import com.gtech.commons.dao.entity.GTechBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ActivationRecordEntity", description = "礼品卡激活记录")
public class ActivationRecordEntity extends GTechBaseEntity {
    
    private String cardNumber;
    private String activationCode;
    private String activatedBy;
    private String channel;
    private LocalDateTime activatedAt;
    private Integer status;
    private String remark;
} 