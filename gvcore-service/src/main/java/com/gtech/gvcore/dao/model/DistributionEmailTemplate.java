package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @ClassName DistributionEmailTemplate
 * @Description 分发系统-分发邮件模板
 * <AUTHOR>
 * @Date 2022/7/5 14:33
 * @Version V1.0
 **/
@Getter
@Setter
@Table(name = "gv_distribution_email_template")
public class DistributionEmailTemplate {

    public static final String C_TEMPLATE_CODE = "templateCode";
    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_STATUS = "status";
    public static final String C_TEMPLATE_TYPE = "templateType";
    public static final String C_CREATE_TIME = "createTime";

    @Id
    private Long id;

    /**
     * 所属Customer
     */
    @Column(name = "customer_code")
    private String customerCode;

    /**
     * 模板编码
     */
    @Column(name = "template_code")
    private String templateCode;

    /**
     * 模板名称
     */
    @Column(name = "template_name")
    private String templateName;

    /**
     * 模板类型 Individual/Bulk
     * @see com.gtech.gvcore.common.enums.DisEmailTemplateTypeEnum
     */
    @Column(name = "template_type")
    private Integer templateType;

    /**
     * 邮件主题
     */
    @Column(name = "subject")
    private String subject;

    /**
     * 邮件状态 0-invalid , 1-valid
     * @see com.gtech.gvcore.common.enums.DisEmailTemplateStatusEnum
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 富文本
     */
    @Column(name = "rich_text")
    private String richText;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}
