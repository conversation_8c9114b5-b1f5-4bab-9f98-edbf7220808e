package com.gtech.gvcore.dao.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Accessors(chain = true)
@Table(name = "gv_issue_handling")
public class IssueHandling {
    public static final String C_APPROVE_NOTES = "approveNotes";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_PERMISSION_CODE = "permissionCode";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER_EMAIL = "createUserEmail";
    public static final String C_RESULT = "result";
    public static final String C_COUNT_FAILED = "countFailed";
    public static final String C_PROCESS_STATUS = "processStatus";
    public static final String C_COUNT_VOUCHER = "countVoucher";
    public static final String C_REMARKS = "remarks";
    public static final String C_UPLOADED_FILE_URL = "uploadedFileUrl";
    public static final String C_UPLOADED_FILE_NAME = "uploadedFileName";
    public static final String C_UPLOADED_FILE_TYPE = "uploadedFileType";
    public static final String C_ISSUE_TYPE = "issueType";
    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_ISSUE_HANDLING_CODE = "issueHandlingCode";
    public static final String C_ID = "id";

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * issue handling code.
     */
    @Column(name = "issue_handling_code")
    private String issueHandlingCode;

    /**
     * Issuer code.
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * issuer type: cancel_sales、cancel_redeem、bulk_active、...
     */
    @Column(name = "issue_type")
    private String issueType;

    /**
     * report file type：csv
     */
    @Column(name = "uploaded_file_type")
    private String uploadedFileType;

    /**
     * uploaded file name
     */
    @Column(name = "uploaded_file_name")
    private String uploadedFileName;

    /**
     * uploaded file url
     */
    @Column(name = "uploaded_file_url")
    private String uploadedFileUrl;

    /**
     * request remarks
     */
    private String remarks;

    /**
     * count of voucher
     */
    @Column(name = "count_voucher")
    private Integer countVoucher;

    /**
     * process status, 0:created,1:processing,2:success,3:failed
     */
    @Column(name = "process_status")
    private Integer processStatus;

    /**
     * count of failed
     */
    @Column(name = "count_failed")
    private Integer countFailed;

    /**
     * result
     */
    private String result;

    /**
     * create user email
     */
    @Column(name = "create_user_email")
    private String createUserEmail;

    /**
     * status, 0:create, 1:approve, 2:execute,3:cancel
     */
    private Integer status;

    /**
     * permission code
     */
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;


    @Column(name = "approve_notes")
    private String approveNotes;

}