package com.gtech.gvcore.dao.dto;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

@Data
public class VoucherReceiveDto {

    /**
     * voucher receive code
     */
    private String voucherReceiveCode;

    /**
     * cpg code 
     */
    private String cpgCode;

    /**
     * voucher start NO
     */
    private String voucherStartNo;

    /**
     * voucher end NO
     */
    private String voucherEndNo;

    /**
     * denomination
     */
    private BigDecimal denomination;

    /**
     * received number of vouchers
     */
    private Integer receivedNum;

    /**
     * booklet start NO
     */
    private String bookletStartNo;

    /**
     * booklet end NO
     */
    private String bookletEndNo;

    /**
     * create user
     */
    private String createUser;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update user
     */
    private String updateUser;

    /**
     * update time
     */
    private Date updateTime;

	private String sourceDataCode;

	private String issuerCode;

	private String sourceType;

	private String articleCode;

	private String purchaseOrderNo;
}