package com.gtech.gvcore.dao.dto;

import java.util.List;

import com.gtech.gvcore.dao.model.TransactionData;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * <AUTHOR>
 * @date 2022年5月5日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TransactionDataDto extends TransactionData {

    /**
     * 
     */
    private static final long serialVersionUID = 6808017518142460516L;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    private List<String> transactionTypeList;

}


