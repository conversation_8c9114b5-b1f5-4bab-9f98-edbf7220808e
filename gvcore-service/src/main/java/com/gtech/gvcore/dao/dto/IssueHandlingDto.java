package com.gtech.gvcore.dao.dto;

import java.util.Date;

import com.gtech.gvcore.dao.model.IssueHandling;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月6日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IssueHandlingDto extends IssueHandling {
	
	private Integer oldStatus;
	
	private Integer oldProcessStatus;
	
	private Date createTimeStart;
	
	private Date createTimeEnd;

}


