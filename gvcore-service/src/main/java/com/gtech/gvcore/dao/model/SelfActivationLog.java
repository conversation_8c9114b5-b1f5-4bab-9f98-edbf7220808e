package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * Self-activation log entity for delayed activation functionality
 * Records the lifecycle of activation tasks
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_self_activation_log")
@ApiModel(value = "SelfActivationLog", description = "Self-activation task log table")
public class SelfActivationLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Primary key
     */
    @Id
    private Long id;
    
    /**
     * Customer order code - core business association key
     */
    @Column(name = "customer_order_code")
    private String customerOrderCode;
    
    /**
     * Unique activation token in the activation URL
     */
    @Column(name = "activation_token")
    private String activationToken;
    
    /**
     * Token status: PENDING, ACTIVATED, EXPIRED
     */
    @Column(name = "token_status")
    private String tokenStatus;
    
    /**
     * Token expiry time (e.g., 30 days)
     */
    @Column(name = "token_expiry_at")
    private Date tokenExpiryAt;
    
    /**
     * Customer email receiving activation email
     */
    @Column(name = "customer_email")
    private String customerEmail;
    
    /**
     * Create time
     */
    @Column(name = "created_at")
    private Date createdAt;
    
    /**
     * Update time
     */
    @Column(name = "updated_at")
    private Date updatedAt;
    
    // Constants for column names
    public static final String C_CUSTOMER_ORDER_CODE = "customerOrderCode";
    public static final String C_ACTIVATION_TOKEN = "activationToken";
    public static final String C_TOKEN_STATUS = "tokenStatus";
    public static final String C_TOKEN_EXPIRY_AT = "tokenExpiryAt";
    public static final String C_CUSTOMER_EMAIL = "customerEmail";
    public static final String C_CREATED_AT = "createdAt";
    public static final String C_UPDATED_AT = "updatedAt";
}
