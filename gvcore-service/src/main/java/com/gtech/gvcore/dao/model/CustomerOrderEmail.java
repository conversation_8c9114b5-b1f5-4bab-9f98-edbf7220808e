package com.gtech.gvcore.dao.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "gv_customer_order_email")
@Accessors(chain = true)
public class CustomerOrderEmail {

    @Id
    private Long id;

    @Column(name = "email_code")
    private String emailCode;

    @Column(name = "customer_order_code")
    private String customerOrderCode;

    @Column(name = "email_address")
    private String emailAddress;

    @Column(name = "send_status")
    private Integer sendStatus;

    @Column(name = "send_time")
    private Date sendTime;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "excel_file_url")
    private String excelFileUrl;

    @Column(name = "secret_code")
    private String secretCode;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

}
