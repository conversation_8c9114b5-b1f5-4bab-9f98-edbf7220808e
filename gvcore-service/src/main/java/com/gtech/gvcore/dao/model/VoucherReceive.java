package com.gtech.gvcore.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "gv_voucher_receive")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoucherReceive {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * voucher receive code
     */
    @Column(name = "voucher_receive_code")
    private String voucherReceiveCode;

    /**
     * source type:  generate, allocation
     */
    @Column(name = "source_type")
    private String sourceType;

    /**
     * source data code
     */
    @Column(name = "source_data_code")
    private String sourceDataCode;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

	@Column(name = "receiver_code")
	private String receiverCode;

    /**
     * outbound name
     */
    @Column(name = "outbound")
    private String outbound;

    /**
     * inbound name
     */
    @Column(name = "inbound")
    private String inbound;


    @Column(name = "outbound_code")
    private String outboundCode;

    @Column(name = "inbound_code")
    private String inboundCode;

    /**
     * Total number of vouchers
     */
    @Column(name = "voucher_num")
    private Integer voucherNum;

    /**
     * received number of vouchers
     */
    @Column(name = "received_num")
    private Integer receivedNum;

    /**
     * status, 0:Processing, 1:Completed
     */
    private Integer status;

    /**
     * Permission code.
     */
    @Column(name = "permission_code")
    private String permissionCode;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}