package com.gtech.gvcore.dao.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_distribution")
public class Distribution {

    public static final String C_DISTRIBUTION_CODE = "distributionCode";
    public static final String C_STATUS = "status";
    public static final String C_PROGRESS_STATUS = "progressStatus";
    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_CPG_CODE = "cpgCode";

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 分发编码，唯一标识符
     */
    @Column(name = "distribution_code")
    private String distributionCode;

    /**
     * 所属customer
     */
    @Column(name = "customer_code")
    private String customerCode;

    /**
     * 分发类型.Individual，Bulk
     *
     * @see com.gtech.gvcore.common.enums.DistributionTypeEnum
     */
    @Column(name = "distribution_type")
    private String distributionType;

    /**
     * 邮件模板编码
     */
    @Column(name = "email_template_code")
    private String emailTemplateCode;

    /**
     * 邮件主题
     */
    @Column(name = "email_subject")
    private String emailSubject;

    /**
     * 邮件模板富文本
     */
    @Column(name = "email_rich_text")
    private String emailRichText;

    /**
     * 分发状态.draft，distributing，distributed
     *
     * @see com.gtech.gvcore.common.enums.DistributionStatusEnum
     */
    @Column(name = "status")
    private String status;

    /**
     * 分发执行状态(区别于status,用于线程异步分发的标识).Unconfirmed,Submitted,Distributing,Distributed
     *
     * @see com.gtech.gvcore.common.enums.DistributionProgressStatusEnum
     */
    @Column(name = "progress_status")
    private String progressStatus;

    /**
     * 确认分发时间
     */
    @Column(name = "confirm_distribution_time")
    private Date confirmDistributionTime;

    /**
     * 实际分发开始时间
     */
    @Column(name = "distribute_time")
    private Date distributeTime;

    /**
     * create user code
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time.
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * latest update user.
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * latest update time.
     */
    @Column(name = "update_time")
    private Date updateTime;

}