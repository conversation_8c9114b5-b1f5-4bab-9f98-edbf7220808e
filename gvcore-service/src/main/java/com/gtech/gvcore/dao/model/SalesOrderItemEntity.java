package com.gtech.gvcore.dao.model;

import com.gtech.commons.dao.entity.GTechBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SalesOrderItemEntity", description = "礼品卡对外销售订单项")
public class SalesOrderItemEntity extends GTechBaseEntity {
    
    private Long orderId;           // 订单ID
    private String orderNumber;     // 订单编号
    private String cardType;        // 卡类型
    private Integer quantity;       // 数量
    private BigDecimal unitPrice;   // 单价
    private BigDecimal faceValue;   // 面值
    private BigDecimal subtotal;    // 小计
    private String remark;          // 备注
} 