package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.GcReportTempLiabilityDStructure;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @ClassName ReportTempLiabilityDStructureMapper
 * @Description ReportTempLiabilityDStructure
 * <AUTHOR>
 * @Date 2023/4/18 15:34
 * @Version V1.0
 **/
@Mapper
public interface GcReportTempLiabilityDStructureMapper extends GTechBaseMapper<GcReportTempLiabilityDStructure> {

    int insertDetail(@Param("tableCode") String tableCode,
                     @Param("detail") GcReportTempLiabilityDStructure detail);

    void insertBatch(@Param("tableCode") String tableCode,
                     @Param("detailList") List<GcReportTempLiabilityDStructure> detailList);
}
