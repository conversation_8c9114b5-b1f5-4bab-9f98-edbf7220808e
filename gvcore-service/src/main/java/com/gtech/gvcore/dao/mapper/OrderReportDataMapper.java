package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.OrderReportData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName OrderReportDataMapper
 * @Description 订单报表执行数据结果集mapper
 * <AUTHOR>
 * @Date 2022/8/22 17:51
 * @Version V1.0
 **/
@Mapper
public interface OrderReportDataMapper extends GTechBaseMapper<OrderReportData> {

//    int insert(@Param("index") Integer index,@Param("data") OrderReportData data);
//    int insertSelective(@Param("index") Integer index,@Param("data") OrderReportData data);
//    OrderReportData selectOne(@Param("index")Integer index, @Param("data") OrderReportData data);

}
