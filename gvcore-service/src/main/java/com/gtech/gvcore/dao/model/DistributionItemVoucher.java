package com.gtech.gvcore.dao.model;

import com.gtech.gvcore.common.enums.DistributionItemVoucherStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gv_distribution_item_voucher")
public class DistributionItemVoucher {

    public static final String C_DISTRIBUTION_CODE = "distributionCode";
    public static final String C_DISTRIBUTION_ITEM_CODE = "distributionItemCode";
    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_VOUCHER_CODE = "voucherCode";
    public static final String C_CPG_CODE = "cpgCode";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_STATUS = "status";

    public static final String C_EMAIL_END_CUSTOMER_NAME = "emailEndCustomerName";
    public static final String C_EMAIL_ADDRESS = "emailAddress";



    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 分发编码
     */
    @Column(name = "distribution_code")
    private String distributionCode;

    /**
     * 分发item编码
     */
    @Column(name = "distribution_item_code")
    private String distributionItemCode;

    /**
     * 所属customer
     */
    @Column(name = "customer_code")
    private String customerCode;

    /**
     * Card program group code.
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * voucher code.
     */
    @Column(name = "voucher_code")
    private String voucherCode;

    /**
     * voucher number.
     */
    @Column(name = "voucher_number")
    private Long voucherNumber;

    /**
     * 邮箱编码
     */
    @Column(name = "email_address")
    private String emailAddress;

    /**
     * 邮箱用户名
     */
    @Column(name = "email_end_customer_name")
    private String emailEndCustomerName;

    /**
     * 券分发状态.Available,Distributing,Distributed,Fail
     *
     * @see DistributionItemVoucherStatusEnum
     */
    @Column(name = "status")
    private String status;

    /**
     * 失败描述
     */
    @Column(name = "error_msg")
    private String errorMsg;

    /**
     * 邮件id
     */
    @Column(name = "message_id")
    private String messageId;

    /**
     * create time.
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * latest update time.
     */
    @Column(name = "update_time")
    private Date updateTime;

}