

package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Table(name = "gv_outlet")
@ApiModel(value = "Outlet)", description = "outlet")
public class Outlet implements Serializable {
    private static final long serialVersionUID = -38266035063628118L;

	public static final String C_PARENT_OUTLET = "parentOutlet";
    public static final String C_OUTLET_CODE = "outletCode";
    public static final String C_OUTLET_NAME = "outletName";
    public static final String C_MERCHANT_CODE = "merchantCode";
    /*public static final String C_ISSUER_CODE = "issuerCode";*/
    public static final String C_SBU = "sbu";
    public static final String C_BUSINESS_OUTLET_CODE = "businessOutletCode";
    public static final String C_OUTLET_TYPE = "outletType";
    public static final String C_STATE_CODE = "stateCode";
    public static final String C_CITY_CODE = "cityCode";
    public static final String C_DISTRICT_CODE = "districtCode";
    public static final String C_ADDRESS1 = "address1";
    public static final String C_ADDRESS2 = "address2";
    public static final String C_PIN_CODE = "pinCode";
    public static final String C_FIRST_NAME = "firstName";
    public static final String C_LAST_NAME = "lastName";
    public static final String C_EMAIL = "email";
    public static final String C_PHONE = "phone";
    public static final String C_MOBILE = "mobile";
    public static final String C_ALERTNATE_EMAIL = "alertnateEmail";
    public static final String C_ALERTNATE_PHONE = "alertnatePhone";
    public static final String C_DESCRIPTIVE = "descriptive";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";
    
    
    
    /**
    * id
    */
    
    @Id
    private Long id;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "outlet_name")
    private String outletName;

    @Column(name = "merchant_code")
    private String merchantCode;

    /*@Column(name = "issuer_code")
    private String issuerCode;*/

    @Column(name = "sbu")
    private String sbu;

    @Column(name = "business_outlet_code")
    private String businessOutletCode;

    @Column(name = "outlet_type")
    private String outletType;
    
    @Column(name = "state_code")
    private String stateCode;
    
    @Column(name = "city_code")
    private String cityCode;
    
    @Column(name = "district_code")
    private String districtCode;
    
    @Column(name = "address1")
    private String address1;

    @Column(name = "address2")
    private String address2;

    @Column(name = "pin_code")
    private String pinCode;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "email")
    private String email;

    @Column(name = "phone")
    private String phone;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "alertnate_email")
    private String alertnateEmail;

    @Column(name = "alertnate_phone")
    private String alertnatePhone;

    @Column(name = "descriptive")
    private String descriptive;

    @Column(name = "`status`")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "parent_outlet")
    private String parentOutlet;


    
}
