package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Pos")
@Table(name = "gv_pos_account")
public class PosAccount implements Serializable {
    private static final long serialVersionUID = 4452099492791783555L;



    @Id
    private Long id;

    /**
     * Pos code
     */
    @Column(name = "pos_account_code")
    private String posAccountCode;

    /**
     * Issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;


    @Column(name = "outlet_type")
    private String outletType;

    @Column(name = "pos_account")
    private String posAccount;

    @Column(name = "pos_password")
    private String posPassword;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;



}
