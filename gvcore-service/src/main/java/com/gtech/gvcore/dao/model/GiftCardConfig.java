package com.gtech.gvcore.dao.model;

import com.gtech.commons.dao.entity.GTechBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GiftCardConfig", description = "礼品卡配置")
public class GiftCardConfig extends GTechBaseEntity {
    
    private String configType;    // 配置类型: WALLET_HOST/CONCEPT/BRAND/DENOMINATION
    private String code;          // 配置代码
    private String name;          // 配置名称
    private String description;   // 配置描述
    private Integer status;       // 状态: 0-禁用 1-启用
    
    // 配置类型常量
    public static final String TYPE_WALLET_HOST = "WALLET_HOST";
    public static final String TYPE_CONCEPT = "CONCEPT";
    public static final String TYPE_BRAND = "BRAND";
    public static final String TYPE_DENOMINATION = "DENOMINATION";
} 