package com.gtech.gvcore.external;

public class PostingXmlTemplate {

	private PostingXmlTemplate() {
	}

	public static final String GOODS_RECEIPT_XML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n"
			+ "<WPUWBW>\r\n"
			+ "    <IDOC BEGIN=\"1\">\r\n"
			+ "        <EDI_DC40 SEGMENT=\"1\">\r\n"
			+ "            <TABNAM>EDI_DC40</TABNAM>\r\n"
			+ "            <MANDT>999</MANDT>\r\n"
			+ "            <DOCREL>620</DOCREL>\r\n"
			+ "            <DIRECT>2</DIRECT>\r\n"
			+ "            <IDOCTYP>WPUWBW01</IDOCTYP>\r\n"
			+ "            <MESTYP>WPUWBW</MESTYP>\r\n"
			+ "            <SNDPOR>XML_PORT</SNDPOR>\r\n"
			+ "            <SNDPRT>KU</SNDPRT>\r\n"
			+ "            <SNDPRN>HO01</SNDPRN>\r\n"
			+ "            <RCVPOR>SAPMGP</RCVPOR>\r\n"
			+ "            <RCVPRT>LS</RCVPRT>\r\n"
			+ "            <RCVPRN>MGPCLNT999</RCVPRN>\r\n"
			+ "            <CREDAT>%s</CREDAT>\r\n"
			+ "            <CRETIM>%s</CRETIM>\r\n"
			+ "            <ARCKEY>HO01-%s</ARCKEY>\r\n"
			+ "        </EDI_DC40>\r\n"
			+ "        <E1WPG01 SEGMENT=\"1\">\r\n"
			+ "            <BELEGDATUM>%s</BELEGDATUM>\r\n"
			+ "            <BONNUMMER>%s-00003</BONNUMMER>\r\n"
			+ "            <E1WPG02 SEGMENT=\"1\">\r\n"
			+ "                <QUALVORG>0013</QUALVORG>\r\n"
			+ "                <REFERENZNR>%s</REFERENZNR>\r\n"
			+ "                <LITEM>00010</LITEM>\r\n"
			+ "                <QUALARTNR>ARTN</QUALARTNR>\r\n"
			+ "                <ARTNR>%s</ARTNR>\r\n"
			+ "                <BEWART>101</BEWART>\r\n"
			+ "                <MENGE>%s</MENGE>\r\n"
			+ "                <LGORT>L001</LGORT>\r\n"
			+ "                <KZBEW>B</KZBEW>\r\n"
			+ "                <VFDAT>00000000</VFDAT>\r\n"
			+ "            </E1WPG02>\r\n"
			+ "        </E1WPG01>\r\n"
			+ "    </IDOC>\r\n"
			+ "</WPUWBW>";
	
}
