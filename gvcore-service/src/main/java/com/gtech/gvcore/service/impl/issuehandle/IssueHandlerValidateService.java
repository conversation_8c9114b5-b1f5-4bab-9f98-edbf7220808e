package com.gtech.gvcore.service.impl.issuehandle;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherService;

import tk.mybatis.mapper.util.StringUtil;

public abstract class IssueHandlerValidateService implements IssueHandlerBaseService {

	protected static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(8, 20, 100, TimeUnit.SECONDS,
			new LinkedBlockingDeque<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

	@Lazy
	@Autowired
	protected VoucherService voucherService;
	@Autowired
	protected VoucherMapper voucherMapper;
	@Autowired
	protected OutletService outletService;
	
	@Autowired
	protected TransactionDataService transactionDataService;

	@Value("${gv.active.url:}")
	protected String activeUrl;

	public static Map<String,String>  outletMapFinal = new HashMap<>();

	public void checkIfExist(List<IssueHandlingDetails> details, IssueHandlingTypeEnum issueHandlingTypeEnum, String issuerCode) {

		if (CollectionUtils.isEmpty(details)) {
			return;
		}
		List<String> voucherCodeList = details.stream().filter(x-> x.getVoucherCode() != null && !x.getVoucherCode().isEmpty()).map(IssueHandlingDetails::getVoucherCode)
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(voucherCodeList)) {
			details.forEach(vo -> {
				vo.setResult("Voucher number is empty!");
				vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			});
			return;
		}
		List<String> newVoucherCodeList = details.stream().map(IssueHandlingDetails::getNewVoucherCode)
				.collect(Collectors.toList());
		voucherCodeList.addAll(newVoucherCodeList);
		//voucherCodeList去除空串
		voucherCodeList = voucherCodeList.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
		List<Voucher> voucherList = voucherService.queryByVoucherCodeList(issuerCode, voucherCodeList);
		if (CollectionUtils.isEmpty(voucherList)) {
			details.forEach(vo -> {
				vo.setResult("Voucher number doesn't exist!");
				vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			});
			return;
		}
		Map<String, Voucher> voucherMap = voucherList.stream().collect(Collectors.toMap(Voucher::getVoucherCode, Function.identity()));
		for (IssueHandlingDetails detail : details) {
			String voucherCode = detail.getVoucherCode();
			if (StringUtil.isEmpty(voucherCode)) {
				detail.setResult("Voucher number is empty!");
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			} else if (!voucherMap.containsKey(voucherCode)) {
				detail.setResult("Voucher number doesn't exist!");
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			} else if (StringUtil.isNotEmpty(detail.getNewVoucherCode())
					&& !voucherMap.containsKey(detail.getNewVoucherCode())) {
				detail.setResult("New voucher number doesn't exist!");
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			} else if (issueHandlingTypeEnum != null) {
				Voucher voucher = voucherMap.get(voucherCode);
				//检查卡券状态
				boolean flag = checkStatus(voucher, issueHandlingTypeEnum);


				if (!flag) {
					detail.setResult(getVoucherResult(voucher));
					detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
				}else {
					detail.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
					detail.setResult("Success");
				}

			}else {
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
				detail.setResult("Success");
			}

			//重新生成激活码类型，需要校验老激活码
			if (IssueHandlingTypeEnum.RESET_ACTIVATION.equals(issueHandlingTypeEnum) && detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.SUCCESS.code())) {
				if (StringUtil.isNotEmpty(detail.getOldActivationCode())) {
					String oldActivationCode = detail.getOldActivationCode();
					if (voucherMap.get(detail.getVoucherCode()).getVoucherActiveCode()!=null &&
							!voucherMap.get(detail.getVoucherCode()).getVoucherActiveCode().equals(oldActivationCode)) {
						detail.setResult("Old activation code doesn't exist!");
						detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
					}
				}
			}


		}
	}

	private boolean checkStatus(Voucher voucher, IssueHandlingTypeEnum issueHandlingTypeEnum) {

		//券不存在：Voucher doesn't exist.
		//券的activated和deactivated状态：Voucher has been {status}.
		//券过期：Voucher has expired.
		//其他情况，如刚刚创建created状态：Voucher status is incorrect and is "{status}".


		boolean flag = true;
		switch (issueHandlingTypeEnum) {
		case CANCEL_SALES:
  			//判断mop是vce还是vcr,如果是vce，就判断voucher的状态是不是VOUCHER_ACTIVATED或者VOUCHER_NEWLY_GENERATED,如果是vcr，就判断voucher的状态是不是VOUCHER_ACTIVATED
			if (GvcoreConstants.MOP_CODE_VCE.equals(voucher.getMopCode())) {
				//如果voucher的状态不是VOUCHER_ACTIVATED也不是VOUCHER_NEWLY_GENERATED的，就报错
				if (!VoucherStatusEnum.VOUCHER_ACTIVATED.getCode().equals(voucher.getStatus())
						&& !VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(voucher.getStatus())) {
					flag = false;
				}
			}else {
				if (!VoucherStatusEnum.VOUCHER_ACTIVATED.getCode().equals(voucher.getStatus())) {
					flag = false;
				}
			}
			break;
		case CANCEL_REDEEM:
			if (!VoucherStatusEnum.VOUCHER_USED.getCode().equals(voucher.getStatus())) {
				flag = false;
			}
			break;
		case BULK_REDEEM:
			if (!VoucherStatusEnum.VOUCHER_ACTIVATED.getCode().equals(voucher.getStatus())) {
				flag = false;
			}
			break;
		case BULK_REACTIVATE:
			if (GvcoreConstants.STATUS_DISABLE.intValue() != voucher.getVoucherStatus()) {
				flag = false;
			}
			break;
		case BULK_REISSUE:
			if (GvcoreConstants.STATUS_ENABLE.intValue() != voucher.getVoucherStatus()) {
				flag = false;
			}
			//如果状态是已使用校验不通过
			if (VoucherStatusEnum.VOUCHER_USED.getCode() == voucher.getStatus()) {
				flag = false;
			}

			if (voucher.getStatus() == VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode()) {
				//检查卡券是否已经被使用

				flag = false;
			}

			break;
		case BULK_ACTIVATION:
			if (!VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(voucher.getStatus())) {
				flag = false;
			}
			break;
		case BULK_DEACTIVATE:
			if (GvcoreConstants.STATUS_ENABLE.intValue() != voucher.getVoucherStatus()) {
				flag = false;
			}
			break;
		case RESET_ACTIVATION:
			if (!GvcoreConstants.MOP_CODE_VCE.equals(voucher.getMopCode())) {
				flag = false;
			}
			if (!VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(voucher.getStatus())) {
				flag = false;
			}
			break;
		default:
			break;
		}
		return flag;
	}

	private String getVoucherResult(Voucher voucher){

		/**
		 * 券不存在：Voucher doesn't exist.
		 *
		 * 券的activated和deactivated状态：Voucher has been {status}.
		 *
		 * 券过期：Voucher has expired.
		 *
		 * 其他情况，如刚刚创建created状态：Voucher status is incorrect and is "{status}".
		 */

		if(voucher.getVoucherStatus()== GvcoreConstants.STATUS_DISABLE){
			return "Voucher has been deactivated.";
		}else if (voucher.getStatus() == VoucherStatusEnum.VOUCHER_ACTIVATED.getCode()
				){
			return "Voucher has been activated.";
		}else if (voucher.getVoucherEffectiveDate().compareTo(new Date()) <0 ){
			//如果券的有效期小于当前时间，就是券过期
			return "Voucher has expired.";
		}else {
			return "Voucher status is incorrect and is "+VoucherStatusEnum.getByCode(voucher.getStatus());
		}

	}

	public void checkInvoiceNumber(List<IssueHandlingDetails> details,TransactionTypeEnum typeEnum) {
		if (CollectionUtils.isEmpty(details)) {
			return;
		}
		List<String> invoiceNoList = details.stream().filter(x-> x.getInvoiceNo() != null && !x.getInvoiceNo().isEmpty()).map(IssueHandlingDetails::getInvoiceNo)
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(invoiceNoList)) {
			details.forEach(vo -> {
				String result = "Invoice number is empty!";
				if (vo.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					vo.setResult(vo.getResult() + result);
				} else {
					vo.setResult(result);
				}
				vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			});
			return;
		}
		List<String> voucherCodeList = details.stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList());
		Map<String, String> invoiceMap = transactionDataService.queryVoucherInvoiceNo(voucherCodeList,typeEnum);
		if (invoiceMap.isEmpty()) {
			details.forEach(x->{
				x.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
			});

			return;
		}
		details.forEach(detail -> {
			String voucherCode = detail.getVoucherCode();
			String invoiceNo = detail.getInvoiceNo();
			if (invoiceMap.get(voucherCode) == null || !invoiceMap.get(voucherCode).equals(invoiceNo)) {
				String result = "Voucher number doesn't match the invoice number!";
				if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					detail.setResult(detail.getResult() + result);
				} else {
					detail.setResult(result);
				}
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			}
		});
	}



	public void checkApprovalCode(List<IssueHandlingDetails> details, TransactionTypeEnum transactionTypeEnum) {
		if (CollectionUtils.isEmpty(details)) {
			return;
		}
		List<String> approvalCodeList = details.stream().filter(x-> x.getApprovalCode() != null && !x.getApprovalCode().isEmpty()).map(IssueHandlingDetails::getApprovalCode)
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(approvalCodeList)) {
			details.forEach(vo -> {
				String result = "Approval Code is empty!";
				if (vo.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					vo.setResult(vo.getResult() + result);
				} else {
					vo.setResult(result);
				}
				vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			});
			return;
		}
		List<String> voucherCodeList = details.stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList());
		Map<String, String> approvalCodeMap = transactionDataService.queryVoucherApprovalCode(voucherCodeList,transactionTypeEnum);
		if (approvalCodeMap.isEmpty()) {
			details.forEach(x->{
				x.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
			});
			return;
		}
		details.forEach(detail -> {
			String voucherCode = detail.getVoucherCode();
			String approvalCode = detail.getApprovalCode();
			if (approvalCodeMap.get(voucherCode) == null || !approvalCodeMap.get(voucherCode).equals(approvalCode)) {
				String result = "Voucher number doesn't match the approval code!";
				if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					detail.setResult(detail.getResult() + result);
				} else {
					detail.setResult(result);
				}
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			}
		});
	}


	public void checkOutletName(List<IssueHandlingDetails> details, String issuerCode) {
		if (CollectionUtils.isEmpty(details)) {
			return;
		}
		List<String> outletNameList = details.stream().filter(x-> x.getOutletName() != null && !x.getOutletName().isEmpty()).map(IssueHandlingDetails::getOutletName).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(outletNameList)) {
			details.forEach(vo -> {
				String result = "Merchant outlet is empty!";
				if (vo.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					vo.setResult(vo.getResult() + result);
				} else {
					vo.setResult(result);
				}
				vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			});
			return;
		}
		List<Outlet> outletList = outletService.queryOutletByNames(outletNameList);
		Map<String, String> outletMap = outletList.stream().collect(Collectors.toMap(Outlet::getOutletName, Outlet::getOutletCode, (k1, k2) -> k1));
		outletMapFinal = outletMap;
		details.forEach(detail -> {
			String outletName = detail.getOutletName();
			String result = "";
			if (StringUtil.isEmpty(outletName)) {
				result = "Merchant outlet is empty!";
			} else if (outletMap.isEmpty() || StringUtil.isEmpty(outletMap.get(outletName))) {
				result = "Merchant outlet doesn't exist!";
			}
			if (!StringUtil.isEmpty(result)) {
				if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					detail.setResult(detail.getResult() + result);
				} else {
					detail.setResult(result);
				}
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			}
		});
	}

	@Override
	public void afterExecute(IssueHandling issueHandling) {
	}

}
