package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.gvcore.dao.model.ProductCategoryDisscountDetails;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface ProductCategoryDisscountDetailsService {

    /**
     * 
     * <AUTHOR>
     * @param productCategoryDisscountCode
     * @param deleteStatus
     * @return
     * @date 2022年2月24日
     */
    List<ProductCategoryDisscountDetails> queryByProductCategoryDisscountCode(String productCategoryDisscountCode,
            Integer deleteStatus);

    /**
     * 
     * <AUTHOR>
     * @param list
     * @return
     * @date 2022年2月25日
     */
    int insertList(List<ProductCategoryDisscountDetails> list);

    /**
     * 
     * <AUTHOR>
     * @param detail
     * @return
     * @date 2022年2月25日
     */
    int updateById(ProductCategoryDisscountDetails detail);

    /**
     * 
     * <AUTHOR>
     * @param detail
     * @return
     * @date 2022年2月25日
     */
    int deleteStatusByPrimaryKey(ProductCategoryDisscountDetails detail);

}
