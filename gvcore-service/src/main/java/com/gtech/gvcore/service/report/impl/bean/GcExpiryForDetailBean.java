package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description: Gift Card Expiry Report Detail Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcExpiryForDetailBean {

    /**
     * Merchant Name
     */
    @ExcelProperty(value = "Merchant Name")
    private String merchant;

    /**
     * Gift Card Number
     */
    @ExcelProperty(value = "Gift Card Number")
    private String voucherNumber;

    /**
     * Gift Card Status
     */
    @ExcelProperty(value = "Gift Card Status")
    private String cardStatus;

    /**
     * Gift Card Program Group
     */
    @ExcelProperty(value = "Gift Card Program Group")
    private String vpg;

    /**
     * Purchase Date
     */
    @ExcelProperty(value = "Purchase Date")
    private String purchaseDate;

    /**
     * Activation Date
     */
    @ExcelProperty(value = "Activation Date")
    private String activationDate;

    /**
     * Expiry Date
     */
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    /**
     * Total Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    /**
     * Customer Name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * Invoice Number
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;
}
