package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.issuehandling.GetIssueHandlingRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.giftcard.domain.model.GcActivationExtensionRecord;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.domain.repository.GcActivationExtensionRepository;
import com.gtech.gvcore.giftcard.domain.repository.GiftCardRepository;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.OutletService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;

@Service
@Slf4j
public class GcIssueHandlerChangeExpiryService extends GcIssueHandlerValidateService implements IssueHandlerBaseService {

    @Autowired
    private GcActivationExtensionRepository gcActivationExtensionRepository;

    @Autowired
    private OutletService outletService;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private GiftCardRepository giftCardRepository;

    @Autowired
    private IssueHandlingService issueHandlingService;


    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_CHANGE_EXPIRY;
    }

    @Override
    public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
        checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkInvoiceNumberAndApprovalCode(details, getIssueHandlingType());
        return details;
    }

    @Override
    public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
        validate(details, issuerCode);
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        CountDownLatch downlatch = new CountDownLatch(details.size());
        List<IssueHandlingDetails> returnList = new CopyOnWriteArrayList<>();
        for (IssueHandlingDetails issueHandlingDetails : details) {
            EXECUTOR.execute(() -> {
                try {
                    returnList.add(issueHandlingDetails);
                    makeIssueHandling(issueHandlingDetails);
                } catch (Exception e) {
                    String msg = e.getMessage();
                    log.error(e.getMessage(), e);
                    issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                    issueHandlingDetails.setResult(msg.length() > 500 ? msg.substring(0, 499) : msg);
                } finally {
                    downlatch.countDown();
                }
            });
        }
        try {
            downlatch.await();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            Thread.currentThread().interrupt();
        }
        return returnList;
    }

    public void makeIssueHandling(IssueHandlingDetails issueHandlingDetails) {
        if (IssueHandlingProcessStatusEnum.FAILED.code().equals(issueHandlingDetails.getProcessStatus())) {
            return;
        }
        String voucherCode = issueHandlingDetails.getVoucherCode();
        GiftCardEntity giftCard = new GiftCardEntity();
        giftCard.setCardNumber(voucherCode);
        GiftCardEntity existingCard = giftCardMapper.selectOne(giftCard);

        if (existingCard == null) {
            issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            issueHandlingDetails.setResult(ResultErrorCodeEnum.DATA_MISS.code() + "【" + voucherCode + "】");
            return;
        }
        GiftCard card = BeanCopyUtils.jsonCopyBean(existingCard, GiftCard.class);
        try {
            card.extendExpiryTime();
        } catch (Exception e) {
            issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            issueHandlingDetails.setResult(e.getMessage() + ", Card Number【" + voucherCode + "】");
            return;
        }

        // 更新过期时间
        Example updateExample = new Example(Voucher.class);
        updateExample.createCriteria().andEqualTo(Voucher.C_VOUCHER_CODE, voucherCode);
        int count = giftCardRepository.updateCard(card);
        if (count == 0) {
            issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            issueHandlingDetails.setResult(ResultErrorCodeEnum.DATA_MISS.code() + "【" + voucherCode + "】");
        } else {
            issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
            // 直接保存延长激活期限记录
            saveExtensionRecord(issueHandlingDetails, existingCard, existingCard.getActivationDeadline(), card.getActivationDeadline());
        }
    }

    /**
     * 保存延长激活期限记录
     */
    private void saveExtensionRecord(IssueHandlingDetails details,
                                     GiftCardEntity giftCard,
                                     Date oldExpiryTime,
                                     Date newExpiryTime) {
        GetOutletRequest getOutletRequest = new GetOutletRequest();
        getOutletRequest.setOutletCode(giftCard.getSalesOutlet());
        OutletResponse outlet = outletService.getOutlet(getOutletRequest);

        // 创建延长激活期限记录
        GcActivationExtensionRecord extensionRecord = GcActivationExtensionRecord.builder()
                .cardNumber(details.getVoucherCode())
                .extensionCode(gvCodeHelper.generateTransactionCode())
                .extensionTime(new Date())
                .oldActivationDeadline(oldExpiryTime)
                .newActivationDeadline(newExpiryTime)
                .issuerCode(giftCard.getIssuerCode())
                .outletCode(giftCard.getSalesOutlet())
                .merchantCode(outlet.getMerchantCode())
                .invoiceNumber(details.getInvoiceNo())
                .approvalCode(details.getApprovalCode())
                .notes((issueHandlingService.getIssueHandling(GetIssueHandlingRequest.builder()
                                .issueHandlingCode(details.getIssueHandlingCode()).build())
                        .getData().getRemarks()))
                .source("Issue Handling")
                .cpgCode(giftCard.getCpgCode())
                .denomination(giftCard.getDenomination())
                .posCode(null) // Issue handling doesn't have POS context
                .build();

        gcActivationExtensionRepository.save(extensionRecord);
        log.info("Successfully saved extension record for card: {}", details.getVoucherCode());
    }
}
