package com.gtech.gvcore.service.impl;

import com.gtech.gvcore.dao.dto.QueryCpgDto;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.VoucherRequestDetailsMapper;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.service.VoucherRequestDetailsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年3月10日
 */
@Service
public class VoucherRequestDetailsServiceImpl implements VoucherRequestDetailsService {

    @Autowired
    private VoucherRequestDetailsMapper voucherRequestDetailsMapper;

    @Autowired
    private CpgMapper cpgMapper;

    @Override
    public List<VoucherRequestDetails> queryByVoucherRequestCode(String voucherRequestCode) {

        if (StringUtils.isBlank(voucherRequestCode)) {
            return Collections.emptyList();
        }
        VoucherRequestDetails details = new VoucherRequestDetails();
        details.setVoucherRequestCode(voucherRequestCode);
        List<VoucherRequestDetails> list = voucherRequestDetailsMapper.select(details);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public Map<String, List<String>> queryDenominationByVoucherRequestCodeList(
            List<String> voucherRequestCodeList) {

        List<QueryCpgDto> list = voucherRequestDetailsMapper
                .queryCpgByVoucherRequestCodeList(voucherRequestCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(QueryCpgDto::getVoucherRequestCode,
                Collectors.mapping(QueryCpgDto::getCpgName, Collectors.toList())));
    }

}
