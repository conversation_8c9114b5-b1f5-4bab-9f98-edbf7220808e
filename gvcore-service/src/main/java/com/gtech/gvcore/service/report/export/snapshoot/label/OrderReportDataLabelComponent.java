package com.gtech.gvcore.service.report.export.snapshoot.label;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.export.snapshoot.QueryOrderReportDataResponse;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName OrderReportDataLabelHelper
 * @Description 报表数据表头帮助类
 * <AUTHOR>
 * @Date 2022/10/8 14:38
 * @Version V1.0
 **/
@Slf4j
@Component
public class OrderReportDataLabelComponent implements ApplicationContextAware {


    /**
     * label map
     */
    private static final Map<Class<?>, List<QueryOrderReportDataResponse.LabelBean>> CLASS_LABEL_MAP_DEFAULT = new HashMap<>();
    private static final Map<Class<?>, List<QueryOrderReportDataResponse.LabelBean>> CLASS_LABEL_MAP_DYNAMIC = new HashMap<>();

    /**
     * 报表结果类型映射
     */
    private static final Map<Integer, Class<?>> REPORT_DATA_TYPE_MAP = new HashMap<>();

    @Autowired
    private OrderReportLabelDynamicHelper dynamicHelper;

    public static Class<?> getResultType(Integer reportType) {

        return REPORT_DATA_TYPE_MAP.get(reportType);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {

        Collection<ReportLabelSupport> values = applicationContext.getBeansOfType(ReportLabelSupport.class).values();

        //所有报表返回结果集类型
        List<Map.Entry<ReportExportTypeEnum, Class<?>>> list = new ArrayList<>();
        values.stream()
                .map(ReportLabelSupport::getResultDateType)
                .map(Map::entrySet)
                .forEach(list::addAll);

        list.forEach(e -> {
            //设置报表实现对应的返回类型
            final ReportExportTypeEnum reportExportTypeEnum = e.getKey();
            final Class<?> exportDataClass = e.getValue();

            if (null == reportExportTypeEnum || null == exportDataClass) return;

            log.info("report label 快照数据类型, enum => {} class => {}, ", reportExportTypeEnum, exportDataClass);

            REPORT_DATA_TYPE_MAP.put(reportExportTypeEnum.getExportType(), exportDataClass);

            log.info("report label 生成label信息, enum => {} class => {}, ", reportExportTypeEnum, exportDataClass);

            //设置报表label
            settingReportLabel(exportDataClass);
        });

    }

    private static void settingReportLabel(Class<?> exportDataClass) {//NOSONAR

        List<Field> declaredFields = Arrays.stream(exportDataClass.getDeclaredFields())
                .filter(f -> !f.isSynthetic())
                .collect(Collectors.toList());

        final List<QueryOrderReportDataResponse.LabelBean> labelBeanList = new ArrayList<>();
        Map<Class<?>, List<QueryOrderReportDataResponse.LabelBean>> resultMap = CLASS_LABEL_MAP_DEFAULT;

        for (Field field : declaredFields) {

            field.setAccessible(true);//NOSONAR

            final String value = field.getName();

            //annotation
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            ReportLabel reportLabel = field.getAnnotation(ReportLabel.class);

            // 报表表头配置不为空
            if (null != reportLabel && reportLabel.value().length > 0) {

                if (resultMap == CLASS_LABEL_MAP_DEFAULT && reportLabel.dynamic()) resultMap = CLASS_LABEL_MAP_DYNAMIC;

                //获得报表表头信息
                String[] labelArray = reportLabel.value();

                //初始化父级表头
                QueryOrderReportDataResponse.LabelBean baseLabel = null;

                //循环表头信息
                for (int l = 0; l < labelArray.length; l++) {

                    //生成当前表头的实体
                    QueryOrderReportDataResponse.LabelBean nowLabel = new QueryOrderReportDataResponse.LabelBean()
                            .setReportLabel(reportLabel)
                            .setLabel(labelArray[l])
                            .setIgnoreBlankLabelColumn(reportLabel.ignoreBlankLabelColumn());


                    if (labelArray.length == (l + 1)) {
                        //如果是最后一个表头节点

                        //绑定的当前字段名赋予表头关系
                        QueryOrderReportDataResponse.LabelBean labelBean = nowLabel.setValue(value)
                                .setAction(reportLabel.action().toResult());

                        //如果父级 表头为空则 直接追加进入集合
                        if (null == baseLabel) labelBeanList.add(labelBean);

                            //如果父级表头不为空则追加进入父级表头
                        else baseLabel.addChildrenNode(labelBean);


                    } else {
                        //非最后一个表头节点


                        if (null == baseLabel) {
                            //父级表头不为空

                            //循环父级表头所有子节点是否与当前重合 重合则直接设置当前父级节点为该重复节点
                            for (QueryOrderReportDataResponse.LabelBean lLabel : labelBeanList) {
                                if (lLabel.getLabel().equals(nowLabel.getLabel())) baseLabel = lLabel;
                            }

                            //父级节点没有在上次循环初始化 [当前不存在对应的父级节点]
                            if (null == baseLabel) {
                                baseLabel = nowLabel;
                                labelBeanList.add(nowLabel);
                            }
                        } else {

                            if (CollectionUtils.isNotEmpty(baseLabel.getChildren())) {
                                //当前父级节点存在子节点

                                //循环子节点找到对应的叶子节点 并且设置当前父级节点为该叶子节点
                                for (QueryOrderReportDataResponse.LabelBean blcNowLabel : baseLabel.getChildren()) {
                                    if (blcNowLabel.getLabel().equals(nowLabel.getLabel())) baseLabel = blcNowLabel;
                                }
                            }

                            //循环后找到叶子节点 跳过当前节点
                            if (baseLabel.getLabel().equals(nowLabel.getLabel())) continue;

                            //当前父级节点存在该叶子节点
                            baseLabel.addChildrenNode(nowLabel);
                            baseLabel = nowLabel;
                        }

                    }

                }

            } else {

                final String label;
                if (null == excelProperty) label = value;
                else label = String.join(" ", excelProperty.value());
                labelBeanList.add(new QueryOrderReportDataResponse.LabelBean().setLabel(label).setValue(value));
            }


        }

        resultMap.put(exportDataClass, labelBeanList);
    }


    /**
     * 获得表头集合
     */
    public List<QueryOrderReportDataResponse.LabelBean> getLabelArray (Class<?> resultType, ReportRequest reportRequest, List<?> orderReportDataList) {

        //静态表头直接返回
        if (CLASS_LABEL_MAP_DEFAULT.containsKey(resultType)) return CLASS_LABEL_MAP_DEFAULT.get(resultType);

        //动态表头不包含该类型 未命中配置
        if (!CLASS_LABEL_MAP_DYNAMIC.containsKey(resultType)) return new ArrayList<>();

        //获得动态表头
        return dynamicHelper.getDynamicLabel(CLASS_LABEL_MAP_DYNAMIC.get(resultType), reportRequest, orderReportDataList);

    }

}
