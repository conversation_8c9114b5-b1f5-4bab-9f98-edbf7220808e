package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.issuer.CreateIssuerRequest;
import com.gtech.gvcore.common.request.issuer.DeleteIssuerRequest;
import com.gtech.gvcore.common.request.issuer.GetIssuerRequest;
import com.gtech.gvcore.common.request.issuer.QueryIssuerRequest;
import com.gtech.gvcore.common.request.issuer.UpdateIssuerRequest;
import com.gtech.gvcore.common.request.issuer.UpdateIssuerStatusRequest;
import com.gtech.gvcore.common.response.issuer.IssuerResponse;
import com.gtech.gvcore.dao.model.Issuer;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface IssuerService {

    Result<String> createIssuer(CreateIssuerRequest param);

    Result<Void> updateIssuer(UpdateIssuerRequest param);

    Result<Void> deleteIssuer(DeleteIssuerRequest param);

    PageResult<IssuerResponse> queryIssuerList(QueryIssuerRequest param);

    IssuerResponse getIssuer(GetIssuerRequest param);

	List<Issuer> queryIssuerByCodeList(List<String> issuerCodeList);

    Result<Void> updateIssuerStatus(UpdateIssuerStatusRequest param);

    /**
     * 
     * @param issuerCode
     * @return
     * <AUTHOR>
     * @date 2022年5月9日
     */
    Issuer queryByIssuerCode(String issuerCode);

}
