package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 14:25
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class BookletInventoryDetailedBo {

    private String issuerCode;

    private String outletCode;

    private String cpgCode;

    private String bookletNumber;

    private String startCardNumber;

    private String endCardNumber;

    private String effectiveTime;

    private String bookletStatus;

    private String cardCount;

    public Date getEffectiveTime() {

        return DateUtil.parseDate(effectiveTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }
}
