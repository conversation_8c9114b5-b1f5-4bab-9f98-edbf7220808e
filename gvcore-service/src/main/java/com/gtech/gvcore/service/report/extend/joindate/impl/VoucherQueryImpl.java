package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName VoucherQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:47
 * @Version V1.0
 **/
@Component
public class VoucherQueryImpl implements QuerySupport<Voucher> {

    public static final Voucher EMPTY = new Voucher();

    @Autowired
    private VoucherMapper voucherMapper;

    @Override
    public List<Voucher> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Voucher.class);
        example.createCriteria().andIn(Voucher.C_VOUCHER_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Voucher> voucherList = voucherMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(voucherList)) return Collections.emptyList();

        return voucherList;
    }

    @Override
    public Function<Voucher, String> codeMapper() {
        return Voucher::getVoucherCode;
    }

    @Override
    public Voucher emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Voucher> supportType() {
        return Voucher.class;
    }
}
