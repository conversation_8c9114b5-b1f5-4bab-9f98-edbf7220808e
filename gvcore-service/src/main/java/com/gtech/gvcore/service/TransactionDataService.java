package com.gtech.gvcore.service;


import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.transaction.BatchcloseRequest;
import com.gtech.gvcore.common.request.transaction.CardtransactionhistoryRequest;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.response.transaction.BatchcloseResponse;
import com.gtech.gvcore.common.response.transaction.CardtransactionhistoryResponse;
import com.gtech.gvcore.common.response.transaction.TransactionResponse;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dto.SalesDataDto;
import com.gtech.gvcore.dto.SalesDataResultDto;
import com.gtech.gvcore.dto.TransactionDashboardDto;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TransactionDataService {


    Result<Void> createTransactionData(CreateTransactionDataRequest request);

    Result<Integer> createTransactionDataList(List<CreateTransactionDataRequest> request);

    Map<String, String> queryVoucherInvoiceNo(List<String> voucherCodeList,TransactionTypeEnum typeEnum);
    Map<String, String> queryVoucherApprovalCode(List<String> voucherCodeList,TransactionTypeEnum typeEnum);

    Result<Void> updateTransactionDataResponseMessage(String transaction , String response,Integer successOfFail,TransactionResponse body);

    List<TransactionData> queryTransactionDataByVoucherCode (String voucherCode);

    /**
     * 
     * @param transactionDataList
     * @return
     * <AUTHOR>
     * @date 2022年5月6日
     */
    int insertList(List<TransactionData> transactionDataList);

    void requestLog(TransactionRequest request, String terminalId, ResponseEntity<TransactionResponse> response, String batchId);

    void responseLog(ResponseEntity<TransactionResponse> response);

    /**
     * 
     * <AUTHOR>
     * @param billNumberList
     * @return
     * @date 2022年6月23日
     */
    Map<String, BigDecimal> sumAmountGroupByBillNumber(List<String> billNumberList);

    BatchcloseResponse batchClose(BatchcloseRequest batchId, String token);

    CardtransactionhistoryResponse cardtransactionhistory(CardtransactionhistoryRequest param);



    List<SalesDataResultDto> querySalesData(SalesDataDto param);

    BigDecimal countAmountByTransactionIdAndTypes(String transactionId, Collection<TransactionTypeEnum> transactionTypes, @Nullable String cpgCode);

    Integer countNumByTransactionIdAndType(String transactionId, Collection<TransactionTypeEnum> transactionTypes, @Nullable String cpgCode);

    TransactionData getPreviousTransactionData(String voucherCode, TransactionTypeEnum transactionTypeEnum);

    /**
     * 统计当前时间的销售增量数据
     * 查询前一个小时的数据
     *
     * @param date
     * @return
     */
    List<TransactionDashboardDto> getTransactionDashboard(Date date,TransactionTypeEnum typeEnum);

    String getLastInvoiceNo (String voucher);

    TransactionData selectLastTransactionDataByVoucherAndType(String voucherCode, TransactionTypeEnum... type);

    Map<String, TransactionData> selectLastTransactionDataByVoucherCodesAndType(List<String> voucherCodes, TransactionTypeEnum... type);
}
