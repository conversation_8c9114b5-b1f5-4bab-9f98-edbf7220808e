package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 14:12
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class SalesDetailedBean {


    @ExcelProperty(value = "Voucher Number")
    private String cardNumber;

    @ExcelProperty(value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "OutletCode")
    private String outletCode;

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "POS Name")
    private String posName;

    @ExcelProperty(value = "Voucher Program Group")
    private String programGroup;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ReportAmountValue
    @ExcelProperty(value = "Amount", converter = ExportExcelNumberConverter.class)
    private String amount;

    @ReportAmountValue
    @ExcelProperty(value = "Bill Amount", converter = ExportExcelNumberConverter.class)
    private String billAmount;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Response Message")
    private String responseMessage;

    @ExcelProperty(value = "Date At Client")
    private String dateAtClient;

    @ExcelProperty(value = "Transaction Mode")
    private String transactionMode;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ExcelProperty(value = "Issuance Year")
    private String issuanceYear;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "Reference Number")
    private String referenceNumber;

    @ExcelProperty(value = "Voucher Entry Mode")
    private String cardEntryMode;

    @ExcelProperty(value = "Batch Number")
    private String batchNumber;

    @ReportAmountValue
    @ExcelProperty(value = "Request Amount", converter = ExportExcelNumberConverter.class)
    private String requestAmount;

    @ExcelProperty(value = "Approval Code")
    private String approvalCode;

    @ExcelProperty(value = "SBU Company Name")
    private String subCompanyName;
}
