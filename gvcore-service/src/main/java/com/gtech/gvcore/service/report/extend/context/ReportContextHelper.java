package com.gtech.gvcore.service.report.extend.context;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Objects;

/**
 * @ClassName ReportContextHelper
 * @Description 报表上下文帮助类
 * <AUTHOR>
 * @Date 2022/12/29 16:44
 * @Version V1.0
 **/
@Slf4j
public class ReportContextHelper {

    private ReportContextHelper() {
    }

    //上下文快照
    private static final ThreadLocal<ReportContext> THREAD_LOCAL_REPORT_CONTEXT = new ThreadLocal<>();

    /**
     * 注册上下文快照
     * @param context
     */
    public static void register(ReportContext context) {

        log.info("ReportContextHelper.register => register context report code: {}", context.getReportCode());

        if (Objects.isNull(context)) return;

        THREAD_LOCAL_REPORT_CONTEXT.set(context);
    }

    /**
     * 清除上下文快照
     */
    public static void cancel() {

        ReportContext context = THREAD_LOCAL_REPORT_CONTEXT.get();

        if (Objects.isNull(context)) return;

        log.info("ReportContextHelper.cancel => cancel context report code: {}", context.getReportCode());

        THREAD_LOCAL_REPORT_CONTEXT.remove();
    }

    /**
     * 获取上下文
     * @return
     */
    public static ReportContext getContext() {

        ReportContext context = THREAD_LOCAL_REPORT_CONTEXT.get();

        if (null == context) {
            log.warn("ReportContextHelper 定位 context 失败. 无法找到上下文对象");
            return null;
        }

        return context;
    }

    /**
     * 获取上下文
     * @return
     */
    public static ReportContext findContext() {

        ReportContext context = getContext();

        if (null == context) throw new ReportContextNotFound("ReportContextHelper 无法找到上下文对象");

        return context;
    }

    /**
     * 上下文标识无数据
     */
    public static void noData () {

        ReportContext context = getContext();

        if (null == context) return;

        context.noData();
    }

    /**
     * 报表构建时间
     */
    public static Date reportBuilderTime () {

        ReportContext context = getContext();

        if (null == context) return new Date();

        return context.getContextBuilderTime();
    }

    public static class ReportContextNotFound extends RuntimeException {

        public ReportContextNotFound(String message) {
            super(message);
        }
    }

}

