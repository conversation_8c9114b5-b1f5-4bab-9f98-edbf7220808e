package com.gtech.gvcore.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.operatelog.CreateOperationLogRequest;
import com.gtech.gvcore.common.request.operatelog.GvOperateLogRemarkJson;
import com.gtech.gvcore.common.request.operatelog.QueryOperationLogRequest;
import com.gtech.gvcore.common.response.operatelog.GvOperateLogByPageResponse;
import com.gtech.gvcore.dao.mapper.GvOperateLogMapper;
import com.gtech.gvcore.dao.model.GvOperateLogEntity;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.GvOperateLogService;
import com.gtech.gvcore.service.GvUserAccountService;

import tk.mybatis.mapper.entity.Example;

/**
 * (GvOperateLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-28 10:22:13
 */
@Service
public class GvOperateLogServiceImpl implements GvOperateLogService {

    @Autowired
    private GvOperateLogMapper gvOperateLogMapper;

    @Autowired
    private GvUserAccountService gvUserAccountService;

    @Override
    public Result<String> createOperationLog(CreateOperationLogRequest createCpgTypeRequest) {

        GvOperateLogEntity operateLogEntity = BeanCopyUtils.jsonCopyBean(createCpgTypeRequest, GvOperateLogEntity.class);

        operateLogEntity.setCreateTime(new Date());
        operateLogEntity.setRemark(JSON.toJSONString(createCpgTypeRequest.getRemarks()));

        if (gvOperateLogMapper.insertSelective(operateLogEntity) > 0) {
            return Result.ok(operateLogEntity.getBusinessCode());
        }

        return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
    }

    @Override
    public PageResult<GvOperateLogByPageResponse> queryOperationLog(QueryOperationLogRequest logRequest) {

        Example example = new Example(GvOperateLogEntity.class, true, false);

		example.createCriteria()
				.andEqualTo(GvOperateLogEntity.C_BUSINESS_CODE, StringUtils.isEmpty(logRequest.getBusinessCode()) ? null : logRequest.getBusinessCode())
				.andEqualTo(GvOperateLogEntity.C_METHOD, StringUtils.isEmpty(logRequest.getMethod()) ? null : logRequest.getMethod())
				.andEqualTo(GvOperateLogEntity.C_OPERATE_USER, StringUtils.isEmpty(logRequest.getOperateUser()) ? null : logRequest.getOperateUser());

        //更新时间倒序
        example.orderBy(GvOperateLogEntity.C_CREATE_TIME).desc();

        PageMethod.startPage(logRequest.getPageNum(), logRequest.getPageSize());
        List<GvOperateLogEntity> list = gvOperateLogMapper.selectByCondition(example);

        PageInfo<GvOperateLogEntity> pageInfo = new PageInfo<>(list);

        List<GvOperateLogByPageResponse> responses = new ArrayList<>(logRequest.getPageSize());

        list.forEach(item -> {
            GvOperateLogByPageResponse response = new GvOperateLogByPageResponse();
            BeanUtils.copyProperties(item, response);

            List<GvOperateLogRemarkJson> remarks = JSON.parseArray(item.getRemark(), GvOperateLogRemarkJson.class);
            response.setRemarks(remarks);

            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
	@Override
    public void createSuccessLog(String businessCode, String method, String operateUserCode, String remarkDesc) {
		
		GvOperateLogEntity operationLogRequest = new GvOperateLogEntity();
    	operationLogRequest.setBusinessCode(businessCode);
    	operationLogRequest.setInput(new JSONObject().toJSONString());
    	operationLogRequest.setOutput(JSON.toJSONString(Result.ok()));
    	operationLogRequest.setMethod(method);
        operationLogRequest.setOperateUser(operateUserCode);
    	operationLogRequest.setFirstName(StringUtils.EMPTY);
    	operationLogRequest.setLastName(StringUtils.EMPTY);
    	operationLogRequest.setSuccess(Boolean.TRUE.toString());
    	operationLogRequest.setCreateTime(new Date());
        if (StringUtils.isNotEmpty(remarkDesc)) {
            JSONArray jsonArray = new JSONArray(1);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("desc", remarkDesc);
            jsonObject.put("language", Locale.US.toLanguageTag());
            jsonArray.add(jsonObject);
            operationLogRequest.setRemark(jsonArray.toJSONString());
        }
        UserAccount userAccount = gvUserAccountService.getUserNameInfo(operateUserCode);
        if (userAccount != null) {
            operationLogRequest.setFirstName(userAccount.getFirstName());
            operationLogRequest.setLastName(userAccount.getLastName());
        }
    	gvOperateLogMapper.insertSelective(operationLogRequest);
		
	}

	@Override
	public List<GvOperateLogEntity> queryLogByBusiness(String businessCode) {
		Example example = new Example(GvOperateLogEntity.class);
		example.createCriteria()
				.andEqualTo(GvOperateLogEntity.C_BUSINESS_CODE, businessCode);
		// 更新时间倒序
		example.orderBy(GvOperateLogEntity.C_CREATE_TIME).desc();
		return gvOperateLogMapper.selectByCondition(example);
	}
}
