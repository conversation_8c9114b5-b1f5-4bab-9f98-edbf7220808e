package com.gtech.gvcore.service.report;

import com.gtech.commons.page.PageData;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.request.orderreport.QueryOrderReportRequest;
import com.gtech.gvcore.common.response.orderreport.QueryReportReqResponse;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.dao.model.SchedulerReport;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @ClassName ReportService
 * @Description report service
 *  报表请求服务(request)
 *  请求产出一张报表 该服务会创建一个报表请求
 * <AUTHOR>
 * @Date 2023/1/20 15:53
 * @Version V1.0
 **/
public interface ReportRequestService {

    String createReport(CreateReportRequest request, boolean local);

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    String createReport(SchedulerReport schedulerReport);

    PageData<QueryReportReqResponse> queryReport(QueryOrderReportRequest queryOrderReportRequest);

    ReportRequest getReport(String code);
}
