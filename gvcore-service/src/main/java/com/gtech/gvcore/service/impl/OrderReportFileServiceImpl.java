package com.gtech.gvcore.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gtech.gvcore.dao.mapper.OrderReportFileMapper;
import com.gtech.gvcore.dao.model.OrderReportFile;
import com.gtech.gvcore.service.OrderReportFileService;

/**
 * <AUTHOR>
 * @date 2022年6月21日
 */
@Service
public class OrderReportFileServiceImpl implements OrderReportFileService {

    @Autowired
    private OrderReportFileMapper orderReportFileMapper;

    @Override
    public int insertList(List<OrderReportFile> files) {
        return orderReportFileMapper.insertList(files);
    }

}
