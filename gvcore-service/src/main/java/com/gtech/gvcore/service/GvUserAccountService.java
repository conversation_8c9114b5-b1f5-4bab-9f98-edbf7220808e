/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.service;

import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.basic.idm.service.dto.UserAccountDto;
import com.gtech.commons.page.PageData;
import com.gtech.gvcore.common.request.useraccount.GvCreateUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvUpdateUserAccountRequest;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.useraccount.UserAccountResponse;
import com.gtech.gvcore.dao.model.UserAccount;

import java.util.List;
import java.util.Map;

public interface GvUserAccountService {

    /**
	 * 创建用户账号
	 * 
	 * @param param GvCreateUserAccountRequest
	 */
	UserAccountDto createUserAccount(GvCreateUserAccountRequest param);

    /**
     * 更新用户账号信息
     * 
     * @param param UserAccountDto
     */
	int updateUserAccount(GvUpdateUserAccountRequest param);

	/**
	 * Retrieve a user account information by user code.
	 * 
	 * @param paramDto -- GetUserAccountParamDto
	 */
	UserAccountResponse getUserAccount(GetUserAccountParamDto paramDto);
	
	String getUserEmail(String userCode);
	
    /**
     * 
     * @param userCode
     * @return
     * <AUTHOR>
     * @date 2022年5月13日
     */
    UserAccount getUserNameInfo(String userCode);

	Map<String, String> queryFullNameByCodeList(List<String> userCodeList);

	PageData<UserAccountResponse> queryUserAccountList(GvQueryUserAccountRequest param);

	List<PermissionCodeResponse> queryPerrmissionCodeList(String userCode);
	
	List<UserAccount> queryUserAccountByRoles(List<String> roleCodeList);

	List<UserAccount> queryUserByRolesAndDataPermissions(List<String> roleCodeList, String permissionCode);

	List<String> getMenuCodeListByRoles(List<String> roleCodeList);

	List<UserAccount> queryUserByCodes(List<String> codes, String... fields);
}
