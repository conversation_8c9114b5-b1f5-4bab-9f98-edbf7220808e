package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.distribution.QueryHistoricalInventoryChartRequest;
import com.gtech.gvcore.common.response.distribution.HistoricalInventoryChartResponse;

/**
 * @ClassName CpgMonthInventorySnapshotService
 * @Description CPG月库存快照服务
 * <AUTHOR>
 * @Date 2022/7/6 15:28
 * @Version V1.0
 **/
public interface CpgMonthInventorySnapshotService {
    HistoricalInventoryChartResponse queryHistoricalInventoryChart(QueryHistoricalInventoryChartRequest request);

    void createMonthInventorySnapshot();
}
