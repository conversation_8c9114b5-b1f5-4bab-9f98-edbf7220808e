package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 14:25
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class InventorySummaryBean {
    /**
     * issuer
     */
    @ExcelProperty(value = "Issuer")
    private String issuer;

    /**
     * merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;


    @ExcelProperty(value = "Outlet Code")
    private String outletCode;

    /**
     * merchantOutletName
     */
    @ExcelProperty(value = "Outlet Name")
    private String merchantOutletName;

    /**
     * vpg
     */
    @ExcelProperty(value = "Voucher Program Group")
    private String vpg;

    /**
     * vpgType
     */
    @ExcelProperty(value = "Voucher Program Group Type")
    private String vpgType;

    /**
     * cardStatus
     */
    @ExcelProperty(value = "Voucher Status")
    private String cardStatus;

    /**
     * cardsCount
     */
    @ExcelProperty(value = "Voucher Count", converter = ExportExcelNumberConverter.class)
    private String cardsCount;

    /**
     * cardsCount
     */
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;
}
