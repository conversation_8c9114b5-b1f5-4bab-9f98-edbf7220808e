package com.gtech.gvcore.service;

import java.util.List;
import java.util.Map;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.outlet.*;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.dto.OutletDto;
import com.gtech.gvcore.dao.dto.OutletIssuerNameInfo;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dto.OutletMerchantNameInfo;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface OutletService {

    Result<String> createOutlet(CreateOutletRequest param);

    Result<Void> updateOutlet(UpdateOutletRequest param);

    Result<Void> deleteOutlet(DeleteOutletRequest param);

    PageResult<OutletResponse> queryOutletList(QueryOutletRequest param);

    OutletResponse getOutlet(GetOutletRequest param);

    OutletResponse getOutletByOutletName(String outletName);


    Result<Void> updateOutletStatus(UpdateOutletStatusRequest param);

    PageResult<OutletResponse> queryOutletByMerchantCodes(QueryOutletByMerchantCodesRequest request);

    List<OutletResponse> queryOutletByCodes(List<String> codeList);

	List<Outlet> queryOutletByNames(List<String> nameList);

	List<Outlet> queryOutletByMerchantCodes(List<String> merchantCodeList);
    /**
     * @param outletCode
     * @return
     * <AUTHOR>
     * @date 2022年4月11日
     */
    Outlet queryByOutletCode(String outletCode);

    /**
     * 
     * @param outletCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<Outlet> queryByOutletCodeList(List<String> outletCodeList);

    /**
     * 
     * <AUTHOR>
     * @param outletCodeList
     * @return
     * @date 2022年6月23日
     */
    Map<String, Outlet> queryOutletMapByOutletCodeList(List<String> outletCodeList);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<OutletIssuerNameInfo> queryOutletIssuerNameInfo(OutletDto dto);


    Result<List<OutletResponse>> queryOutletByBusinessType(QueryOutletByBusinessRequest queryOutletByBusinessRequest);

    /**
     * 
     * @param outletCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月26日
     */
    Map<String, OutletMerchantNameInfo> queryOutletMerchantName(List<String> outletCodeList);

    /**
     * 
     * <AUTHOR>
     * @param companyCodeList
     * @param merchantCodeList
     * @return
     * @date 2022年6月16日
     */
    List<String> queryAllOutletCodeByCompanyOrMerchant(List<String> companyCodeList, List<String> merchantCodeList);

    /**
     * 获得outlet 根据编码
     * @param outletCode
     * @return
     */
    Outlet getOutletByCode (String outletCode);

	List<Outlet> queryAllOutLet();

    Result<Void> updateOutletCodeScript(ReplaceOutletCodeRequest outlet);

    Result<Void> checkColumWithOutlet();


}
