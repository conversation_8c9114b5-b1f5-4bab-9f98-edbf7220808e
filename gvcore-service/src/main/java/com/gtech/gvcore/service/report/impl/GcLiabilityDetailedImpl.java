package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.GcReportTempLiabilityDStructure;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcLiabilityDetailedBean;
import com.gtech.gvcore.service.report.impl.param.LiabilityDetailQueryData;
import com.gtech.gvcore.service.report.impl.support.liability.GcLiabilityDataScript;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:10
 * @Description:
 */
@Service
public class GcLiabilityDetailedImpl extends ReportSupport
        implements BusinessReport<LiabilityDetailQueryData, GcLiabilityDetailedBean>, PollReport {

    @Autowired
    private GcLiabilityDataScript script;

    @Override
    public LiabilityDetailQueryData builderQueryParam(CreateReportRequest reportParam) {

        LiabilityDetailQueryData param = new LiabilityDetailQueryData();

        // issuer merchant outlet
        param.setIssuerCode(reportParam.getIssuerCode());
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());
        param.setReportVoucherStatusList(reportParam.getVoucherStatus());
        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        // table code - simplified backward compatibility logic
        Date transactionDateStart = reportParam.getTransactionDateStart();
        Date targetDate = GvConvertUtils.toObject(transactionDateStart, new Date());

        // 1. 首先尝试旧格式表
        String legacyTableCode = DateUtil.format(targetDate, GcLiabilityDataScript.TABLE_CODE_DATE_FORMAT);
        String legacyTableName = String.format("gc_report_temp_liability_%s_%s", 
            GcLiabilityDataScript.LIABILITY_DETAIL_TABLE_TYPE, legacyTableCode);
        
        if (script.isTableExist(legacyTableName)) {
            // 旧表存在，使用旧格式
            param.setTableCode(legacyTableCode);
        } else {
            // 旧表不存在，使用新格式
            String newTableName = script.generateNewTableName(GcLiabilityDataScript.LIABILITY_DETAIL_TABLE_TYPE, targetDate);
            
            if (!script.isTableExist(newTableName)) {
                ReportContextHelper.noData();
            }

            // 新格式表名：gc_report_temp_liability_d_1_08
            // 需要提取：1_08 (年份模数_月份)
            String[] parts = newTableName.split("_");
            String yearMod = parts[parts.length - 2];  // 获取 "1"
            String month = parts[parts.length - 1];    // 获取 "08"
            String newTableCode = yearMod + "_" + month; // 拼接为 "1_08"
            
            param.setTableCode(newTableCode);
        }
        return param;
    }

    @Override
    public List<GcLiabilityDetailedBean> getExportData(LiabilityDetailQueryData param) {

        final List<GcReportTempLiabilityDStructure> detailList = gcReportBusinessMapper.liabilityDetailReport(param, GvPageHelper.getRowBounds(param));

        final JoinDataMap<GcCpg> cpgJoinDataMap = super.getMapByCode(detailList, GcReportTempLiabilityDStructure::getCpgCode, GcCpg.class);

        return detailList.stream()
                .flatMap(e ->
                        Arrays.stream(e.getVoucherCodes().split(","))
                                .filter(StringUtils::isNotBlank)
                                .map(s -> new GcLiabilityDetailedBean()
                                        .setVoucherNumber(s)
                                        .setVoucherProgramGroup(cpgJoinDataMap.findValue(e.getCpgCode()).getCpgName())
                                        .setBalance(super.toAmount(e.getBalance()))
                                        .setDenomination(super.toAmount(e.getDenomination()))
                                        .setExpiryDate(DateUtil.format(e.getExpiryDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                                        .setVoucherStatus(e.getVoucherStatus())
                                )
                ).collect(Collectors.toList());
    }

    @Override
    public void customContext(ReportContextBuilder builder) {
        builder.bindFileContext(new GcLiabilityFileContext());
    }

    public static class GcLiabilityFileContext implements FileContext {

        private final StringBuilder value = new StringBuilder();

        @Override
        public void init() {
            value.append("Gift Card Number").append("\t")
                    .append("Gift Card Program Group").append("\t")
                    .append("Gift Card Status").append("\t")
                    .append("Denomination").append("\t")
                    .append("Current Balance").append("\t")
                    .append("Expiry Date").append("\n");
        }

        @Override
        public void doFill(List<?> beanList) {

            this.doFillLiabilityDetailBean(BeanCopyUtils.jsonCopyList(beanList, GcLiabilityDetailedBean.class));

        }

        public void doFillLiabilityDetailBean(List<GcLiabilityDetailedBean> beanList) {

            beanList.forEach(e -> value.append(e.getVoucherNumber()).append("\t")
                    .append(e.getVoucherProgramGroup()).append("\t")
                    .append(e.getVoucherStatus()).append("\t")
                    .append(e.getDenomination()).append("\t")
                    .append(e.getBalance()).append("\t")
                    .append(e.getExpiryDate()).append("\n")
            );
        }

        @Override
        public String finish() {

            final String fileName = ReportExportTypeEnum.GC_LIABILITY_DETAILED_REPORT.getExportName() + ReportContextHelper.findContext().getReportCode() + ".txt";
            final ByteArrayInputStream inputStream = new ByteArrayInputStream(value.toString().getBytes());
            return ReportUploadHelper.fileUpload(inputStream, fileName);
        }
    }

    @Override
    public int pageSize() {
        return 100;
    }


    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.GC_LIABILITY_DETAILED_REPORT;
    }

}
