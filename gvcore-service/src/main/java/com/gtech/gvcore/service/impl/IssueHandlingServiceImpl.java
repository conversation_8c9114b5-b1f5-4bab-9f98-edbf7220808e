package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.pagehelper.util.StringUtil;
import com.gtech.basic.masterdata.core.controller.QueryResult;
import com.gtech.basic.masterdata.web.entity.MasterDataDdLangEntity;
import com.gtech.basic.masterdata.web.request.QueryDdLangRequest;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.constants.RedisConstants;
import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.issuehandling.*;
import com.gtech.gvcore.common.response.issuehandling.*;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.dto.IssueHandlingDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.service.*;
import com.gtech.gvcore.service.impl.issuehandle.IssueHandlerRegenerateActivationCodeService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IssueHandlingServiceImpl implements IssueHandlingService {

	@Value("#{${gv.issuehandling.uploadfile.header.field}}")
    private Map<String, String> headerFieldMap;

	@Value("#{${gv.issuehandling.uploadfile.header.field.date.pattern}}")
    private Map<String, String> fieldDatePatternMap;

	@Autowired
	private IssueHandlingMapper issueHandlingMapper;

	@Autowired
	private IssueHandlingProofService issueHandlingProofService;

	@Autowired
	private IssueHandlingDetailsService issueHandlingDetailsService;

	@Autowired
    private GvCodeHelper gvCodeHelper;

	@Autowired
	private ApplicationContext applicationContext;

	@Autowired
	private GTechRedisTemplate gTechRedisTemplate;

    @Autowired
    private FlowNoticeService flowNoticeService;

    @Autowired
    private MasterDataDdLangService masterDataDdLangService;

    @Autowired
    private GvUserAccountService gvUserAccountService;

    @Lazy
	@Autowired
	private VoucherService voucherService;
    @Autowired
	private CpgService cpgService;

    @Autowired
    private OssHelper ossHelper;


    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IssueHandlerRegenerateActivationCodeService regenerateActivationCodeService;

    @Async
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Result<String> createIssueHandling(CreateIssueHandlingRequest request) {

        IssueHandlingTypeEnum typeEnum = getIssueHandlingTypeEnum(request.getIssueType());
        if (typeEnum == null) {
			return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), ResultErrorCodeEnum.PARAMTER_ERROR.desc() + ":issueType");
		}

		IssueHandling issueHandling = new IssueHandling();
        BeanUtils.copyProperties(request, issueHandling);
        issueHandling.setIssueHandlingCode(gvCodeHelper.issueHandlingCode());
        issueHandling.setUploadedFileType(GvcoreConstants.ISSUE_HANDLING_FILE_TYPE);
        issueHandling.setProcessStatus(IssueHandlingProcessStatusEnum.CREATED.code());
        issueHandling.setStatus(IssueHandlingStatusEnum.CREATED.code());
        issueHandling.setCreateTime(new Date());

        Result<List<IssueHandlingProof>> proofResult = checkProof(request.getProofFileList(), request.getIssueType(),
                issueHandling);
        if (!proofResult.isSuccess()) {
            return Result.failed(proofResult.getCode(), proofResult.getMessage());
        }
        List<IssueHandlingProof> proofList = proofResult.getData();


        String uploadedFileUrl = request.getUploadedFileUrl();

		String key = RedisConstants.LOCK_KEY_ISSUEHANDLING_FILE + uploadedFileUrl;
		boolean flag = gTechRedisTemplate.opsValueSetIfAbsent(RedisConstants.APPKEY, key, key, RedisConstants.TASK_CACHE_TIMEOUT);
		if(!flag) {
			return Result.failed(ResultErrorCodeEnum.SAME_FILE_PROCESSING.code(), ResultErrorCodeEnum.SAME_FILE_PROCESSING.desc());
		}


        String url = ossHelper.grantAccessUrl(uploadedFileUrl);

        Result<List<IssueHandlingDetails>> result = parseFile(url, issueHandling);
		if(!result.isSuccess()) {
			gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
			return Result.failed(result.getCode(), result.getMessage());
		}

		List<IssueHandlingDetails> details = result.getData();

		issueHandling.setCountVoucher(details.size());
		issueHandling.setCountFailed(0);
        issueHandlingMapper.insert(issueHandling);

        issueHandlingProofService.insertList(proofList);

		issueHandlingDetailsService.insertList(details);

		gTechRedisTemplate.delete(RedisConstants.APPKEY, key);


        redisTemplate.opsForValue().set("GV:CREATISSUEHANDING:"+uploadedFileUrl, issueHandling.getIssueHandlingCode(), 1, TimeUnit.DAYS);


        return Result.ok(issueHandling.getIssueHandlingCode());
	}

    /**
     *
     * @param proofFileList
     * @param issueType
     * @param issueHandling
     * @return
     * <AUTHOR>
     * @date 2022年5月30日
     */
    private Result<List<IssueHandlingProof>> checkProof(List<ProofFile> proofFileList, String issueType,
            IssueHandling issueHandling) {

        Set<String> proofTypeSet = queryProofTypeSet(issueType);

        Collection<ProofFile> proofFileCollection = proofFileList.stream()
                .collect(Collectors.toMap(ProofFile::getProofFileUrl, v -> v, (v1, v2) -> v1)).values();
        List<IssueHandlingProof> proofList = new ArrayList<>(proofFileCollection.size());
        for (ProofFile proofFile : proofFileCollection) {
            IssueHandlingProof proof = new IssueHandlingProof();
            proof.setIssueHandlingProofCode(UUIDUtils.generateCode());
            proof.setIssueHandlingCode(issueHandling.getIssueHandlingCode());
            proof.setProofType(proofFile.getProofType());
            proof.setProofFileName(proofFile.getProofFileName());
            proof.setProofFileUrl(proofFile.getProofFileUrl());
            proof.setCreateUser(issueHandling.getCreateUser());
            proof.setCreateTime(issueHandling.getCreateTime());
            proofList.add(proof);

            proofTypeSet.remove(proofFile.getProofType());
        }

        if (!proofTypeSet.isEmpty()) {
            return Result.failed(ResultErrorCodeEnum.MISSING_PROOF_DOCUMENT.code(),
                    ResultErrorCodeEnum.MISSING_PROOF_DOCUMENT.desc() + JSON.toJSONString(proofTypeSet));
        }
        return Result.ok(proofList);
    }

    /**
     *
     * @param issueType
     * @return
     * <AUTHOR>
     * @date 2022年5月30日
     */
    private Set<String> queryProofTypeSet(String issueType) {

        QueryDdLangRequest ddLangRequest = new QueryDdLangRequest();
        ddLangRequest.setDdCode("PROOF_" + issueType.toUpperCase());
        ddLangRequest.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        QueryResult<MasterDataDdLangEntity> queryResult = masterDataDdLangService.queryPagesAll(ddLangRequest);
        if (queryResult != null && CollectionUtils.isNotEmpty(queryResult.getList())) {
            return queryResult.getList().stream().map(MasterDataDdLangEntity::getDdValue).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

	/**
	 *
	 * @param uri
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月11日
	 */
    private Result<List<IssueHandlingDetails>> parseFile(String uri, IssueHandling issueHandling) {

        long start = System.currentTimeMillis();
		URL url = null;
		try {
			url = new URL(uri);
		} catch (MalformedURLException e) {
			log.info(ResultErrorCodeEnum.UPLOADED_FILE_URL_IS_INVALID.desc() + e.getMessage(), e);
			return Result.failed(ResultErrorCodeEnum.UPLOADED_FILE_URL_IS_INVALID.code(), ResultErrorCodeEnum.UPLOADED_FILE_URL_IS_INVALID.desc());
		}
        log.info("parseFile new URL time={}, fileUrl={}", System.currentTimeMillis() - start, uri);

        start = System.currentTimeMillis();
        String[] header = new String[0];
		try (InputStream is = url.openStream();
				InputStreamReader isr = new InputStreamReader(is, StandardCharsets.UTF_8);
				BufferedReader br = new BufferedReader(isr);
                CSVParser csvParser = CSVFormat.DEFAULT.builder().setAllowDuplicateHeaderNames(false).setHeader(header)
                        .build().parse(br);) {

            log.info("parseFile csvParser time={}, fileUrl={}", System.currentTimeMillis() - start, uri);
            start = System.currentTimeMillis();
			if(csvParser.getHeaderMap() == null) {
				return Result.failed(ResultErrorCodeEnum.UPLOADED_FILE_URL_IS_INVALID.code(), ResultErrorCodeEnum.UPLOADED_FILE_URL_IS_INVALID.desc());
			}

			int columnSize = csvParser.getHeaderMap().size();
			List<String> fieldList = new ArrayList<>(columnSize);
			for (Map.Entry<String, Integer> entry : csvParser.getHeaderMap().entrySet()) {
				String fieldName = headerFieldMap.get(entry.getKey());
				if(fieldName == null) {
					return Result.failed(ResultErrorCodeEnum.UNSUPPORTED_COLUMN_NAME.code(), ResultErrorCodeEnum.UNSUPPORTED_COLUMN_NAME.desc() + entry.getKey());
				}
				fieldList.add(entry.getValue(), fieldName);
			}

            log.info("parseFile getHeaderMap time={}, fileUrl={}", System.currentTimeMillis() - start, uri);
            start = System.currentTimeMillis();

            Result<List<IssueHandlingDetails>> result = parse(issueHandling, csvParser, fieldList);

            log.info("parseFile parse time={}, fileUrl={}", System.currentTimeMillis() - start, uri);

            return result;
		} catch (IOException e) {
			log.warn(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc() + e.getMessage(), e);
			return Result.failed(ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.code(), ResultErrorCodeEnum.UPLOADED_FILE_NOT_MEET_STANDARDS.desc());
		} catch (GTechBaseException e) {
            return Result.failed(ResultErrorCodeEnum.WRONG_DATE_FORMAT.code(), e.getMessage());
		}
	}

    private Result<List<IssueHandlingDetails>> parse(IssueHandling issueHandling, CSVParser csvParser,
            List<String> fieldList) {
        int pageSize = 500;
        List<IssueHandlingDetails> details = new ArrayList<>(pageSize);
        int fieldSize = fieldList.size();
        for (CSVRecord csvRecord : csvParser) {
            JSONObject josn = new JSONObject();
            int recordSize = csvRecord.size();
            if (recordSize > fieldSize) {
                return Result.failed(ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.code(),
                        ResultErrorCodeEnum.UPLOADED_FILE_MORE_COLUMNS.desc());
            }
            for (int i = 0; i < recordSize; i++) {

                String key = fieldList.get(i);
                String pattern = fieldDatePatternMap.get(key);
                String value = csvRecord.get(i);
                if (pattern != null) {
                    josn.put(key, DateUtil.parseDate(value, pattern));
                } else {
                    josn.put(key, value);
                }
            }
            IssueHandlingDetails detail = josn.toJavaObject(IssueHandlingDetails.class);
            detail.setIssueHandlingDetailsCode(UUIDUtils.generateCode());
            detail.setIssueHandlingCode(issueHandling.getIssueHandlingCode());
			detail.setIssueType(issueHandling.getIssueType());
            detail.setRowOfSheet((int) csvRecord.getRecordNumber());
            detail.setProcessStatus(issueHandling.getProcessStatus());
            detail.setCreateUser(issueHandling.getCreateUser());
            detail.setCreateTime(issueHandling.getCreateTime());
            details.add(detail);
        }

        if (CollectionUtils.isEmpty(details)) {
            return Result.failed(ResultErrorCodeEnum.UPLOADED_FILE_ISEMPTY.code(),
                    ResultErrorCodeEnum.UPLOADED_FILE_ISEMPTY.desc());
        }
        return Result.ok(details);
    }



	/**
	 *
	 * @param issueType
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
    private IssueHandlingTypeEnum getIssueHandlingTypeEnum(String issueType) {

		for (IssueHandlingTypeEnum typeEnum : IssueHandlingTypeEnum.values()) {
			if(typeEnum.equalsCode(issueType)) {
                return typeEnum;
			}
		}
		return null;
	}

    @Async
	@Override
	public Result<ValidateUploadedFileResponse> validateUploadedFile(ValidateUploadedFileRequest request) {

	    IssueHandlingTypeEnum typeEnum = getIssueHandlingTypeEnum(request.getIssueType());
		if(typeEnum == null) {
			return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), ResultErrorCodeEnum.PARAMTER_ERROR.desc() + ":issueType");
		}

		ValidateUploadedFileResponse response = new ValidateUploadedFileResponse();
		response.setStartTime(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS));
        response.setUploadedFileName(request.getUploadedFileName());

		// 解析加载csv文件后，分批校验
        long start = System.currentTimeMillis();
		Result<List<IssueHandlingDetails>> result = parseFile(request.getUploadedFileUrl(), new IssueHandling());
        log.info("parseFile time={}, fileUrl={}", System.currentTimeMillis() - start, request.getUploadedFileUrl());
		if(!result.isSuccess()) {
            response.setResult(result.getMessage());
            response.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            redisTemplate.opsForValue().set(request.getUploadedFileUrl(),response , 1, TimeUnit.SECONDS.DAYS);
			return Result.failed(result.getCode(), result.getMessage());
		}

		List<IssueHandlingDetails> details = result.getData();

        start = System.currentTimeMillis();
		IssueHandlerBaseService handlerBaseService = getIssueHandlerBaseService(request.getIssueType());
		if(handlerBaseService == null) {
            response.setResult(ResultErrorCodeEnum.CANNOT_HANDLE_THIS_ISSUE_TYPE.desc() + request.getIssueType());
            response.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            redisTemplate.opsForValue().set(request.getUploadedFileUrl(),response , 1, TimeUnit.SECONDS.DAYS);
            return Result.failed(ResultErrorCodeEnum.CANNOT_HANDLE_THIS_ISSUE_TYPE.code(),
					ResultErrorCodeEnum.CANNOT_HANDLE_THIS_ISSUE_TYPE.desc() + request.getIssueType());
		}

		response.setCountVoucher(details.size());
        //检查detail是否有重复券号
        Map<String, IssueHandlingDetails> voucherCodeMap = new HashMap<>(details.size());
        for (IssueHandlingDetails detail : details) {
            if (voucherCodeMap.containsKey(detail.getVoucherCode())) {
                response.setResult(ResultErrorCodeEnum.DUPLICATE_DATA_ERROR.desc());
                response.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                redisTemplate.opsForValue().set(request.getUploadedFileUrl(),response , 1, TimeUnit.SECONDS.DAYS);
                return Result.failed(ResultErrorCodeEnum.DUPLICATE_DATA_ERROR.code(),
                        ResultErrorCodeEnum.DUPLICATE_DATA_ERROR.desc());
            }
            voucherCodeMap.put(detail.getVoucherCode(), detail);
        }

		details = handlerBaseService.validate(details, request.getIssuerCode());
        /*log.info("parseFile validate time={}, fileUrl={}", System.currentTimeMillis() - start,
                request.getUploadedFileUrl());*/

		//校验的结果集
		int serialNumber = 1;
		List<ExceptionRow> exceptionRowList = new ArrayList<>(response.getCountVoucher() / 2 + 1);
		for (IssueHandlingDetails detail : details) {
            if (!IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus())) {
				ExceptionRow row = new ExceptionRow();
				row.setSerialNumber(serialNumber);
				row.setRow(detail.getRowOfSheet());
				row.setReason(detail.getResult());
				row.setVoucherCode(detail.getVoucherCode());
				exceptionRowList.add(row);
				serialNumber ++;
			}
		}
		//校验的结果集

		response.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		response.setExceptionRowList(exceptionRowList);
		response.setCountFailed(exceptionRowList.size());

        //response转json
        String responseJson = JSON.toJSONString(response);

        redisTemplate.opsForValue().set(request.getUploadedFileUrl(), responseJson, 1, TimeUnit.SECONDS.DAYS);


		return Result.ok(response);
	}

	private IssueHandlerBaseService getIssueHandlerBaseService(String issueType) {

		Map<String, IssueHandlerBaseService> serviceMap = applicationContext.getBeansOfType(IssueHandlerBaseService.class);
		for (Map.Entry<String, IssueHandlerBaseService> entry : serviceMap.entrySet()) {
			IssueHandlingTypeEnum handlingTypeEnum = entry.getValue().getIssueHandlingType();
			if(handlingTypeEnum != null && issueType.equals(handlingTypeEnum.code())) {
				return entry.getValue();
			}
		}
		return null;
	}

    @Async
	@Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> editIssueHandling(EditIssueHandlingRequest request) {

        log.info("editIssueHandling issueHandlingCode={}, request={}", request.getIssueHandlingCode(), JSON.toJSONString(request));

        IssueHandling issueHandling = queryByIssueHandlingCode(request.getIssueHandlingCode());
        if(issueHandling == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        if (!(IssueHandlingStatusEnum.CREATED.equalsCode(issueHandling.getStatus())
                || IssueHandlingStatusEnum.REJECT.equalsCode(issueHandling.getStatus()))) {
            return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_EDIT.code(),
                    ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_EDIT.desc());
        }

        IssueHandlingDto updateIssueHandling = new IssueHandlingDto();
        BeanUtils.copyProperties(issueHandling, updateIssueHandling);
        updateIssueHandling.setUpdateTime(new Date());
        updateIssueHandling.setUpdateUser(request.getUpdateUser());
        updateIssueHandling.setUploadedFileName(request.getUploadedFileName());
        updateIssueHandling.setRemarks(request.getRemarks());
        updateIssueHandling.setStatus(IssueHandlingStatusEnum.CREATED.code());
        updateIssueHandling.setOldStatus(issueHandling.getStatus());

        Result<List<IssueHandlingProof>> proofResult = checkProof(request.getProofFileList(),
                issueHandling.getIssueType(), issueHandling);
        if (!proofResult.isSuccess()) {
            return Result.failed(proofResult.getCode(), proofResult.getMessage());
        }

        List<IssueHandlingDetails> details = null;
        String uploadedFileUrl = request.getUploadedFileUrl();
        String key = RedisConstants.LOCK_KEY_ISSUEHANDLING_FILE + uploadedFileUrl;
        if(!updateIssueHandling.getUploadedFileUrl().equals(uploadedFileUrl)) {
            updateIssueHandling.setUploadedFileUrl(request.getUploadedFileUrl());
            updateIssueHandling.setCreateUser(updateIssueHandling.getUpdateUser());
            updateIssueHandling.setCreateTime(updateIssueHandling.getUpdateTime());

            boolean flag = gTechRedisTemplate.opsValueSetIfAbsent(RedisConstants.APPKEY, key, key, RedisConstants.TASK_CACHE_TIMEOUT);
            if(!flag) {
                return Result.failed(ResultErrorCodeEnum.SAME_FILE_PROCESSING.code(), ResultErrorCodeEnum.SAME_FILE_PROCESSING.desc());
            }

            String url = ossHelper.grantAccessUrl(uploadedFileUrl);
            Result<List<IssueHandlingDetails>> result = parseFile(url, updateIssueHandling);
            if(!result.isSuccess()) {
                gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
                return Result.failed(result.getCode(), result.getMessage());
            }

            details = result.getData();
            updateIssueHandling.setCountVoucher(details.size());
        }

        int i = issueHandlingMapper.updateByEdit(updateIssueHandling);
        if(i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }


        List<IssueHandlingProof> proofList = getInsertProofFile(request, updateIssueHandling);

        issueHandlingProofService.deleteByIssueHandlingCode(updateIssueHandling.getIssueHandlingCode());
        issueHandlingProofService.insertList(proofList);

        if(details != null) {

            issueHandlingDetailsService.deleteByIssueHandlingCode(updateIssueHandling.getIssueHandlingCode());
            issueHandlingDetailsService.insertList(details);
            gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
        }

        redisTemplate.opsForValue().set("GV:UPDATEISSUEHANDING:"+uploadedFileUrl, issueHandling.getIssueHandlingCode(), 1, TimeUnit.DAYS);

        return Result.ok(updateIssueHandling.getIssueHandlingCode());
    }

    private List<IssueHandlingProof> getInsertProofFile(EditIssueHandlingRequest request,
            IssueHandling updateIssueHandling) {
        List<IssueHandlingProof> proofList = issueHandlingProofService.queryByIssueHandlingCode(updateIssueHandling.getIssueHandlingCode());
        Map<String, IssueHandlingProof> fileUrlMap = proofList.stream().collect(Collectors.toMap(IssueHandlingProof :: getProofFileUrl, v -> v, (v1, v2) -> v1));

        Collection<ProofFile> proofFileList = request.getProofFileList().stream()
                .collect(Collectors.toMap(ProofFile::getProofFileUrl, v -> v, (v1, v2) -> v1)).values();
        proofList = new ArrayList<>(proofFileList.size());
        for (ProofFile proofFile : proofFileList) {
            IssueHandlingProof proof = fileUrlMap.get(proofFile.getProofFileUrl());
            if(proof == null || !proof.getProofFileName().equals(proofFile.getProofFileName())) {
                proof = new IssueHandlingProof();
                proof.setIssueHandlingProofCode(UUIDUtils.generateCode());
                proof.setIssueHandlingCode(updateIssueHandling.getIssueHandlingCode());
                proof.setProofType(proofFile.getProofType());
                proof.setProofFileName(proofFile.getProofFileName());
                proof.setProofFileUrl(proofFile.getProofFileUrl());
                proof.setCreateUser(updateIssueHandling.getUpdateUser());
                proof.setCreateTime(updateIssueHandling.getUpdateTime());
            }
            proofList.add(proof);
        }
        return proofList;
    }

	@Override
    public Result<String> submit(IssueHandlingSubmitRequest request) {

        IssueHandling issueHandling = queryByIssueHandlingCode(request.getIssueHandlingCode());
        if (issueHandling == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        if (!IssueHandlingStatusEnum.CREATED.equalsCode(issueHandling.getStatus())
                && !IssueHandlingStatusEnum.REJECT.equalsCode(issueHandling.getStatus())) {
            return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_SUBMIT.code(),
                    ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_SUBMIT.desc());
        }

        int i = updateStatus(issueHandling.getIssueHandlingCode(), IssueHandlingStatusEnum.SUBMIT.code(),
                issueHandling.getStatus(), request.getUpdateUser(),"");
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_SUBMIT.code(),
                    ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_SUBMIT.desc());
        }

		/*ThreadPoolCenter.commonThreadPoolExecute(()->{
            sendFlowNotice(issueHandling, FlowNodeEnum.SUBMIT.getCode(), null, null);
        });*/
        return Result.ok(issueHandling.getIssueHandlingCode());
    }

    @Override
	public Result<String> approve(IssueHandlingApproveRequest request) {
        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        Integer status = null;
        String flowNodeCode = null;
        if (Boolean.TRUE.equals(request.getStatus())) {
            status = IssueHandlingStatusEnum.APPROVE.code();
            flowNodeCode = FlowNodeEnum.APPROVE.getCode();
        } else {
            status = IssueHandlingStatusEnum.REJECT.code();
            flowNodeCode = FlowNodeEnum.REJECTED.getCode();
        }
		IssueHandling issueHandling = queryByIssueHandlingCode(request.getIssueHandlingCode());
		if (issueHandling == null) {
			return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}
        if (!IssueHandlingStatusEnum.SUBMIT.equalsCode(issueHandling.getStatus())) {
			return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.code(), ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.desc());
		}

        if (IssueHandlingStatusEnum.APPROVE.code().equals(status)) {

            int i = updateStatus(issueHandling.getIssueHandlingCode(), status, IssueHandlingStatusEnum.SUBMIT.code(),
                    request.getUpdateUser(), IssueHandlingProcessStatusEnum.PROCESSING.code(),
                    IssueHandlingProcessStatusEnum.CREATED.code(), 0,request.getNotes());
            if (i == 0) {
                return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.code(),
                        ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.desc());
            }

            // 执行
            ThreadPoolCenter.commonThreadPoolExecute(
                    () -> processing(issueHandling.getIssueHandlingCode(), request.getUpdateUser()));
        } else {

            int i = updateStatus(issueHandling.getIssueHandlingCode(), status, IssueHandlingStatusEnum.SUBMIT.code(),
                    request.getUpdateUser(),request.getNotes());
            if (i == 0) {
                return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.code(),
                        ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.desc());
            }
        }
		List<String> emails;
		String createUserEmail = issueHandling.getCreateUserEmail();
		if (!StringUtil.isEmpty(createUserEmail)) {
			emails = Arrays.asList(createUserEmail);
		} else {
            emails = null;
        }
        String finalFlowNodeCode = flowNodeCode;
        //ThreadPoolCenter.commonThreadPoolExecute(()->{sendFlowNotice(issueHandling, finalFlowNodeCode, emails, null);});
		HintManager.clear();
        return Result.ok(issueHandling.getIssueHandlingCode());
	}

	@SuppressWarnings("unchecked")
	private void sendFlowNotice(IssueHandling issueHandling, String flowNodeCode, List<String> emails, Integer detailStatus) {
		IssueHandlingTypeEnum typeEnum = getIssueHandlingTypeEnum(issueHandling.getIssueType());
		if (typeEnum == null) {
			return;
		}
		String flowCode = FlowEnum.ISSUE_HANDLING.getCode() + typeEnum.code();
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
		sendNoticeRequest.setFlowCode(flowCode);
        sendNoticeRequest.setFlowNodeCode(flowNodeCode);
		sendNoticeRequest.setBusinessCode(issueHandling.getIssueHandlingCode());
		Long startId = 0L;
		int pageSize = 500;
		int listSize = 0;
		List<IssueHandlingDetails> detailList = null;
		BigDecimal voucherAmount = BigDecimal.ZERO;
		do {
			detailList = issueHandlingDetailsService.queryByIssueHandlingCode(issueHandling.getIssueHandlingCode(),
					detailStatus, pageSize, startId);
			if(detailList.isEmpty()) {
				break;
			}
			List<String> voucherCodeList = detailList.stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList());
			List<Voucher> voucherList = voucherService.queryByVoucherCodeList(null, voucherCodeList);
			if (!CollectionUtils.isEmpty(voucherList)) {
				for (Voucher voucher : voucherList) {
					BigDecimal denomination = voucher.getDenomination();
					voucherAmount = voucherAmount.add(denomination);
				}
			}
			listSize = detailList.size();
			startId = detailList.get(listSize - 1).getId() + 1;
		} while (listSize == pageSize);
		Map<String, Object> extendParams = JSON.parseObject(JSON.toJSONString(issueHandling), Map.class);
		extendParams.put("voucherAmount", voucherAmount);
		extendParams.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(issueHandling.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		extendParams.put(VoucherBatch.C_UPDATE_TIME, DateUtil.format(issueHandling.getUpdateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		extendParams.put("type", FlowEnum.getFlowDescByCode(flowCode));
		extendParams.put("flowNode", FlowNodeEnum.getByCode(flowNodeCode));
		sendNoticeRequest.setExtendParams(extendParams);
		if (!CollectionUtils.isEmpty(emails)) {
			sendNoticeRequest.setEmails(emails);
		}
		flowNoticeService.send(sendNoticeRequest);
    }

	/**
	 *
	 * @param issueHandlingCode
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	public IssueHandling queryByIssueHandlingCode(String issueHandlingCode) {

		IssueHandling issueHandling = new IssueHandling();
		issueHandling.setIssueHandlingCode(issueHandlingCode);
		return issueHandlingMapper.selectOne(issueHandling);
	}

	/**
	 *
	 * @param issueHandlingCode
	 * @param status
	 * @param oldstatus
	 * @param updateUser
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	private int updateStatus(String issueHandlingCode, Integer status, Integer oldstatus, String updateUser,String approveNotes) {

        return updateStatus(issueHandlingCode, status, oldstatus, updateUser, null, null, null,approveNotes);
	}

	/**
	 *
	 * @param issueHandlingCode
	 * @param status
	 * @param oldstatus
	 * @param updateUser
	 * @param processStatus
	 * @param oldProcessStatus
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	private int updateStatus(String issueHandlingCode, Integer status, Integer oldstatus, String updateUser,
            Integer processStatus, Integer oldProcessStatus, Integer countFailed,String approveNotes) {

		IssueHandlingDto dto = new IssueHandlingDto();
		dto.setIssueHandlingCode(issueHandlingCode);
		dto.setStatus(status);
		dto.setOldStatus(oldstatus);
		dto.setProcessStatus(processStatus);
		dto.setOldProcessStatus(oldProcessStatus);
        dto.setCountFailed(countFailed);
		dto.setUpdateUser(updateUser);
		dto.setUpdateTime(new Date());
		dto.setApproveNotes(approveNotes);
		return issueHandlingMapper.updateStatus(dto);
	}

	@Override
    public Result<String> cancel(IssueHandlingCancelRequest request) {

	    IssueHandling issueHandling = queryByIssueHandlingCode(request.getIssueHandlingCode());
        if(issueHandling == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        if (!(IssueHandlingStatusEnum.CREATED.equalsCode(issueHandling.getStatus())
                || IssueHandlingStatusEnum.REJECT.equalsCode(issueHandling.getStatus()))) {
            return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_CANCEL.code(),
                    ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_CANCEL.desc());
        }

        int i = updateStatus(issueHandling.getIssueHandlingCode(), IssueHandlingStatusEnum.CANCEL.code(), issueHandling.getStatus(), request.getUpdateUser(),"");
        if(i == 0) {
            return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_CANCEL.code(), ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_CANCEL.desc());
        }

        return Result.ok(issueHandling.getIssueHandlingCode());
    }

    /**
	 *
	 * @param issueHandlingCode
	 * @param updateUser
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	private void processing(String issueHandlingCode, String updateUser) {
        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

		IssueHandling issueHandling = queryByIssueHandlingCode(issueHandlingCode);
		if(issueHandling == null) {
			log.info("IssueHandlingprocessing queryByIssueHandlingCode isnull issueHandlingCode={}", issueHandlingCode);
			return;
		}
        if (!IssueHandlingStatusEnum.APPROVE.equalsCode(issueHandling.getStatus())) {
			log.info("IssueHandlingprocessing getStatus issueHandlingCode={}, status={}", issueHandlingCode, issueHandling.getStatus());
			return;
		}

		IssueHandlerBaseService handlerBaseService = getIssueHandlerBaseService(issueHandling.getIssueType());
		if(handlerBaseService == null) {
			log.error("IssueHandlingprocessing Cannot handle this issue type. issueHandlingCode={}, issue type={}",
					issueHandlingCode, issueHandling.getIssueType());
			return;
		}

		Long startId = 0L;
		int pageSize = 500;
		int listSize = 0;
		List<IssueHandlingDetails> detailList = null;
		do {
			detailList = issueHandlingDetailsService.queryByIssueHandlingCode(issueHandlingCode,
					IssueHandlingProcessStatusEnum.CREATED.code(), pageSize, startId);
			if(detailList.isEmpty()) {
				break;
			}
            HintManager.clear();

			detailList = handlerBaseService.execute(detailList, issueHandling.getIssuerCode());
            if (!HintManager.isMasterRouteOnly()) {
                HintManager.clear();
                HintManager hintManager = HintManager.getInstance();
                hintManager.setMasterRouteOnly();
            }
			int i = issueHandlingDetailsService.updateByProcess(detailList, updateUser);
			log.info("IssueHandlingprocessing updateDetailsByProcess issueHandlingCode={}, i={}, startId={}", issueHandlingCode, i, startId);

			listSize = detailList.size();
			startId = detailList.get(listSize - 1).getId() + 1;
		} while (listSize == pageSize);

        int failedCount = issueHandlingDetailsService.countByIssueHandlingCode(issueHandlingCode,
                IssueHandlingProcessStatusEnum.FAILED.code());

        int i = updateStatus(issueHandling.getIssueHandlingCode(), IssueHandlingStatusEnum.APPROVE.code(),
                IssueHandlingStatusEnum.APPROVE.code(), updateUser, IssueHandlingProcessStatusEnum.SUCCESS.code(),
                IssueHandlingProcessStatusEnum.PROCESSING.code(), failedCount,"");
		log.info("IssueHandlingprocessing issueHandlingCode={}, updateStatus i={}", issueHandlingCode, i);

		handlerBaseService.afterExecute(issueHandling);
		List<String> emails = null;
		String createUserEmail = issueHandling.getCreateUserEmail();
		if (!StringUtil.isEmpty(createUserEmail)) {
			emails = Arrays.asList(createUserEmail);
		}
        HintManager.clear();
		//sendFlowNotice(issueHandling, FlowNodeEnum.EXECUTE.getCode(), emails, IssueHandlingProcessStatusEnum.SUCCESS.code());
	}

	@Override
	public PageResult<QueryIssueHandlingByPageResponse> queryIssueHandlingByPage(
			QueryIssueHandlingByPageRequest request) {

		IssueHandlingDto dto = new IssueHandlingDto();
		BeanUtils.copyProperties(request, dto);

		PageMethod.startPage(request.getPageNum(), request.getPageSize());
		List<IssueHandling> issueHandlingList = issueHandlingMapper.selectSelective(dto);
		if (CollectionUtils.isEmpty(issueHandlingList)) {
            return PageResult.ok(Collections.emptyList(), 0L);
        }


        List<String> userCodeList = issueHandlingList.stream().map(IssueHandling::getCreateUser)
                .collect(Collectors.toList());
        Map<String, String> userMap = gvUserAccountService.queryFullNameByCodeList(userCodeList);

		List<QueryIssueHandlingByPageResponse> responseList = new ArrayList<>(request.getPageSize());
		for (IssueHandling issueHandling : issueHandlingList) {
			QueryIssueHandlingByPageResponse response = new QueryIssueHandlingByPageResponse();
			BeanUtils.copyProperties(issueHandling, response);
            response.setCreateUserName(userMap.get(issueHandling.getCreateUser()));
			responseList.add(response);
		}

		PageInfo<IssueHandling> pageInfo = new PageInfo<>(issueHandlingList);
		return PageResult.ok(responseList, pageInfo.getTotal());
	}

	@Override
	public Result<GetIssueHandlingResponse> getIssueHandling(GetIssueHandlingRequest request) {

        //主库
        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }


		IssueHandling issueHandling = queryByIssueHandlingCode(request.getIssueHandlingCode());
		if(issueHandling == null) {
			return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}

		GetIssueHandlingResponse response = new GetIssueHandlingResponse();
		BeanUtils.copyProperties(issueHandling, response);

		List<IssueHandlingProof> proofList = issueHandlingProofService.queryByIssueHandlingCode(issueHandling.getIssueHandlingCode());

		List<String> userCodeList = proofList.stream().map(IssueHandlingProof :: getCreateUser).collect(Collectors.toList());
        userCodeList.add(issueHandling.getCreateUser());
        Map<String, String> userMap = gvUserAccountService.queryFullNameByCodeList(userCodeList);

        response.setCreateUserName(userMap.get(issueHandling.getCreateUser()));

		List<ProofFileInfo> proofFileList = new ArrayList<>(proofList.size());
		for (IssueHandlingProof issueHandlingProof : proofList) {
			ProofFileInfo proofFile = new ProofFileInfo();
			BeanUtils.copyProperties(issueHandlingProof, proofFile);
            proofFile.setCreateUserName(userMap.get(issueHandlingProof.getCreateUser()));
			proofFileList.add(proofFile);
		}
		response.setProofFileList(proofFileList);

        int countFailed = issueHandlingDetailsService.countByIssueHandlingCode(issueHandling.getIssueHandlingCode(),
                IssueHandlingProcessStatusEnum.FAILED.code());
        response.setCountFailed(countFailed);
        HintManager.clear();
		return Result.ok(response);
	}

    @Override
    public Map<String, IssueHandling> queryMapIssueHandingByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) return new HashMap<>();
        Example example = new Example(IssueHandling.class);
        example.createCriteria().andIn(IssueHandling.C_ISSUE_HANDLING_CODE,codes)
                                .andEqualTo(IssueHandling.C_ISSUE_TYPE,IssueHandlingTypeEnum.BULK_DEACTIVATE.code());
        List<IssueHandling> list = issueHandlingMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return new HashMap<>();
        return list.stream().collect(Collectors.toMap(IssueHandling::getIssueHandlingCode, Function.identity()));
    }

    @Override
    public String getNotesByMaxIdInVoucherCodes(String transactionId) {


        List<String> notesByMaxTransactionCode = issueHandlingMapper.getNotesByMaxTransactionCode(transactionId);
        return issueHandlingMapper.getNotesByMaxIdInVoucherCodes(notesByMaxTransactionCode);
    }


    @Override
    public void resendRegenerateActivationCodeEmail(ResendRegenerateActivationCodeEmailRequest request){

        regenerateActivationCodeService.resendRegenerateActivationCodeEmail(request.getIssueHandlingCode(), request.getEmail(), request.getVoucherCode(), request.getUpdateUser());
    }

    @Override
    public PageResult<RegenerateActivationCodeVoucherInfoResponse> regenerateActivationCodeVoucherInfo(RegenerateActivationCodeVoucherInfoRequest request) {

        PageParam pageParam = new PageParam();
        pageParam.setPageNum(request.getPageNum());
        pageParam.setPageSize(request.getPageSize());
        PageResult<IssueHandlingDetails> issueHandlingDetails = issueHandlingDetailsService.queryByIssueHandlingCodeByCode(request.getIssueHandlingCode(), pageParam);

        List<String> voucherCodes = issueHandlingDetails.getData().getList().stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList());
        List<Voucher> vouchers = voucherService.queryByVoucherCodeList(request.getIssueCode(), voucherCodes);
        Map<String, Voucher> voucherMap = vouchers.stream().collect(Collectors.toMap(Voucher::getVoucherCode, x -> x));
        List<String> cpgCodes = vouchers.stream().map(Voucher::getCpgCode).collect(Collectors.toList());
        Map<String, Cpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(cpgCodes);


        List<RegenerateActivationCodeVoucherInfoResponse> responses = issueHandlingDetails.getData().getList().stream()
                .map(issueHandlingDetail -> {
                    Voucher voucher = voucherMap.getOrDefault(issueHandlingDetail.getVoucherCode(), Voucher.builder().voucherStatus(9999).build());

                    RegenerateActivationCodeVoucherInfoResponse response = new RegenerateActivationCodeVoucherInfoResponse();
                    response.setVoucherCode(issueHandlingDetail.getVoucherCode());
                    response.setVpg(cpgMap.getOrDefault(voucher.getCpgCode(),new Cpg().setCpgName("")).getCpgName());
                    response.setVoucherStatus(VoucherStatusResultEnum.getDescByCode(voucher.getStatus()));
                    response.setCustomerEmail(issueHandlingDetail.getReceiverEmail());
                    response.setValidStatus(issueHandlingDetail.getResult());
                    return response;
                })
                .collect(Collectors.toList());

        return PageResult.ok(responses, issueHandlingDetails.getData().getTotal());
    }


}
