package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.IssueHandlingMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName IssueHandlingQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:49
 * @Version V1.0
 **/
@Component
public class IssueHandlingQueryImpl implements QuerySupport<IssueHandling> {

    private static final IssueHandling EMPTY = new IssueHandling();

    @Autowired
    private IssueHandlingMapper issueHandlingMapper;

    @Override
    public List<IssueHandling> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(IssueHandling.class);
        example.createCriteria().andIn(IssueHandling.C_ISSUE_HANDLING_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<IssueHandling> list = issueHandlingMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<IssueHandling, String> codeMapper() {
        return IssueHandling::getIssueHandlingCode;
    }

    @Override
    public IssueHandling emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<IssueHandling> supportType() {
        return IssueHandling.class;
    }
}
