package com.gtech.gvcore.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.gtech.basic.masterdata.web.entity.MasterDataValueEntity;
import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.cpg.QueryCpgsByPageRequest;
import com.gtech.gvcore.common.request.voucher.CreateVoucherRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.request.vouchermerge.CancelMergeVoucherRequest;
import com.gtech.gvcore.common.request.vouchermerge.MergeVoucherRequest;
import com.gtech.gvcore.common.response.cpg.QueryCpgsByPageResponse;
import com.gtech.gvcore.common.response.vouchermerge.MergeVoucherResponse;
import com.gtech.gvcore.dao.mapper.VoucherMergeMapper;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherMerge;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CpgTypeService;
import com.gtech.gvcore.service.VoucherMergeService;
import com.gtech.gvcore.service.VoucherService;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

@Service
public class VoucherMergeServiceImpl implements VoucherMergeService {

	@Lazy
	@Autowired
	private VoucherService voucherService;
	
	@Autowired
	private CpgTypeService cpgTypeService;

	@Autowired
	private CpgService cpgService;

	@Autowired
	private MasterDataValueService masterDataValueService;

	@Autowired
	private VoucherNumberHelper voucherNumberHelper;

	@Autowired
	private VoucherMergeMapper voucherMergeMapper;

	public static final String CPG_TYPE_PREFIX = "105";

	@Override
	@Transactional
	public MergeVoucherResponse merge(MergeVoucherRequest request) {
		if (request == null || CollectionUtils.isEmpty(request.getVoucherCodeList())) {
			return null;
		}
		List<Voucher> voucherList = voucherService.queryByVoucherCodeList(null, request.getVoucherCodeList());
		if (CollectionUtils.isEmpty(voucherList)) {
			throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}
		BigDecimal denomination = BigDecimal.ZERO;
		List<String> errorCodeList = new ArrayList<>();
		List<String> mergeCodeList = new ArrayList<>();
		for (Voucher voucher : voucherList) {
			if (GvcoreConstants.STATUS_ENABLE.intValue() == voucher.getVoucherStatus().intValue()
					&& VoucherStatusEnum.VOUCHER_ACTIVATED.getCode() == voucher.getStatus().intValue()
					&& voucher.getVoucherEffectiveDate().compareTo(new Date()) > 0) {
				denomination = denomination.add(voucher.getDenomination());
				mergeCodeList.add(voucher.getVoucherCode());
			} else {
				errorCodeList.add(voucher.getVoucherCode());
			}
		}
		if (CollectionUtils.isEmpty(mergeCodeList)) {
			throw new GTechBaseException(ResultErrorCodeEnum.MERGE_VOUCHER_ERROR.code(), ResultErrorCodeEnum.MERGE_VOUCHER_ERROR.desc());
		}
		MergeVoucherResponse mergeVoucherResponse = new MergeVoucherResponse();
		String mergeMainCode = voucherNumberHelper.voucherCodeElectronic(CPG_TYPE_PREFIX, denomination, Calendar.getInstance().get(Calendar.YEAR) + "","");
		QueryCpgsByPageResponse queryCpgsByPageResponse = getCpg();
		if (queryCpgsByPageResponse == null) {
			throw new GTechBaseException(ResultErrorCodeEnum.MERGE_VOUCHER_CPG_NOT_FOUND_ERROR.code(),
					ResultErrorCodeEnum.MERGE_VOUCHER_CPG_NOT_FOUND_ERROR.desc());
		}
		// 1. create merge voucher
		CreateVoucherRequest createVoucherRequest = new CreateVoucherRequest();
		createVoucherRequest.setCreateUser(request.getOperateUser());
		createVoucherRequest.setVoucherCode(mergeMainCode);
		createVoucherRequest.setVoucherBatchCode(mergeMainCode);
		createVoucherRequest.setDenomination(denomination);
		createVoucherRequest.setIssuerCode(getMapIssuerCode());
		createVoucherRequest.setCpgCode(queryCpgsByPageResponse.getCpgCode());
		createVoucherRequest.setMopCode(queryCpgsByPageResponse.getArticleMopCode());
		createVoucherRequest.setVoucherBarcode(voucherNumberHelper.barCode27Bit(mergeMainCode));
		createVoucherRequest.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
		createVoucherRequest.setCreateTime(new Date());
		Integer effectiveHour = queryCpgsByPageResponse.getEffectiveHour();
		createVoucherRequest.setVoucherEffectiveDate(DateUtils.addHours(new Date(), effectiveHour == null ? 24 : effectiveHour));
		voucherService.createVoucher(createVoucherRequest);
		
		// 2. update original voucher
		List<VoucherMerge> mergeList = new ArrayList<>();
		for (String voucherCode : mergeCodeList) {
			UpdateVoucherStatusRequest updateVoucherStatusRequest = new UpdateVoucherStatusRequest();
			updateVoucherStatusRequest.setVoucherCode(voucherCode);
			updateVoucherStatusRequest.setStatus(VoucherStatusEnum.VOUCHER_USED.getCode());
			updateVoucherStatusRequest.setOldStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
			voucherService.updateVoucherStatus(updateVoucherStatusRequest);

			VoucherMerge voucherMerge = new VoucherMerge();
			voucherMerge.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
			voucherMerge.setVoucherMainCode(mergeMainCode);
			voucherMerge.setVoucherMergeCode(voucherCode);
			voucherMerge.setCreateUser(request.getOperateUser());
			mergeList.add(voucherMerge);
		}
		// 3. create merge relation
		voucherMergeMapper.insertList(mergeList);

		mergeVoucherResponse.setVoucherCode(mergeMainCode);
		if (!CollectionUtils.isEmpty(errorCodeList)) {
			mergeVoucherResponse.setMessage(ResultErrorCodeEnum.MERGE_VOUCHER_ERROR.desc() + ":" + JSON.toJSONString(errorCodeList));
		}
		
		mergeCodeList.add(mergeMainCode);
		return mergeVoucherResponse;
	}

	private QueryCpgsByPageResponse getCpg() {
		CpgType cpgType = cpgTypeService.getCpgTypeByPrefix(CPG_TYPE_PREFIX);
		if (cpgType == null) {
			return null;
		}
		String cpgTypeCode = cpgType.getCpgTypeCode();
		QueryCpgsByPageRequest queryCpgsByPageRequest = new QueryCpgsByPageRequest();
		queryCpgsByPageRequest.setCpgTypeCode(cpgTypeCode);
		PageResult<QueryCpgsByPageResponse> pageResult = cpgService.queryCpgsByPage(queryCpgsByPageRequest);
		if (pageResult == null || CollectionUtils.isEmpty(pageResult.getData().getList())) {
			return null;
		}
		return pageResult.getData().getList().get(0);
	}

	private String getMapIssuerCode() {
		MasterDataValueEntity entiy = new MasterDataValueEntity();
		entiy.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
		entiy.setValueCode(GvcoreConstants.MAP_ISSUER_CODE_KEY);
		String issuerCode = "MAP";
		MasterDataValueEntity masterDataValueEntity = masterDataValueService.getByCode(entiy);
		if (masterDataValueEntity != null) {
			issuerCode = masterDataValueEntity.getValueValue();
		}
		return issuerCode;
	}

	@Override
	@Transactional
	public void cancelMerge(CancelMergeVoucherRequest request) {
		if (request == null || StringUtil.isEmpty(request.getVoucherCode())) {
			return;
		}
		Voucher voucher = voucherService.getVoucherByCode(request.getVoucherCode());
		if (voucher == null) {
			throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}
		if (!VoucherStatusEnum.VOUCHER_ACTIVATED.getCode().equals(voucher.getStatus())) {
			throw new GTechBaseException(ResultErrorCodeEnum.MERGE_VOUCHER_ERROR.code(), ResultErrorCodeEnum.MERGE_VOUCHER_ERROR.desc());
		}
		VoucherMerge mergeRecord = new VoucherMerge();
		mergeRecord.setVoucherMainCode(request.getVoucherCode());
		mergeRecord.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
		List<VoucherMerge> mergeList = voucherMergeMapper.select(mergeRecord);
		if (CollectionUtils.isEmpty(mergeList)) {
			throw new GTechBaseException(ResultErrorCodeEnum.MERGE_VOUCHER_NOT_FOUND_ERROR.code(), ResultErrorCodeEnum.MERGE_VOUCHER_NOT_FOUND_ERROR.desc());
		}
		// 1. change voucher to disable
		UpdateVoucherStatusRequest updateVoucherStatus = new UpdateVoucherStatusRequest();
		updateVoucherStatus.setVoucherCode(request.getVoucherCode());
		updateVoucherStatus.setVoucherStatus(GvcoreConstants.STATUS_DISABLE);
		updateVoucherStatus.setOldVoucherStatus(GvcoreConstants.STATUS_ENABLE);
		voucherService.updateVoucherStatus(updateVoucherStatus);

		// 2. change origin voucher to activated
		List<String> voucherCodeList = new ArrayList<>();
		for (VoucherMerge voucherMerge : mergeList) {
			String voucherCode = voucherMerge.getVoucherMergeCode();
			UpdateVoucherStatusRequest updateVoucherStatusRequest = new UpdateVoucherStatusRequest();
			updateVoucherStatusRequest.setVoucherCode(voucherCode);
			updateVoucherStatusRequest.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
			updateVoucherStatusRequest.setOldStatus(VoucherStatusEnum.VOUCHER_USED.getCode());
			voucherService.updateVoucherStatus(updateVoucherStatusRequest);
			
			voucherCodeList.add(voucherCode);
		}
		// 3. update voucher merge status
		updateMergeVoucherStatus(request.getVoucherCode(), VoucherStatusEnum.VOUCHER_CANCELLED.getCode(), VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
		
		voucherCodeList.add(request.getVoucherCode());
	}

	@Override
	public void useMergeVoucher(String voucherCode) {
		updateMergeVoucherStatus(voucherCode, VoucherStatusEnum.VOUCHER_USED.getCode(), VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
	}

	private void updateMergeVoucherStatus(String voucherCode, Integer status, Integer oldStatus) {
		Example example = new Example(VoucherMerge.class);
		example.createCriteria().andEqualTo(VoucherMerge.C_VOUCHER_MAIN_CODE, voucherCode).andEqualTo(VoucherMerge.C_STATUS, oldStatus);
		VoucherMerge voucherMerge = new VoucherMerge();
		voucherMerge.setStatus(status);
		int count = voucherMergeMapper.updateByConditionSelective(voucherMerge, example);
		if (count > 0) {
			// 生成流水 TODO
		}
	}
}
