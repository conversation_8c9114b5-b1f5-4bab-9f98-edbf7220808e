package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.pos.CreatePosRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.pos.QueryOutletByPosIdRequest;
import com.gtech.gvcore.common.request.pos.QueryPosListRequest;
import com.gtech.gvcore.common.request.pos.UpdatePosRequest;
import com.gtech.gvcore.common.request.pos.UpdatePosStatusRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.dao.model.Pos;

import java.util.List;
import java.util.Map;

public interface PosService {


    Result<String> createPos(CreatePosRequest request);
    Result<String> updatePos(UpdatePosRequest request);
    PageResult<PosResponse> queryPosList(QueryPosListRequest request);
    List<Pos> queryPosALL();
    Result<String> updatePosStatus(UpdatePosStatusRequest request);
    Result<PosResponse> getPos(GetPosRequest request);
    Result<OutletResponse> queryOutletByPosId(QueryOutletByPosIdRequest request);

    /**
     * 
     * <AUTHOR>
     * @param posCodeList
     * @return
     * @date 2022年6月23日
     */
    Map<String, String> queryNameByPosCodeList(List<String> posCodeList);
}
