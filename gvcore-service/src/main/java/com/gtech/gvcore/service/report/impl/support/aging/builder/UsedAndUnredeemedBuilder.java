package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertSupport;
import com.gtech.gvcore.service.report.impl.bean.aging.UsedAndUnredeemedBean;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.UsedAndUnredeemedBo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName AgingUsedAndUnredeemedReportBuilder
 * @Description Used and Unredeemed
 * <AUTHOR>
 * @Date 2022/10/31 19:58
 * @Version V1.0
 **/
@Component
public class UsedAndUnredeemedBuilder implements AgingSheetBuilder, ReportVoucherStatusConvertSupport, ReportProportionDataFunction {

    private static final int SCALE = 1;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        return ReportExportTypeEnum.AGING_USED_AND_UNREDEEMED_REPORT;
    }

    @Override
    public AgingSheet builder(final ReportContext context) {

        final Collection<UsedAndUnredeemedBo> group = this.group(context);

        return new AgingSheet()
                .setHead(null)
                .setSheetName(exportTypeEnum().getSheetName())
                .addSheetData("uau", exportTypeEnum(), getResult(group));
    }

    private Collection<UsedAndUnredeemedBo> group(final ReportContext context) {

        //result
        final Map<String, UsedAndUnredeemedBo> resultMap = new HashMap<>();

        //sales data
        final List<AgingBo> list = context.getCacheList(AgingSheetBuilder.SALES_DATA_KEY);

        //join data
        final JoinDataMap<Customer> customerMap = context.getCacheJoinMap(AgingSheetBuilder.CUSTOMER_MAP_KEY);
        final JoinDataMap<Voucher> voucherMap = context.getCacheJoinMap(AgingSheetBuilder.VOUCHER_MAP_KEY);

        //for
        list.stream()
                // filter customer exist
                .filter(e -> customerMap.containsKey(e.getCustomerCode()))
                // filter voucher exist
                .filter(e -> voucherMap.containsKey(e.getVoucherCode()))
                //for
                .forEach(e -> {

                    //get voucher status
                    ReportVoucherStatusEnum statusEnum = getVoucherStatus(voucherMap.findValue(e.getVoucherCode()));

                    // init bo
                    UsedAndUnredeemedBo bo = resultMap.computeIfAbsent(e.getCustomerCode(), k -> new UsedAndUnredeemedBo()
                            .setCustomerCode(e.getCustomerCode())
                            .setCustomerName(customerMap.findValue(e.getCustomerCode()).getCustomerName()));

                    // add count
                    if (ReportVoucherStatusEnum.VOUCHER_REDEEMED == statusEnum) bo.addUse();
                    else bo.addUnUse();
                });

        return resultMap.values();
    }

    private List<UsedAndUnredeemedBean> getResult(Collection<UsedAndUnredeemedBo> values) {

        final UsedAndUnredeemedBo totalBo = new UsedAndUnredeemedBo().setCustomerName("Total");
        values.forEach(e -> totalBo.addUnUse(e.getUnUse()).addUse(e.getUse()));

        final ArrayList<UsedAndUnredeemedBo> result = new ArrayList<>(values);
        result.add(totalBo);

        return result.stream().map(e ->
                new UsedAndUnredeemedBean().setCustomerName(e.getCustomerName())
                        .setUseScale(getProportion(e.getUse(), e.getCount(), SCALE))
                        .setUnUseScale(getProportion(e.getUnUse(), e.getCount(), SCALE))
        ).collect(Collectors.toList());
    }

    @Override
    public Class<?> getExportDataClass() {
        return UsedAndUnredeemedBean.class;
    }

}
