package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.request.customerorder.AddSendEmailRequest;
import com.gtech.gvcore.common.request.customerorder.ApproveCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.CancelReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.CancelReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.DelPaymentVoucherRequest;
import com.gtech.gvcore.common.request.customerorder.DeliverCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.GetCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.request.customerorder.NonSystemCreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.QueryCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.ReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.ReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.SendCustomerOrderEmailRequest;
import com.gtech.gvcore.common.request.customerorder.SubmitCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerInfoInCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UploadPaymentVoucherRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderResponse;
import com.gtech.gvcore.common.response.customerorder.QueryCustomerOrderResponse;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.CustomerOrderReceiver;
import com.gtech.gvcore.dto.GetCpgQuantityNumParam;
import com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18 14:45
 */


public interface CustomerOrderService {
    /**
     * Create customer order
     *
     * @param createCustomerOrderRequest Request arguments
     * @return Result
     */
    Result<String> createCustomerOrder(CreateCustomerOrderRequest createCustomerOrderRequest);

    List<CustomerOrderDetails> insertCustomerOrderDetails(CreateCustomerOrderRequest createCustomerOrderRequest, CustomerOrder customerOrder);

    /**
     * Query customer order list
     *
     * @param queryCustomerOrderRequest Request conditions
     * @return Result
     */
    PageResult<QueryCustomerOrderResponse> queryCustomerOrder(QueryCustomerOrderRequest queryCustomerOrderRequest);

    /**
     * Get a data for customer order detail
     *
     * @param getCustomerOrderRequest Customer order code
     * @return Data
     */
    Result<GetCustomerOrderResponse> getCustomerOrder(GetCustomerOrderRequest getCustomerOrderRequest);

	Map<String, Object> getExtendsParams(String customerOrderCode);

    /**
     * edit data for customer order
     *
     * @param updateCustomerOrderRequest request data
     * @return result
     */
    Result<Void> updateCustomerOrder(UpdateCustomerOrderRequest updateCustomerOrderRequest);

    /**
     * Generate purchase order number
     *
     * @return number
     */
    Result<String> generatePurchaseOrderNumber(String storeCode);

    Result<String> updateRedisOrderNumber(String storeCode, String poNumber);


    Result<Void> submitCustomerOrder(SubmitCustomerOrderRequest submitCustomerOrderRequest);

    Result<String> approveCustomerOrder(ApproveCustomerOrderRequest approveCustomerOrderRequest);


    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年3月23日
     */
    Result<String> issuance(IssuanceRequest request);

    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年5月18日
     */
    Result<String> issuanceAndNoticeNextNode(IssuanceRequest request);

    /**
     *
     * @param approveNodeRecordRequest
     * @return
     * <AUTHOR>
     * @date 2022年4月20日
     */
    Result<String> approveCustomerOrderRelease(ApproveNodeRecordRequest approveNodeRecordRequest);

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年3月23日
     */
    Result<String> release(ReleaseRequest request);

    Result<String> receive(ReceiveRequest request);


    Result<Boolean> approveCustomerOrderAble(ReleaseApproveAbleRequest releaseApproveAbleRequest);

    Result<String> nonSystemCreateCustomerOrder(NonSystemCreateCustomerOrderRequest nonSystemCreateCustomerOrderRequest);

    void deliverCustomerOrder(DeliverCustomerOrderRequest deliverCustomerOrderRequest);

    Result<Void> cancelRelease(CancelReleaseRequest cancelReleaseRequest);

    Result<Void> uploadPaymentVoucher(UploadPaymentVoucherRequest uploadPaymentVoucherRequest);

    Result<Void> delPaymentVoucher(DelPaymentVoucherRequest delPaymentVoucherRequest);

    Result<Void> sendEmail(SendCustomerOrderEmailRequest sendCustomerOrderEmailRequest);

    CustomerOrder getCustomerByCustomerOrderDetailCode(String customerOrderDetailCode);

    /**
     * @param customerOrderCode
     * @return
     * <AUTHOR>
     * @date 2022年5月6日
     */
    CustomerOrder queryByCustomerOrderCode(String customerOrderCode);

    Result<Void> updateCustomerInfoInCustomerOrder(UpdateCustomerInfoInCustomerOrderRequest updateCustomerInfoInCustomerOrderRequest);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月14日
     */
    List<SumCustomerOrderGroupByArticle> sumCustomerOrderGroupByArticle(CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月14日
     */
    List<SumCustomerOrderGroupByArticle> sumCancelCustomerOrderGroupByArticle(CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月14日
     */
    List<CustomerOrder> sumGroupByMeansOfPaymentCode(CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月14日
     */
    List<CustomerOrder> sumCancelGroupByMeansOfPaymentCode(CustomerOrderDto dto);

    CustomerOrderReceiver getOrderReceiver(String customerOrderCode);

    /**
     *
     * 重发卡券邮件
     *
     * @param emailCode 邮件编码
     */
    void resendVoucherEmail(String emailCode);

    void addSendVoucherEmail(AddSendEmailRequest request);

    int getCpgQuantityNum(GetCpgQuantityNumParam getCpgQuantityNumParam);

    CustomerOrder getCustomerOrder(String customerOrderCode, String batchCode);
    CustomerOrder getCustomerOrder(String customerOrderCode);

    Result<String> delivery();

    Result<String> cancelReceive(CancelReceiveRequest request) throws Exception;
    int updateByRelease(CustomerOrderDto dto);
    int rollbackOrder(String customerOrderCode, CustomerOrderStatusEnum statusEnum);
    int updateCustomerBatchCode(String customerOrderCode, String batchCode);
    int loadingOrder(String customerOrderCode, CustomerOrderStatusEnum statusEnum);
    int updateStatus(String customerOrderCode, String status, String oldStatus, String updateUser);
    List<CustomerOrderDetails> queryCustomerOrderDetails(String customerOrderCode);
    List<GetCustomerOrderDetailsResponse> selectCustomerDetails(String customerOrderCode,  Integer deleteStatus);

}
