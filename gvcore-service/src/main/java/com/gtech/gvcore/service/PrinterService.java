package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.printer.CreatePrinterRequest;
import com.gtech.gvcore.common.request.printer.GetPrinterRequest;
import com.gtech.gvcore.common.request.printer.QueryPrinterByPageRequest;
import com.gtech.gvcore.common.request.printer.UpdatePrinterRequest;
import com.gtech.gvcore.common.request.printer.UpdatePrinterStatusRequest;
import com.gtech.gvcore.common.response.productcategory.printer.GetPrinterResponse;
import com.gtech.gvcore.common.response.productcategory.printer.QueryPrinterByPageResponse;
import com.gtech.gvcore.dao.model.Printer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface PrinterService {

    Result<Object> createPrinter(CreatePrinterRequest createPrinterRequestParam);

    PageResult<QueryPrinterByPageResponse> queryPrinterDataByPage(QueryPrinterByPageRequest request);

    Result<Void> updatePrinterStatus(UpdatePrinterStatusRequest request);

    Result<Object> deletePrinterById(String printerCode);

    Result<Object> updatePrinter(UpdatePrinterRequest request);

    Result<GetPrinterResponse> getPrinter(GetPrinterRequest request);

    /**
     * 
     * <AUTHOR>
     * @param printerCodeList
     * @return
     * @date 2022年3月7日
     */
    List<Printer> queryBasicInfoByPrinterCodeList(List<String> printerCodeList);

    /**
     * @param printerCodeList
     * @return
     * <AUTHOR>
     * @date 2022年5月25日
     */
    int countByPrinterCodeList(List<String> printerCodeList);

}
