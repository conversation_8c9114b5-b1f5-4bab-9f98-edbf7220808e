package com.gtech.gvcore.service.report.extend.joindate;

import com.gtech.gvcore.common.utils.GvConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName GetMapByCodeSupport
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 13:55
 * @Version V1.0
 **/
@Slf4j
@Component
public class JoinDateFunctionHelper implements ApplicationContextAware {

    private static final Map<Class<?>, QuerySupport<?>> QUERY_SUPPORT_HASH_MAP = new HashMap<>();

    public static  <T> GenerateReportJoinMapFunction<T> getGenerateReportJoinMapFunction(Class<T> type, T defaultValue, String... selectFields) {

        final QuerySupport<T> querySupport = gettQuerySupport(type);

        log.info("JoinDateFunctionHelper join => join data type: {}, selectFields: {}", type, selectFields);

        final T defValue = defaultValue == null ? querySupport.emptyObject() : defaultValue;

        return codes -> {

            final JoinDataMap<T> tJoinDataMap = new JoinDataMap<>(defValue);

            ListUtils.partition(new ArrayList<>(codes), 1000)
                    .parallelStream()
                    .forEach(codeSubList -> tJoinDataMap.putAll(querySupport.queryByCode(new ArrayList<>(codeSubList), selectFields), querySupport.codeMapper()));

            return tJoinDataMap;

        };
    }

    private static <T> QuerySupport<T> gettQuerySupport(Class<T> type) {

        if (!QUERY_SUPPORT_HASH_MAP.containsKey(type)) throw new UnsupportedOperationException("getMapByCode => not config type.");

        @SuppressWarnings("unchecked")
        QuerySupport<T> querySupport = (QuerySupport<T>) QUERY_SUPPORT_HASH_MAP.get(type);

        return querySupport;
    }

    public static  <T> JoinDataMap<T> getEmptyReportJoinDataMap(Class<T> type) {

        QuerySupport<T> querySupport = gettQuerySupport(type);

        return new JoinDataMap<>(querySupport.emptyObject());
    }

    public static  <T> GenerateReportJoinFunction<T> nonNullGetByCode(Class<T> type) {

        QuerySupport<T> querySupport = gettQuerySupport(type);

        return code -> GvConvertUtils.toObject(querySupport.getByCode(code), querySupport.emptyObject());
    }

    public static  <T> GenerateReportJoinFunction<T> getByCode(Class<T> type) {

        return code -> gettQuerySupport(type).getByCode(code);
    }

    public interface GenerateReportJoinMapFunction<T> {

        JoinDataMap<T> get(Collection<String> codes);
    }

    public interface GenerateReportJoinFunction<T> {

        T get(String codes);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {

        applicationContext.getBeansOfType(QuerySupport.class)
                .values()
                .forEach(e -> {

                    if (null == e.supportType()) {
                        log.warn("报表关联数据支持加载 跳过支持警告, 支持来源:{} 未配置的支持项 ", e.getClass().getSimpleName());
                        return;
                    }

                    log.info("报表关联数据支持加载 {}, 支持来源:{}", e.supportType().getSimpleName(), e.getClass().getSimpleName());

                    QUERY_SUPPORT_HASH_MAP.put(e.supportType(), e);
                });

    }

}

