package com.gtech.gvcore.service.report.export.snapshoot.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName ReportAmountValue
 * @Description 报表金额格式化注解
 * <AUTHOR>
 * @Date 2023/02/15 18:00
 * @Version V1.0
 **/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ReportAmountValue {


}
