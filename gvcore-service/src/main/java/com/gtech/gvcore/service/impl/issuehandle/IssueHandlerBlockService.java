package com.gtech.gvcore.service.impl.issuehandle;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlerBaseService;

import tk.mybatis.mapper.entity.Example;

@Service
public class IssueHandlerBlockService extends IssueHandlerValidateService implements IssueHandlerBaseService {

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.BULK_DEACTIVATE;
    }

    @Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {

		return check(details, issuerCode);
    }

    @Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {

		List<IssueHandlingDetails> check = check(details, issuerCode);

        List<String> successVoucherCodes = check.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(successVoucherCodes)){
            performAction(successVoucherCodes);
        }


        return check;
    }


	private List<IssueHandlingDetails> check(List<IssueHandlingDetails> details, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
		checkIfExist(details, getIssueHandlingType(), issuerCode);

        return details;
    }

    private int performAction(List<String> voucherCodes) {
        Example example = new Example(Voucher.class);
        example.createCriteria().andIn(Voucher.C_VOUCHER_CODE, voucherCodes);
        Voucher voucher = new Voucher();
        voucher.setVoucherStatus(GvcoreConstants.STATUS_DISABLE);
        return voucherMapper.updateByConditionSelective(voucher, example);
    }


}


