package com.gtech.gvcore.service.report.extend.joindate;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName GetMapByCode
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 11:00
 * @Version V1.0
 **/
public interface GetJoinDateMapSupport {

    /**
     * 通过编码获得一个指定类型的对象 如果为空则返回支持类中的默认类型
     *
     * @param code 编码
     * @param type 期望返回的结果类型
     * @param <O>  结果类型
     * @return result
     */
    default <O> O nonNullGetByCode(String code, Class<O> type) {

        return JoinDateFunctionHelper.nonNullGetByCode(type).get(code);
    }

    /**
     * 通过编码获得一个指定类型的对象
     *
     * @param code 编码
     * @param type 期望返回的结果类型
     * @param <O>  结果类型
     * @return result
     */
    default <O> O getByCode(String code, Class<O> type) {

        return JoinDateFunctionHelper.getByCode(type).get(code);
    }

    /**
     * 根据传入的编码获得对应的 map
     *
     * @param codes 编码集合
     * @param type  期望返回的结果类型
     * @param <O>   结果类型
     * @return JoinDataMap
     */
    default <O> JoinDataMap<O> getMapByCode(Collection<String> codes, Class<O> type) {

        return getMapByCode(codes, type, (O)null);
    }

    default <O> JoinDataMap<O> getMapByCode(Collection<String> codes, Class<O> type, String... selectFields) {

        return getMapByCode(codes, type, null, selectFields);
    }

    /**
     * 根据传入的编码获得对应的 map
     *
     * @param codes        编码集合
     * @param type         期望返回的结果类型
     * @param defaultValue 为空时默认对象 为空则取支持类中返回的默认对象
     * @param <O>          结果类型
     * @return JoinDataMap
     */
    default <O> JoinDataMap<O> getMapByCode(Collection<String> codes, Class<O> type, O defaultValue, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return JoinDateFunctionHelper.getEmptyReportJoinDataMap(type);

        return JoinDateFunctionHelper.getGenerateReportJoinMapFunction(type, defaultValue, selectFields).get(codes);
    }


    /**
     * 根据传入参数获得编码集合
     *
     * @param list   bean
     * @param mapper bean 获取编码映射字段
     * @param <C>    bean类型
     * @return 通过mapper 获得的编码集合
     */
    default <C> List<String> getCodeList(Collection<C> list, Function<C, String> mapper) {

        if (CollectionUtils.isEmpty(list)) return new ArrayList<>();

        return list.stream().map(mapper).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    /**
     * 根据传入的 参数获得对应的 map
     *
     * @param list   bean
     * @param mapper bean 获取编码映射字段
     * @param type   期望返回的结果类型
     * @param <O>    结果类型
     * @param <C>    bean类型
     * @return JoinDataMap
     */
    default <O, C> JoinDataMap<O> getMapByCode(Collection<C> list, Function<C, String> mapper, Class<O> type) {

        List<String> codes = this.getCodeList(list, mapper);

        return this.getMapByCode(codes, type);
    }

    default <O, C> JoinDataMap<O> getMapByCode(Collection<C> list, Function<C, String> mapper, Class<O> type, String... selectField) {

        List<String> codes = this.getCodeList(list, mapper);

        return this.getMapByCode(codes, type, selectField);
    }

    /**
     * 根据传入的 参数获得对应的 map
     *
     * @param list        bean
     * @param mapperArray bean 获取编码映射字段
     * @param type        期望返回的结果类型
     * @param <O>         结果类型
     * @param <C>         bean类型
     * @return JoinDataMap
     */
    default <O, C> JoinDataMap<O> getMapByCode(Collection<C> list, List<Function<C, String>> mapperArray, Class<O> type) {

        Collection<String> codes = this.joinCodeList(list, mapperArray);

        return this.getMapByCode(codes, type);
    }

    /**
     * 根据传入的 参数获得对应的 map
     *
     * @param list         bean
     * @param mapper       bean 获取编码映射字段
     * @param type         期望返回的结果类型
     * @param defaultValue 为空时默认对象 为空则取支持类中返回的默认对象
     * @param <O>          结果类型
     * @param <C>          bean类型
     * @return JoinDataMap
     */
    default <O, C> JoinDataMap<O> getMapByCode(Collection<C> list, Function<C, String> mapper, Class<O> type, O defaultValue) {

        List<String> codes = this.getCodeList(list, mapper);

        return this.getMapByCode(codes, type, defaultValue);
    }

    /**
     * 根据传入的 参数获得对应的 map
     *
     * @param list         bean
     * @param mapperArray  bean 获取编码映射字段
     * @param type         期望返回的结果类型
     * @param defaultValue 为空时默认对象 为空则取支持类中返回的默认对象
     * @param <O>          结果类型
     * @param <C>          bean类型
     * @return JoinDataMap
     */
    default <O, C> JoinDataMap<O> getMapByCode(Collection<C> list, List<Function<C, String>> mapperArray, Class<O> type, O defaultValue) {

        Collection<String> codes = this.joinCodeList(list, mapperArray);

        return this.getMapByCode(codes, type, defaultValue);
    }

    /**
     * @param list        bean
     * @param mapperArray bean 获取编码映射字段
     * @param <C>         bean类型
     * @return code List
     */
    default <C> Collection<String> joinCodeList(Collection<C> list, List<Function<C, String>> mapperArray) {

        Collection<String> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(mapperArray)) return result;

        mapperArray.stream().map(e -> this.getCodeList(list, e)).filter(CollectionUtils::isNotEmpty).forEach(result::addAll);

        return result.stream().distinct().collect(Collectors.toList());
    }
}
