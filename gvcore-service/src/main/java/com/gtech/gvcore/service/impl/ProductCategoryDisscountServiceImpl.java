package com.gtech.gvcore.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.ProductCategoryDisscountMapper;
import com.gtech.gvcore.dao.model.ProductCategoryDisscount;
import com.gtech.gvcore.service.ProductCategoryDisscountService;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Service
public class ProductCategoryDisscountServiceImpl implements ProductCategoryDisscountService {

    @Autowired
    private ProductCategoryDisscountMapper productCategoryDisscountMapper;

    @Override
    public ProductCategoryDisscount queryByProductCategoryCode(String productCategoryCode) {

        if (StringUtils.isBlank(productCategoryCode)) {
            return null;
        }

        ProductCategoryDisscount productCategoryDisscount = new ProductCategoryDisscount();
        productCategoryDisscount.setProductCategoryCode(productCategoryCode);
        return productCategoryDisscountMapper.selectOne(productCategoryDisscount);
    }

    @Transactional
    @Override
    public int insertOrUpdate(ProductCategoryDisscount productCategoryDisscount) {

        if (productCategoryDisscount.getId() != null) {
            return productCategoryDisscountMapper.updateByPrimaryKey(productCategoryDisscount);
        } else {
            productCategoryDisscount.setProductCategoryDisscountCode(UUIDUtils.generateCode());
            return productCategoryDisscountMapper.insertSelective(productCategoryDisscount);
        }
    }

}
