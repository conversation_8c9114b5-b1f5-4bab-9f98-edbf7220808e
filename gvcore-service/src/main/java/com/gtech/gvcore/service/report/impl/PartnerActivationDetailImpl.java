package com.gtech.gvcore.service.report.impl;

import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.PartnerActivationDetailBean;
import com.gtech.gvcore.service.report.impl.bo.PartnerDetailBo;
import com.gtech.gvcore.service.report.impl.param.PartnerDetailQueryData;
import com.gtech.gvcore.service.report.impl.support.PartnerDetailBaseImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName PartnerActivationDetailImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/10 15:27
 * @Version V1.0
 **/
@Service
public class PartnerActivationDetailImpl extends PartnerDetailBaseImpl<PartnerActivationDetailBean>
        implements BusinessReport<PartnerDetailQueryData, PartnerActivationDetailBean>, SingleReport {

    @Autowired private IssueHandlingDetailsService issueHandlingDetailsService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.PARTNER_ACTIVATION_REPORT;
    }

    @Override
    public List<PartnerActivationDetailBean> getExportData(PartnerDetailQueryData param) {

        List<PartnerDetailBo> boList = super.filterDate(super.getBoList(param), ReportVoucherStatusEnum.VOUCHER_ACTIVATED);
        if (CollectionUtils.isEmpty(boList)) return Collections.emptyList();

        //merchant
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, PartnerDetailBo::getMerchantCode, Merchant.class);
        //outlet
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, PartnerDetailBo::getOutletCode, Outlet.class);
        //pos
        final JoinDataMap<Pos> posMap = super.getMapByCode(boList, PartnerDetailBo::getPosCode, Pos.class);
        //cpg
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, PartnerDetailBo::getCpgCode, Cpg.class);
        //cpg type
        final JoinDataMap<CpgType> cpgTypeMap = super.getMapByCode(cpgMap.values(), Cpg::getCpgTypeCode, CpgType.class);
        //cpg type
        final JoinDataMap<Issuer> issuerMap = super.getMapByCode(boList, PartnerDetailBo::getIssuerCode, Issuer.class);
        //district
        final JoinDataMap<MasterDataDistrictEntity> districtMap = super.getMapByCode(outletMap.values(), Outlet::getDistrictCode, MasterDataDistrictEntity.class);
        //customer order
        final JoinDataMap<Customer> customerMap = super.getMapByCode(boList, PartnerDetailBo::getCustomerCode, Customer.class);
        // company
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);
        //原始卡券编号 map
        final Map<String, String> souceVoucherCodeMap = this.issueHandlingDetailsService.queryReIssuerSourceVoucherCode(
                super.getCodeList(boList.stream().map(PartnerDetailBo::getVoucher).collect(Collectors.toList()), Voucher::getVoucherCode));

        return boList.stream()
                .map(e -> {

                    final Voucher voucher = e.getVoucher();
                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Pos pos = posMap.findValue(e.getPosCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final CpgType cpgType = cpgTypeMap.findValue(cpg.getCpgTypeCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());
                    final Customer customer = customerMap.findValue(e.getCustomerCode());
                    final MasterDataDistrictEntity district = districtMap.findValue(outlet.getDistrictCode());
                    final Issuer issuer = issuerMap.findValue(e.getIssuerCode());

                    final String posName = null != pos && StringUtil.isNotEmpty(pos.getPosName()) ? pos.getPosName() : "GV POS";
                    final String transactionDate = DateUtil.format(e.getTransactionDate(), GvDateUtil.FORMAT_DD_MM_YYYY_JOIN_SLASH);
                    final String transactionTime = DateUtil.format(e.getTransactionDate(), GvDateUtil.FORMAT_HH_MM_SS_JOIN_POINT);
                    final String denomination = super.toAmount(voucher.getDenomination());

                    return new PartnerActivationDetailBean()
                            .setVoucherNumber(e.getVoucherCode())
                            .setBookletNumber(GvcoreConstants.MOP_CODE_VCE.equals(voucher.getMopCode()) ? "NA" : voucher.getBookletCode())
                            .setSbuCompanyName(company.getCompanyName())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setOutletType(outlet.getOutletType())
                            .setRegion(district.getDistrictName())
                            .setIssuer(issuer.getIssuerName())
                            .setTransactionDate(transactionDate)
                            .setTransactionTime(transactionTime)
                            .setInvoiceDate(transactionDate)
                            .setInvoiceTime(transactionTime)
                            .setPosName(posName)
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setVoucherProgramGroupType(cpgType.getCpgTypeName())
                            .setAmount(denomination)
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setDenomination(denomination)
                            .setExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), GvDateUtil.FORMAT_DD_MM_YYYY_JOIN_SLASH))
                            .setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setReferenceNumber(e.getReferenceNumber())
                            .setOriginalCardNumberBeforeReissue(souceVoucherCodeMap.get(e.getVoucherCode()))
                            .setBatchNumber(e.getInvoiceNumber())
                            .setApprovalCode(e.getApproveCode())
                            .autoFull(customer, PartnerActivationDetailBean.class)
                            ;
                })
                .collect(Collectors.toList());
    }
}
