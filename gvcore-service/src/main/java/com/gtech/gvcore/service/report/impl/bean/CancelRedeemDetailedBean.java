package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 18:32
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CancelRedeemDetailedBean {


    /**
     * gv_transaction_data 表中 voucher_code_num
     */
    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    /**
     * gv_transaction_data 表中 merchant_code 对应的 Merchant Name
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * gv_transaction_data 表中 outlet_code 对应的 Outlet Name
     */
    @ExcelProperty(value = "Merchant Out")
    private String outlet;

    /**
     * gv_transaction_data 表中 cpg_code 对应的 Cpg(Vpg) Name
     */
    @ExcelProperty(value = "VPG")
    private String voucherProgramGroup;

    /**
     * gv_transaction_data 表中 voucher_code 对应 voucher 的 voucher_effective_date
     */
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    /**
     * gv_transaction_data 中该记录的 面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Voucher Amount", converter = ExportExcelNumberConverter.class)
    private String transactionAmount;

    /**
     * gv_transaction_data 表中该记录的 transaction_date
     */
    @ExcelProperty(value = "Cancellation Redeem Date")
    private String transactionDate;

    /**
     * gv_transaction_data 表中该记录的 invoice_number
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * merchant_code 对应的 merchant 的 company_code 对应的 Company 的 Company Name
     */
    @ExcelProperty(value = "SBU Company Name")
    private String corporateName;
}
