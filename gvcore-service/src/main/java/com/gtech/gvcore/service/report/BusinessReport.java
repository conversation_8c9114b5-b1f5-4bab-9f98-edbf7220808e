package com.gtech.gvcore.service.report;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.report.export.file.ReportExcelSupport;
import com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelSupport;

import java.util.List;

/**
 * @ClassName BusinessReport
 * @Description 业务报表接口
 * <AUTHOR>
 * @Date 2022/10/20 19:07
 * @Version V1.0
 **/
public interface BusinessReport<P extends ReportQueryParam, R> extends BusinessReportConfig, ReportLabelSupport, ReportExcelSupport {

    /**
     * get export type
     * @return export type
     */
    ReportExportTypeEnum exportTypeEnum();

    /**
     * builder query param
     * @param reportParam report param
     * @return query param
     */
    P builderQueryParam(CreateReportRequest reportParam);

    /**
     * get export data
     * @param param query param
     * @return export data
     */
    List<R> getExportData(P param);

}
