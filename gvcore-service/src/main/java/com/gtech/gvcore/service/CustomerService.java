package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customer.CheckCustomerInfoRequest;
import com.gtech.gvcore.common.request.customer.CreateCustomerRequest;
import com.gtech.gvcore.common.request.customer.CreateIncompleteCustomerRequest;
import com.gtech.gvcore.common.request.customer.DeleteCustomerRequest;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.customer.GetEmailValidateCodeRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerByUserCodeRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerCompanyNameRequest;
import com.gtech.gvcore.common.request.customer.QueryCustomerRequest;
import com.gtech.gvcore.common.request.customer.UpdateCustomerRequest;
import com.gtech.gvcore.common.request.customer.UpdateCustomerStatusRequest;
import com.gtech.gvcore.common.request.customer.ValidateEmailRequest;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.CustomerOrder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface CustomerService {

    PageResult<String> queryCustomerCompanyName(QueryCustomerCompanyNameRequest request);

    Result<String> createCustomer(CreateCustomerRequest param);

    Result<Void> updateCustomer(UpdateCustomerRequest param);

    Result<Void> deleteCustomer(DeleteCustomerRequest param);

    PageResult<CustomerResponse> queryCustomerList(QueryCustomerRequest param);

    CustomerResponse getCustomer(GetCustomerRequest param);


    Customer getCustomerSummary(String customerCode);

    Map<String, Customer> getCustomerByCode(List<String> customerCode);


    Result<Void> updateCustomerStatus(UpdateCustomerStatusRequest param);

    /**
     * Verify the mailbox verification code
     * author Dragon
     *
     * @param validateEmailRequest email
     * @return true/false
     */
    Result<Boolean> validateEmail(ValidateEmailRequest validateEmailRequest);

     /**
     * Check whether the email exists and send the verification code
     *
     * @param email email
     * @return true/false
     */
    Result<Boolean> getEmailValidateCode(String email,String issuerCode);

    /**
     * Create incomplete Customer information
     *
     * @param createIncompleteCustomerRequest param
     * @return customer_code
     */
    Result<String> createIncompleteCustomer(CreateIncompleteCustomerRequest createIncompleteCustomerRequest);

    /**
     * @param checkCustomerInfo param
     * @return true/false
     */
    Result<Boolean> checkCustomerInfo(CheckCustomerInfoRequest checkCustomerInfo);

	List<Customer> queryCustomerByCodes(List<String> codeList);

    Result<List<CustomerResponse>> queryCustomerByEmail(GetEmailValidateCodeRequest email);

    Result<List<CustomerResponse>> queryCustomerListByUserCode(QueryCustomerByUserCodeRequest param);


}
