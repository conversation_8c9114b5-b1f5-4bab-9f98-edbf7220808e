package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class DeactivatedForDetailBean {

    /**
     * Voucher Number
     */
    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    /**
     * Merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Merchant Out
     */
    @ExcelProperty(value = "Outlet")
    private String merchantOut;

    /**
     * VPG
     */
    @ExcelProperty(value = "Voucher Program Group")
    private String vpg;

    /**
     * Expiry Date
     */
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    /**
     * Voucher Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Transaction Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    /**
     * Created on
     */
    @ExcelProperty(value = "Transaction Date")
    private String createdOn;

    /**
     * Invoice Number
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * Company Name
     */
    @ExcelProperty(value = "Client Name")
    private String companyName;

    /**
     * Deactivate Reason
     */
    @ExcelProperty(value = "Deactivate Reason")
    private String deactivateReason;
}
