package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 13:28
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcRedemptionDetailedBean {

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;
    @ExcelProperty(value = "Card Number")
    private String cardNumber;
    @ExcelProperty(value = "Merchant Name")
    private String merchant;
    @ExcelProperty(value = "Outlet Name")
    private String outlet;
    @ExcelProperty(value = "Gift Card Program Group")
    private String voucherProgramGroup;
    @ExcelProperty(value = "Transaction Type")
    private String transactionType;
    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;
    @ExcelProperty(value = "Issuance Date")
    private String issuanceYear;
    @ReportAmountValue
    @ExcelProperty(value = "Redeem Amount", converter = ExportExcelNumberConverter.class)
    private String redeemAmount;
    @ReportAmountValue
    @ExcelProperty(value = "Remaining Balance", converter = ExportExcelNumberConverter.class)
    private String redeemBalance;
    @ExcelProperty(value = "Balance Expiry")
    private String expiryDate;
    @ExcelProperty(value = "Approval Code")
    private String approvalCode;
    @ExcelProperty(value = "SBU Company Name")
    private String sbuCompanyName;
}
