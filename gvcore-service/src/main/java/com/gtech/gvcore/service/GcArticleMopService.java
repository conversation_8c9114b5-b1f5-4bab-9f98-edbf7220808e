package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.articlemop.*;
import com.gtech.gvcore.common.response.articlemop.CreateArticleMopResponse;
import com.gtech.gvcore.common.response.articlemop.QueryArticleMopsByPageResponse;
import com.gtech.gvcore.dao.model.GcArticleMop;

import java.util.List;
import java.util.Map;

/**
 * GC商品支付方式关联服务接口
 */
public interface GcArticleMopService {
    /**
     * 创建商品支付方式关联
     *
     * @param request 创建请求
     * @return 创建结果
     */
    Result<String> createArticleMop(CreateArticleMopRequest request);

    /**
     * 更新商品支付方式关联
     *
     * @param request 更新请求
     * @return 更新结果
     */
    Result<Void> updateArticleMop(UpdateArticleMopRequest request);

    /**
     * 更新商品支付方式关联状态
     *
     * @param request 更新状态请求
     * @return 更新结果
     */
    Result<Void> updateArticleMopStatus(UpdateArticleMopStatusRequest request);

    /**
     * 分页查询商品支付方式关联
     *
     * @param request 查询请求
     * @return 查询结果
     */
    PageResult<QueryArticleMopsByPageResponse> queryArticleMopsByPage(QueryArticleMopsByPageRequest request);

    /**
     * 获取商品支付方式关联
     *
     * @param articleMopCode 商品支付方式关联编码
     * @return 商品支付方式关联信息
     */
    QueryArticleMopsByPageResponse getArticleMop(String articleMopCode);

    /**
     * 根据商品支付方式关联编码查询
     *
     * @param articleMopCode 商品支付方式关联编码
     * @return 商品支付方式关联信息
     */
    GcArticleMop queryByArticleMopCode(String articleMopCode);
    
    /**
     * 根据商品支付方式关联编码列表查询
     *
     * @param articleMopCodeList 商品支付方式关联编码列表
     * @return 商品支付方式关联信息Map
     */
    Map<String, GcArticleMop> queryByArticleMopCodeList(List<String> articleMopCodeList);
} 