package com.gtech.gvcore.service;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IssueHandlingDetailsService {
	
	/**
	 * 
	 * @param detailList
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	int insertList(List<IssueHandlingDetails> detailList);
	
	/**
	 * 
	 * @param issueHandlingCode
	 * @param processStatus
	 * @param pageSize
	 * @param startId
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	List<IssueHandlingDetails> queryByIssueHandlingCode(String issueHandlingCode, Integer processStatus,
			Integer pageSize, Long startId);

	PageResult<IssueHandlingDetails> queryByIssueHandlingCodeByCode(String issueHandlingCode, PageParam page);
	
	/**
	 * 
	 * @param details
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	int updateByProcess(List<IssueHandlingDetails> details, String updateUser);
	
	/**
	 * 
	 * @param issueHandlingCode
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月16日
	 */
	int deleteByIssueHandlingCode(String issueHandlingCode);

    /**
     * 
     * <AUTHOR>
     * @param issueHandlingCode
     * @param processStatus
     * @return
     * @date 2022年6月14日
     */
    int countByIssueHandlingCode(String issueHandlingCode, Integer processStatus);

	Map<String, String> queryRemarkByVoucherCodeAndIssueType(List<String> codes, IssueHandlingTypeEnum issueType);

	/**
	 * 根据券号获取reIssuer 原始券号
	 * @param voucherCode 新卡券编号
	 * @return 新卡券编号 - 原始卡券编号
	 */
    Map<String, String> queryReIssuerSourceVoucherCode(List<String> voucherCode);

	/**
	 * Request Date
	 * Start Voucher Number
	 * End Voucher Number
	 * Customer email
	 * Upload CSV file

	 */
	List<IssueHandlingDetails> exportIssueHandlingDetails(IssueHandlingTypeEnum issueType, String issueHandlingCode, Date startDate, Date endDate, Long startVoucherNumber, Long endVoucherNumber, String customerEmail, List<String> voucherCodes,Integer pageSize, Integer pageNum);
}
