package com.gtech.gvcore.service.report.base;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.FrequencyEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.RepeatEndEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.request.schedulerreport.CreateSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.DeleteSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.QuerySchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.UpdateSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.UpdateSchedulerReportsStatusRequest;
import com.gtech.gvcore.common.response.schedulerreport.QuerySchedulerReportResponse;
import com.gtech.gvcore.common.utils.ExceptionBuilder;
import com.gtech.gvcore.common.utils.SftpUtil;
import com.gtech.gvcore.dao.mapper.SchedulerReportMapper;
import com.gtech.gvcore.dao.model.SchedulerReport;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.RedisLockHelper;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.report.SchedulerReportService;
import com.gtech.gvcore.service.report.SchedulerReportTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @date 2022/08/07 18:04
 */
@Slf4j
@Service
@EnableScheduling
public class SchedulerReportServiceImpl implements SchedulerReportService {

    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private SchedulerReportMapper schedulerReportMapper;

    @Autowired
    private SchedulerReportHelper schedulerReportHelper;

    @Autowired
    private MessageComponent messageComponent;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public Result<String> createSchedulerReport(CreateSchedulerReportRequest createSchedulerReportRequest) {

        createSchedulerReportRequest.setSchedulerReportCode(codeHelper.generateSchedulerReportCode());

        SchedulerReport schedulerReport = BeanCopyUtils.jsonCopyBean(createSchedulerReportRequest, SchedulerReport.class);
        schedulerReportMapper.insertSelective(schedulerReport);

        return Result.ok(schedulerReport.getSchedulerReportCode());
    }

    @Override
    public Result<Void> updateSchedulerReport(UpdateSchedulerReportRequest updateSchedulerReportRequest) {

        SchedulerReport schedulerReport = schedulerReportMapper.selectOne(SchedulerReport.builder().
                schedulerReportCode(updateSchedulerReportRequest.getSchedulerReportCode()).
                build());

        SchedulerReport updateBean = BeanCopyUtils.jsonCopyBean(updateSchedulerReportRequest, SchedulerReport.class);
        updateBean.setId(schedulerReport.getId());

        schedulerReportMapper.updateByPrimaryKeySelective(updateBean);
        return Result.ok();
    }

    @Override
    public PageResult<QuerySchedulerReportResponse> querySchedulerReport(QuerySchedulerReportRequest querySchedulerReportRequest) {

        try (Page<Object> ignored = PageMethod.startPage(querySchedulerReportRequest.getPageNum(), querySchedulerReportRequest.getPageSize())){

            List<QuerySchedulerReportResponse> querySchedulerReportResponses = schedulerReportMapper.queryList(querySchedulerReportRequest);

            PageInfo<QuerySchedulerReportResponse> of = PageInfo.of(querySchedulerReportResponses);

            return new PageResult<>(of.getList(), of.getTotal());

        }

    }

    @Override
    @Transactional
    public Result<Void> updateSchedulerReportStatus(UpdateSchedulerReportsStatusRequest updateStatusSchedulerReportRequest) {

        final String[] schedulerReportCodeArray = updateStatusSchedulerReportRequest.getSchedulerReportCodeList();
        if (ArrayUtils.isEmpty(schedulerReportCodeArray)) return Result.ok();
        final boolean status = ConvertUtils.toBoolean(updateStatusSchedulerReportRequest.getStatus(), false);

        final List<String> schedulerReportCodeList = Arrays.stream(schedulerReportCodeArray)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        //如果为禁用逻辑 直接全局更新
        if (!status) {
            int update = schedulerReportMapper.updateSchedulerReportStatus(schedulerReportCodeList, false);
            if (update != schedulerReportCodeList.size()) throw ExceptionBuilder.buildGTechBaseException(ResultErrorCodeEnum.SCHEDULER_REPORT_NOT_EXIST);
            return Result.ok();
        }

        //如果为启用则需要轮询其是否满足 任务复活逻辑
        schedulerReportCodeList.forEach(e -> {

            SchedulerReport schedulerReport = this.schedulerReportMapper.selectOne(SchedulerReport.builder().schedulerReportCode(e).build());

            if (null == schedulerReport) return;

            boolean enableSchedulerReportValidate = enableSchedulerReportValidate(schedulerReport);

            this.schedulerReportMapper.updateByPrimaryKeySelective(SchedulerReport.builder()
                            .id(schedulerReport.getId())
                            .status(true)
                            .executeFlag(enableSchedulerReportValidate ? 1 : 0)
                    .build());

        });

        return Result.ok();
    }

    @Override
    public Result<Void> deleteSchedulerReport(DeleteSchedulerReportRequest deleteSchedulerReportRequest) {

        schedulerReportMapper.delete(SchedulerReport.builder()
                .schedulerReportCode(deleteSchedulerReportRequest.getSchedulerReportCode()).build());
        return Result.ok();
    }


    /**
     *
     * 启用定时报表检查
     *
     * @param schedulerReport
     * @return
     */
    private boolean enableSchedulerReportValidate (SchedulerReport schedulerReport) {

        final FrequencyEnum frequencyEnum = FrequencyEnum.valueOfCode(schedulerReport.getFrequency());
        final RepeatEndEnum repeatEndEnum = RepeatEndEnum.valueOfCode(schedulerReport.getRepeatEnd());

        //只进行检查不进行数据修正
        boolean correction = false;

        //ONCE
        if (FrequencyEnum.ONCE == frequencyEnum) return schedulerReportHelper.executeValidateOnce(schedulerReport, correction);
        //AFTER
        else if (RepeatEndEnum.AFTER == repeatEndEnum) return schedulerReportHelper.executeValidateAfter(schedulerReport, correction);
        //ON_DATE
        else if (RepeatEndEnum.ON_DATE == repeatEndEnum) return schedulerReportHelper.executeValidateOnDate(schedulerReport, correction);
        // NEVER
        else return true;

    }











}
