package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.export.file.ReportExcelUtils;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.aging.AgeBySbuDetailsTwoBean;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.AgeBySbuDetailsTwoBo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AgeBySbuDetailsTwoBuilder
 * @Description Age by SBU Details_2
 * <AUTHOR>
 * @Date 2022/11/4 14:43
 * @Version V1.0
 **/
@Component
public class AgeBySbuDetailsTwoBuilder implements AgingSheetBuilder {

    private final AddFunction addFunction = getAddFunction();

    public static final String TOTAL_VALUE = " Total";
    public static final String GRAND_TOTAL_VALUE = "Grand Total";

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_AGE_BY_SBU_DETAILS_TWO_REPORT;
    }

    @Override
    public AgingSheet builder(final ReportContext context) {

        final List<AgeBySbuDetailsTwoBean> beans = new ArrayList<>();
        final AgeBySbuDetailsTwoBo allTotalBo = new AgeBySbuDetailsTwoBo().setCustomerName(GRAND_TOTAL_VALUE);

        //group by customer
        final Map<String, List<AgeBySbuDetailsTwoBo>> resultCustomerMap = this.group(context)
                .stream().collect(Collectors.groupingBy(AgeBySbuDetailsTwoBo::getCustomerCode));

        resultCustomerMap.forEach((k, v) -> {

            final String customerName = v.get(0).getCustomerName();
            final AgeBySbuDetailsTwoBo customerTotal = new AgeBySbuDetailsTwoBo().setCustomerName(customerName + TOTAL_VALUE);
            final List<AgeBySbuDetailsTwoBean> customerBeans = new ArrayList<>();
            final Map<String, List<AgeBySbuDetailsTwoBo>> resultSubMap = v.stream().collect(Collectors.groupingBy(AgeBySbuDetailsTwoBo::getSbuName));

            resultSubMap.forEach((sbuName, sv) -> {

                final AgeBySbuDetailsTwoBo sbuTotal = new AgeBySbuDetailsTwoBo().setSbuName(sbuName + TOTAL_VALUE);
                final List<AgeBySbuDetailsTwoBean> sbuBeans = new ArrayList<>();

                sv.forEach(i -> {
                    final AgeBySbuDetailsTwoBo bo = i.setCustomerName(null).setSbuName(null);

                    sbuTotal.add(bo);
                    customerTotal.add(bo);
                    allTotalBo.add(bo);

                    sbuBeans.add(bo.bean());
                });

                sbuBeans.add(sbuTotal.bean());
                sbuBeans.get(0).setSbuName(sbuName);

                customerBeans.addAll(sbuBeans);
            });

            customerBeans.add(customerTotal.bean());
            customerBeans.get(0).setCustomerName(customerName);

            beans.addAll(customerBeans);
        });

        beans.add(allTotalBo.bean());


        return new AgingSheet()
                .setHead(null)
                .setCellWriteHandler(new CellWriteHandlerImpl())
                .addSheetData("asd2", exportTypeEnum(), beans)
                .setSheetName(exportTypeEnum().getSheetName());
    }

    private Collection<AgeBySbuDetailsTwoBo> group(final ReportContext context) {

        final Map<String, AgeBySbuDetailsTwoBo> result = new HashMap<>();

        //sales data
        final List<AgingBo> list = context.getCacheList(AgingSheetBuilder.SALES_DATA_KEY);

        final JoinDataMap<Customer> customerMap = context.getCacheJoinMap(AgingSheetBuilder.CUSTOMER_MAP_KEY);
        final JoinDataMap<Voucher> voucherMap = context.getCacheJoinMap(AgingSheetBuilder.VOUCHER_MAP_KEY);
        final List<AgingVoucherTransactionBoVoucher> transactionDataList = context.getCacheList(AgingSheetBuilder.REDEEM_DATA_KEY);
        final JoinDataMap<Merchant> merchantMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_MERCHANT_MAP_KEY);
        final Map<String, String> sbuMap = getSbuMap(context, merchantMap);
        final Map<String, AgingVoucherTransactionBoVoucher> useTransactionMap = transactionDataList.stream().collect(Collectors.toMap(AgingVoucherTransactionBoVoucher::getVoucherCode, Function.identity() , (a, b) -> b));

        list.stream()
                .filter(e -> useTransactionMap.containsKey(e.getVoucherCode()))
                .filter(e -> customerMap.containsKey(e.getCustomerCode()))
                .filter(e -> voucherMap.containsKey(e.getVoucherCode()))
                .forEach(e -> {

                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final AgingVoucherTransactionBoVoucher useTransactionData = useTransactionMap.get(e.getVoucherCode());
                    final Merchant merchant = merchantMap.findValue(useTransactionData.getMerchantCode());
                    //后续需要隐藏从此处入手修改
                    String customerCode = e.getCustomerCode();
                    String subName = sbuMap.get(useTransactionData.getMerchantCode());
                    String key = customerCode + subName + merchant.getMerchantCode();

                    AgeBySbuDetailsTwoBo bo = result.computeIfAbsent(key, k -> new AgeBySbuDetailsTwoBo()
                            .setCustomerCode(customerCode)
                            .setSbuName(subName)
                            .setMerchantName(merchant.getMerchantName())
                            .setCustomerName(customerMap.get(customerCode).getCustomerName()));

                    this.addFunction.add(voucher.getCreateTime(), useTransactionData.getTransactionDate(), bo, voucher.getDenomination());
                });

        return result.values();
    }


    private Map<String, String> getSbuMap(final ReportContext context, final Map<String, Merchant> merchantMap) {

        final JoinDataMap<Company> companyMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_COMPANY_MAP_KEY);

        Map<String, String> sbuMap = new HashMap<>();
        merchantMap.forEach((k, v) -> sbuMap.put(k, companyMap.get(v.getCompanyCode()).getSbu()));
        return sbuMap;
    }

    private AddFunction getAddFunction () {

        final BiConsumer<AgeBySbuDetailsTwoBo, BigDecimal> addM30Function    = AgeBySbuDetailsTwoBo::addOneValue;
        final BiConsumer<AgeBySbuDetailsTwoBo, BigDecimal> addM60Function    = AgeBySbuDetailsTwoBo::addTwoValue;
        final BiConsumer<AgeBySbuDetailsTwoBo, BigDecimal> addM90Function    = AgeBySbuDetailsTwoBo::addThreeValue;
        final BiConsumer<AgeBySbuDetailsTwoBo, BigDecimal> addM180Function   = AgeBySbuDetailsTwoBo::addFourValue;
        final BiConsumer<AgeBySbuDetailsTwoBo, BigDecimal> addM360Function   = AgeBySbuDetailsTwoBo::addSixValue;

        //add function
        return (saleTime, useTime, executeObject, value) ->  {

            long time = useTime.getTime() - saleTime.getTime();
            int day   = (int) (time / (24 * 60 * 60 * 1000));

            if      (day <= AddFunction.FunctionMethod.M30.getDay())  addM30Function  .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M60.getDay())  addM60Function  .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M90.getDay())  addM90Function  .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M180.getDay()) addM180Function .accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M360.getDay()) addM360Function .accept(executeObject, value);
        };
    }

    public interface AddFunction {

        enum FunctionMethod {
            M30     (30),
            M60     (60),
            M90     (90),
            M180    (180),
            M360    (360),
            ;
            private final int day;
            FunctionMethod(int day) {
                this.day = day;
            }

            public int getDay() {
                return day;
            }
        }

        void add (Date saleTime, Date useTime, AgeBySbuDetailsTwoBo executeObject, BigDecimal value);
    }

    @Override
    public Class<?> getExportDataClass() {
        return AgeBySbuDetailsTwoBean.class;
    }

    public static class CellWriteHandlerImpl implements CellWriteHandler {

        private static final int AMOUNT_CELL_INDEX = 3;
        private static final int ROW_INDEX_MAX = 9;
        // style map
        private final EnumMap<Style, CellStyle> styleMap = new EnumMap<>(Style.class);
        // style sheet name
        private static final String STYLE_SHEET_NAME = "AGE_BY_SBU_DETAIL_2_STYLE";

        enum Style {
            CUSTOMER,
            SBU,
            MERCHANT,
            DEFAULT_AMOUNT,
            SBU_TOTAL,
            SBU_TOTAL_AMOUNT,
            CUSTOMER_TOTAL,
            CUSTOMER_TOTAL_AMOUNT,
            GRAND_TOTAL,
            GRAND_TOTAL_AMOUNT
        }


        public Style getStyle(Style style, int cellIndex) {

            if (Style.CUSTOMER == style || Style.SBU == style || Style.MERCHANT == style) return cellIndex < AMOUNT_CELL_INDEX ? style : Style.DEFAULT_AMOUNT;
            else if (Style.SBU_TOTAL == style) return cellIndex < AMOUNT_CELL_INDEX ? style : Style.SBU_TOTAL_AMOUNT;
            else if (Style.CUSTOMER_TOTAL == style) return cellIndex < AMOUNT_CELL_INDEX ? style : Style.CUSTOMER_TOTAL_AMOUNT;
            else if (Style.GRAND_TOTAL == style) return cellIndex < AMOUNT_CELL_INDEX ? style : Style.GRAND_TOTAL_AMOUNT;
            else return style;
        }

        public CellStyle getCellStyle (Style style, int cellIndex) {

            return styleMap.get(getStyle(style, cellIndex));
        }

        private void initStyle(SXSSFWorkbook workbook) {

            if (!styleMap.isEmpty()) return;

            //load style
            Sheet sheet = workbook.getXSSFWorkbook().getSheet(STYLE_SHEET_NAME);
            styleMap.put(Style.CUSTOMER, ReportExcelUtils.getCell(sheet, 0, 0).getCellStyle());
            styleMap.put(Style.SBU, ReportExcelUtils.getCell(sheet, 1, 0).getCellStyle());
            styleMap.put(Style.MERCHANT, ReportExcelUtils.getCell(sheet, 2, 0).getCellStyle());
            styleMap.put(Style.DEFAULT_AMOUNT, ReportExcelUtils.getCell(sheet, 3, 0).getCellStyle());
            styleMap.put(Style.SBU_TOTAL, ReportExcelUtils.getCell(sheet, 4, 0).getCellStyle());
            styleMap.put(Style.SBU_TOTAL_AMOUNT, ReportExcelUtils.getCell(sheet, 5, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_TOTAL, ReportExcelUtils.getCell(sheet, 6, 0).getCellStyle());
            styleMap.put(Style.CUSTOMER_TOTAL_AMOUNT, ReportExcelUtils.getCell(sheet, 7, 0).getCellStyle());
            styleMap.put(Style.GRAND_TOTAL, ReportExcelUtils.getCell(sheet, 8, 0).getCellStyle());
            styleMap.put(Style.GRAND_TOTAL_AMOUNT, ReportExcelUtils.getCell(sheet, 9, 0).getCellStyle());
        }

        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {

            Cell cell = context.getCell();
            int columnIndex = cell.getColumnIndex();
            if (columnIndex > 1) return;


            final Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
            final Row row = context.getRow();
            final String value = cell.getStringCellValue();

            this.initStyle((SXSSFWorkbook) workbook);

            if (columnIndex == 0) cellStyle(row, value);
            else cellTotalStyle(cell, row);



        }

        private void cellTotalStyle(Cell cell, Row row) {
            if (StringUtils.contains(cell.getStringCellValue(), TOTAL_VALUE)) {

                for (int i = 1; i < ROW_INDEX_MAX; i++) CellUtil.getCell(row, i).setCellStyle(getCellStyle(Style.SBU_TOTAL, i));

            }
        }

        private void cellStyle(Row row, String value) {

            if (StringUtils.equals(value, GRAND_TOTAL_VALUE)) {

                for (int i = 0; i < ROW_INDEX_MAX; i++) CellUtil.getCell(row, i).setCellStyle(getCellStyle(Style.GRAND_TOTAL, i));

            } else if (StringUtils.contains(value, TOTAL_VALUE)) {

                for (int i = 0; i < ROW_INDEX_MAX; i++) CellUtil.getCell(row, i).setCellStyle(getCellStyle(Style.CUSTOMER_TOTAL, i));

            } else {

                for (int i = 0; i < ROW_INDEX_MAX; i++) {

                    Style thisStyle;
                    if (0 == i) thisStyle = Style.CUSTOMER;
                    else if (1 == i) thisStyle = Style.SBU;
                    else thisStyle = Style.MERCHANT;

                    CellUtil.getCell(row, i).setCellStyle(getCellStyle(thisStyle, i));

                }
            }
        }

    }


}
