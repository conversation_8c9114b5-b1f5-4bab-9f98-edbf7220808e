package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月26日
 */
@Getter
@Setter
@Accessors(chain = true)
public class GoodsInTransitSummaryBo {

    private String requestId;

    private String outbound;

    private String inbound;

    private String cpgCode;

    private Integer voucherCount;

    private BigDecimal amount;

    private Date allLocationTime;

    private Date inboundTime;

}


