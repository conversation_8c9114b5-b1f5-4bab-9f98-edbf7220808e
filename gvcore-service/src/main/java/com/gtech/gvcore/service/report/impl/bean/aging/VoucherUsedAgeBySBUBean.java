package com.gtech.gvcore.service.report.impl.bean.aging;

import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherUsedAgeBySBUBo
 * @Description voucher used age by sbu bo
 * <AUTHOR>
 * @Date 2022/11/3 15:49
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherUsedAgeBySBUBean {

    @ReportLabel(value = {"Sum of Amount", "Row Labels"})
    private String sbuName;

    @ReportAmountValue
    @ReportLabel(value = {"Column Labels", "30 Days"})
    private String oneValue;

    @ReportAmountValue
    @ReportLabel(value = {"Column Labels", "60 Days"})
    private String twoValue;

    @ReportAmountValue
    @ReportLabel(value = {"Column Labels", "90 Days"})
    private String threeValue;

    @ReportAmountValue
    @ReportLabel(value = {"Column Labels", "180 Days"})
    private String fourValue;

    @ReportAmountValue
    @ReportLabel(value = {"Column Labels", "360 Days"})
    private String sixValue;

    @ReportAmountValue
    @ReportLabel(value = {"Column Labels", "Grand Total"})
    private String total;

}
