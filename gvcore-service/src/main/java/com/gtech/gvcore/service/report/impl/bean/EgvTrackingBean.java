package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 10:56
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class EgvTrackingBean {

    @ReportLabel(value = "No")
    @ExcelProperty(index = 0)
    private Integer no;

    @ReportLabel(value = "Type of e-GV")
    @ExcelProperty(index = 1)
    private String typeOfeGV;

    @ReportLabel(value = "Channel")
    @ExcelProperty(index = 2)
    private String channel;

    @ReportAmountValue
    @ReportLabel({"Sales", "WTD"})
    @ExcelProperty(index = 3, converter = ExportExcelNumberConverter.class)
    private String salesWTD;

    @ReportAmountValue
    @ReportLabel({"Sales", "MTD"})
    @ExcelProperty(index = 4, converter = ExportExcelNumberConverter.class)
    private String salesMTD;

    @ReportAmountValue
    @ReportLabel({"Sales", "YTD"})
    @ExcelProperty(index = 5, converter = ExportExcelNumberConverter.class)
    private String salesYTD;

    @ReportAmountValue
    @ReportLabel({"Activation", "WTD"})
    @ExcelProperty(index = 6, converter = ExportExcelNumberConverter.class)
    private String activationWTD;

    @ReportAmountValue
    @ReportLabel({"Activation", "MTD"})
    @ExcelProperty(index = 7, converter = ExportExcelNumberConverter.class)
    private String activationMTD;

    @ReportAmountValue
    @ReportLabel({"Activation", "YTD"})
    @ExcelProperty(index = 8, converter = ExportExcelNumberConverter.class)
    private String activationYTD;

    @ReportAmountValue
    @ReportLabel({"Redemption", "WTD"})
    @ExcelProperty(index = 9, converter = ExportExcelNumberConverter.class)
    private String redemptionWTD;

    @ReportAmountValue
    @ReportLabel({"Redemption", "MTD"})
    @ExcelProperty(index = 10, converter = ExportExcelNumberConverter.class)
    private String redemptionMTD;

    @ReportAmountValue
    @ReportLabel({"Redemption", "YTD"})
    @ExcelProperty(index = 11, converter = ExportExcelNumberConverter.class)
    private String redemptionYTD;

    @ReportLabel(value = "% Activation to Sales YTD")
    @ExcelProperty(index = 12)
    private String percentageActivationToSalesYTD;

    @ReportLabel(value = "% Redemption to Sales YTD")
    @ExcelProperty(index = 13)
    private String percentageRedemptionToSalesYTD;

    public static EgvTrackingBean newInstance() {
        return new EgvTrackingBean()
                .setSalesWTD("0")
                .setSalesMTD("0")
                .setSalesYTD("0")
                .setActivationWTD("0")
                .setActivationMTD("0")
                .setActivationYTD("0")
                .setRedemptionWTD("0")
                .setRedemptionMTD("0")
                .setRedemptionYTD("0");
    }

    public EgvTrackingBean addSalesWTD(String number) {
        this.salesWTD = new BigDecimal(salesWTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addSalesMTD(String number) {
        this.salesMTD = new BigDecimal(salesMTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addSalesYTD(String number) {
        this.salesYTD = new BigDecimal(salesYTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addActivationWTD(String number) {
        this.activationWTD = new BigDecimal(activationWTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addActivationMTD(String number) {
        this.activationMTD = new BigDecimal(activationMTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addActivationYTD(String number) {
        this.activationYTD = new BigDecimal(activationYTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addRedemptionWTD(String number) {
        this.redemptionWTD = new BigDecimal(redemptionWTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addRedemptionMTD(String number) {
        this.redemptionMTD = new BigDecimal(redemptionMTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addRedemptionYTD(String number) {
        this.redemptionYTD = new BigDecimal(redemptionYTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addSalesWTD(BigDecimal number) {
        this.salesWTD = new BigDecimal(salesWTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addSalesMTD(BigDecimal number) {
        this.salesMTD = new BigDecimal(salesMTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addSalesYTD(BigDecimal number) {
        this.salesYTD = new BigDecimal(salesYTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addActivationWTD(BigDecimal number) {
        this.activationWTD = new BigDecimal(activationWTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addActivationMTD(BigDecimal number) {
        this.activationMTD = new BigDecimal(activationMTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addActivationYTD(BigDecimal number) {
        this.activationYTD = new BigDecimal(activationYTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addRedemptionWTD(BigDecimal number) {
        this.redemptionWTD = new BigDecimal(redemptionWTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addRedemptionMTD(BigDecimal number) {
        this.redemptionMTD = new BigDecimal(redemptionMTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }

    public EgvTrackingBean addRedemptionYTD(BigDecimal number) {
        this.redemptionYTD = new BigDecimal(redemptionYTD).add(ConvertUtils.toBigDecimal(number, BigDecimal.ZERO)).toPlainString();
        return this;
    }
}
