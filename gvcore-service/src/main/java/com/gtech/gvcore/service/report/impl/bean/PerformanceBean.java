package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName PerformanceBean
 * @Description Performance bean
 * <AUTHOR>
 * @Date 2022/12/29 15:15
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class PerformanceBean {

    @ExcelProperty(index = 0, value = "Issuer")
    private String issuer;

    @ExcelProperty(index = 1, value = "Merchant")
    private String merchant;

    @ExcelProperty(index = 2, value = "Outlet")
    private String outlet;

    @ExcelProperty(index = 3, value = "Outlet Code")
    private String outletCode;

    @ExcelProperty(index = 4, value = "Outlet Type")
    private String outletType;

    @ExcelProperty(index = 5, value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(index = 6, value = "Net Sales Count", converter = ExportExcelNumberConverter.class)
    private String netSalesCount;

    @ReportAmountValue
    @ExcelProperty(index = 7, value = "Net Sales Amount", converter = ExportExcelNumberConverter.class)
    private String netSalesAmount;

    @ExcelProperty(index = 8, value = "Net Redemption Count", converter = ExportExcelNumberConverter.class)
    private String netRedemptionCount;

    @ReportAmountValue
    @ExcelProperty(index = 9, value = "Net Redemption Amount", converter = ExportExcelNumberConverter.class)
    private String netRedemptionAmount;

}
