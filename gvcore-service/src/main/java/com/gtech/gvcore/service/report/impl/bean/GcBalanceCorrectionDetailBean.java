package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName BulkOrderDetailedBean
 * @Description BulkOrderDetailedBean
 * <AUTHOR>
 * @Date 2022/7/12 16:54
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcBalanceCorrectionDetailBean {

    @ExcelProperty(value = "Gift Card Number")
    private String cardNumber;
    @ExcelProperty(value = "Gift Card Program Group")
    private String cpgCode;
    @ExcelProperty(value = "Merchant")
    private String merchant;
    @ExcelProperty(value = "Outlet")
    private String outlet;
    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;
    @ReportAmountValue
    @ExcelProperty(value = "Initial Balance")
    private String initialBalance;
    @ReportAmountValue
    @ExcelProperty(value = "Correction Balance")
    private String correctionBalance;
    @ExcelProperty(value = "After Correction")
    private String afterCorrection;
    @ExcelProperty(value = "Transaction Type")
    private String transactionType;
    @ExcelProperty(value = "Customer name")
    private String customerName;
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;
    @ExcelProperty(value = "Notes")
    private String notes;
}
