package com.gtech.gvcore.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.ibatis.session.RowBounds;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageRowBounds;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.BookletStatusEnum;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.enums.GvPosInputTypesEnum;
import com.gtech.gvcore.common.enums.QCTransactionTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionChannelEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherEnableDisablePosEnum;
import com.gtech.gvcore.common.enums.VoucherStatusPosEnum;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.cancelredeem.CancelRedeemRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.transaction.BatchcloseRequest;
import com.gtech.gvcore.common.request.transaction.CardtransactionhistoryRequest;
import com.gtech.gvcore.common.request.transaction.CustomerInfo;
import com.gtech.gvcore.common.request.transaction.LineItem;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.request.voucherbatch.GetVoucherBatchRequest;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgTypeResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.common.response.transaction.BatchcloseResponse;
import com.gtech.gvcore.common.response.transaction.CardtransactionhistoryResponse;
import com.gtech.gvcore.common.response.transaction.LineItemResponseInfo;
import com.gtech.gvcore.common.response.transaction.SVRecentTransactionsResponse;
import com.gtech.gvcore.common.response.transaction.TransactionResponse;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dto.SalesDataDto;
import com.gtech.gvcore.dto.SalesDataResultDto;
import com.gtech.gvcore.dto.TransactionDashboardDataBase;
import com.gtech.gvcore.dto.TransactionDashboardDto;
import com.gtech.gvcore.external.JwtService;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CpgTypeService;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.PosService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherBatchService;
import com.gtech.gvcore.service.VoucherBookletService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class TransactionDataServiceImpl implements TransactionDataService {
    @Lazy
    @Autowired
    private TransactionDataMapper transactionDataMapper;
    @Lazy
    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;
    @Lazy
    @Autowired
    private PosService posService;


    @Autowired
    private CpgService cpgService;
    @Lazy
    @Autowired
    private CpgTypeService cpgTypeService;
    @Lazy
    @Autowired
    private OutletService outletService;

    @Lazy
    @Autowired
    private VoucherBatchService voucherBatchService;
    @Lazy
    @Autowired
    private CustomerOrderDetailsMapper customerOrderDetailsMapper;

    @Lazy
    @Autowired
    private CustomerOrderService customerOrderService;

    @Lazy
    @Autowired
    private CustomerOrderMapper customerOrderMapper;
    @Lazy
    @Autowired
    private VoucherBookletService voucherBookletService;

    @Lazy
    @Autowired
    private VoucherServiceImpl voucherService;
    public static final String API_CUSTOMER = "api customer";
    @Value("${gvcore.jwt.effective.hours:1440}")
    private Integer effectiveHours;

    @Autowired
    private RedisTemplate redisTemplate;


    @Autowired
    private JwtService jwtService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    @Qualifier("transactionExecutor")
    private ThreadPoolTaskExecutor transactionExecutor;

    @Autowired
    @Qualifier("asyncLogExecutor")
    private ThreadPoolTaskExecutor asyncLogExecutor;

    @Autowired
    @Qualifier("batchProcessExecutor")
    private ThreadPoolTaskExecutor batchProcessExecutor;

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;


    @Override
    public Result<Void> createTransactionData(CreateTransactionDataRequest request) {

        TransactionData transactionData = BeanCopyUtils.jsonCopyBean(request, TransactionData.class);
        if (StringUtil.isEmpty(transactionData.getTransactionCode())) {
            transactionData.setTransactionCode(gvCodeHelper.generateTransactionDataCode());

        }
        if (StringUtil.isEmpty(transactionData.getTransactionId())) {
            transactionData.setTransactionId(transactionData.getTransactionCode());

        }
//        if (StringUtil.isEmpty(request.getOtherInputParameter())) {
//            transactionData.setOtherInputParameter(new JsonObject().toString());
//
//        }
        if (StringUtil.isEmpty(request.getApproveCode())) {
            transactionData.setApproveCode(gvCodeHelper.generateApproveCode());

        }
        if (StringUtil.isEmpty(request.getReferenceNumber())) {
            transactionData.setReferenceNumber(gvCodeHelper.generateReferenceNumber());

        }
        if (StringUtil.isEmpty(request.getTransactionChannel())) {
            transactionData.setTransactionChannel(TransactionChannelEnum.SYSTEM.getCode());

        }

        /*if(StringUtil.isEmpty(request.getCardEntryMode())){
            request.setCardEntryMode("Swiped");
        }*/

        if (StringUtil.isEmpty(request.getTransactionMode())) {
            request.setTransactionMode("Transaction successful");
        }

        if (StringUtil.isEmpty(request.getResponseMessage())) {
            request.setResponseMessage("Transaction Successful.");
        }

        transactionDataMapper.insertSelective(transactionData);

        return Result.ok();
    }

    @Override
    public Result<Integer> createTransactionDataList(List<CreateTransactionDataRequest> request) {
        if (request == null || request.isEmpty()) {
            return Result.ok(0);
        }
        // 预分配容量，避免动态扩容
        List<TransactionData> transactionDatas = new ArrayList<>(request.size());
        transactionDatas = BeanCopyUtils.jsonCopyList(request, TransactionData.class);
        // 在遍历前检查集合是否为空
        if (transactionDatas != null && !transactionDatas.isEmpty()) {
            for (TransactionData vo : transactionDatas) {
                // 保持原有的字段设置逻辑
                if (StringUtil.isEmpty(vo.getTransactionCode())) {
                    vo.setTransactionCode(gvCodeHelper.generateTransactionDataCode());
                }
                if (StringUtil.isEmpty(vo.getTransactionId())) {
                    vo.setTransactionId(vo.getTransactionCode());
                }
                if (StringUtil.isEmpty(vo.getOtherInputParameter())) {
                    vo.setOtherInputParameter(new JsonObject().toString());
                }
                if (StringUtil.isEmpty(vo.getApproveCode())) {
                    vo.setApproveCode(gvCodeHelper.generateApproveCode());
                }
                if (StringUtil.isEmpty(vo.getReferenceNumber())) {
                    vo.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
                }
                if (StringUtil.isEmpty(vo.getTransactionChannel())) {
                    vo.setTransactionChannel(TransactionChannelEnum.SYSTEM.getCode());
                }
                /*if(StringUtil.isEmpty(vo.getCardEntryMode())){
                    vo.setCardEntryMode("Swiped");
                }*/
                if (StringUtil.isEmpty(vo.getTransactionMode())) {
                    vo.setTransactionMode("Transaction successful");
                }
                if (StringUtil.isEmpty(vo.getResponseMessage())) {
                    vo.setResponseMessage("Transaction Successful.");
                }
            }
        }
        int insertCount = 0;
        try {
            insertCount = transactionDataMapper.insertList(transactionDatas);
        } catch (Exception e) {
            log.error("Insert transaction data failed", e);
            // 不抛出异常，与原代码保持一致
        }
        return Result.ok(insertCount);
    }

    @Override
    public Map<String, String> queryVoucherInvoiceNo(List<String> voucherCodeList, TransactionTypeEnum transactionTypeEnum) {
        if (CollectionUtils.isEmpty(voucherCodeList)) {
            return Collections.emptyMap();
        }
        List<TransactionData> list = transactionDataMapper.queryVoucherInvoiceNo(voucherCodeList, null != transactionTypeEnum ? transactionTypeEnum.getCode() : null);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(TransactionData::getVoucherCode, TransactionData::getInvoiceNumber, (k1, k2) -> k1));
    }


    @Override
    public Map<String, String> queryVoucherApprovalCode(List<String> voucherCodeList, TransactionTypeEnum transactionTypeEnum) {
        if (CollectionUtils.isEmpty(voucherCodeList)) {
            return Collections.emptyMap();
        }
        List<TransactionData> list = transactionDataMapper.queryVoucherApprovalCode(voucherCodeList, transactionTypeEnum.getCode());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(TransactionData::getVoucherCode, TransactionData::getApproveCode, (k1, k2) -> k1));
    }

    @Override
    public Result<Void> updateTransactionDataResponseMessage(String transaction, String response, Integer successOfFail, TransactionResponse body) {

        TransactionData transactionData = new TransactionData();
        transactionData.setSuccessOrFailure(String.valueOf(successOfFail));
        transactionData.setResponseMessage(response);
        transactionData.setApproveCode(body.getApprovalCode());

        Example example = new Example(TransactionData.class);
        example.createCriteria().andEqualTo(TransactionData.C_TRANSACTION_ID, transaction);


        transactionDataMapper.updateByConditionSelective(transactionData, example);
        return Result.ok();
    }

    public List<TransactionData> queryTransactionDataByVoucherCode(String voucherCode) {

        if (StringUtils.isEmpty(voucherCode)) return Collections.emptyList();

        Example example = new Example(TransactionData.class);
        example.createCriteria().andEqualTo(TransactionData.C_VOUCHER_CODE, voucherCode)
                .orEqualTo(TransactionData.C_VOUCHER_CODE_NUM, voucherCode);

        return transactionDataMapper.selectByCondition(example);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertList(List<TransactionData> transactionDataList) {

        for (TransactionData transactionData : transactionDataList) {
            transactionData.setTransactionCode(gvCodeHelper.generateTransactionDataCode());
            if (StringUtil.isEmpty(transactionData.getApproveCode())) {
                transactionData.setApproveCode(gvCodeHelper.generateApproveCode());
            }
            if (StringUtil.isEmpty(transactionData.getTransactionId())) {
                transactionData.setTransactionId(gvCodeHelper.generateTransactionDataCode());
            }
            if (StringUtil.isEmpty(transactionData.getTransactionChannel())) {
                transactionData.setTransactionChannel(TransactionChannelEnum.SYSTEM.getCode());
            }

            /*if(StringUtil.isEmpty(transactionData.getCardEntryMode())){
                transactionData.setCardEntryMode("Swiped");
            }*/

            if (StringUtil.isEmpty(transactionData.getTransactionMode())) {
                transactionData.setTransactionMode("Transaction successful");
            }

            if (StringUtil.isEmpty(transactionData.getResponseMessage())) {
                transactionData.setResponseMessage("Transaction Successful.");
            }


        }
        int i = 0;
        try {
            i = transactionDataMapper.insertList(transactionDataList);
        } catch (Exception e) {
            log.error("插入交易记录失败", e);
        }
        log.info("已经成功插入的交易记录:{}", i);
        return i;
    }


    private void createLogLeaflet(TransactionRequest request, List<Voucher> vouchers, PosResponse pos, String approvalCode, String invoiceNumber, String transactionType, OutletResponse outletResponse, String success, String responseMessage, String batchId, Map<String, String> voucherSuccessStatus) {

		if (StringUtil.isEmpty(transactionType) || TransactionTypeEnum.GIFT_CARD_REDEEM_VALIDATE.getCode().equals(transactionType)
				|| TransactionTypeEnum.GIFT_CARD_ACTIVATE_VALIDATE.getCode().equals(transactionType)) {
			// 为空或者校验的不记流水
			log.info("createLogLeaflet typoe:{},request:{}", transactionType, JSON.toJSONString(request));
			return;
		}
		for (Voucher voucher : vouchers) {
            CreateTransactionDataRequest logRequest = new CreateTransactionDataRequest();


            logRequest.setTransactionId(String.valueOf(request.getTransactionId())+String.valueOf(batchId)+invoiceNumber);
            logRequest.setTransactionType(String.valueOf(transactionType));
            logRequest.setMerchantCode(outletResponse.getMerchantCode());
            logRequest.setOutletCode(pos.getOutletCode());
            logRequest.setIssuerCode(voucher.getIssuerCode());
            logRequest.setCpgCode(voucher.getCpgCode());
            logRequest.setMopCode(voucher.getMopCode());
            //.transactionDate(request.getDateAtClient())
            logRequest.setTransactionDate(new Date());
            //根据类型获取范围或者单张
            logRequest.setVoucherCode(voucher.getVoucherCode());
            logRequest.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll("[a-zA-Z]", "")));
            logRequest.setInitiatedBy("");
            logRequest.setPosCode(pos.getPosCode());
            logRequest.setBatchCode(voucher.getVoucherBatchCode());
            logRequest.setLoginSource("");
            logRequest.setDenomination(voucher.getDenomination());
            logRequest.setActualOutlet(voucher.getMopCode());
            logRequest.setForwardingEntityId("");
            logRequest.setTransactionMode(request.getTransactionModeId());
            logRequest.setVoucherEffectiveDate(voucher.getVoucherEffectiveDate());
            CustomerInfo customerInfo = new CustomerInfo();
            if (null != request.getCardholderInfo()) {
                customerInfo = request.getCardholderInfo();
            } else if (null != request.getPurchaserInfo()) {
                customerInfo = request.getPurchaserInfo();
            }

            if (null != customerInfo) {
                logRequest.setCustomerSalutation(customerInfo.getSalutation());
                logRequest.setCustomerFirstName(customerInfo.getFirstName());
                logRequest.setCustomerLastName(customerInfo.getLastName());
                logRequest.setMobile(customerInfo.getMobile());
                logRequest.setCorporateName(customerInfo.getCorporatename());
                logRequest.setEmail(customerInfo.getEmail());
                logRequest.setCustomerType(customerInfo.getCustomerType());
            }

            if (StringUtil.isEmpty(request.getInvoiceNumber())) {
                logRequest.setInvoiceNumber(invoiceNumber);
            } else {
                logRequest.setInvoiceNumber(request.getInvoiceNumber());
            }

//            logRequest.setOtherInputParameter(JSON.toJSONString(request));
            logRequest.setSuccessOrFailure(voucherSuccessStatus.getOrDefault(voucher.getVoucherCode(),success));
            logRequest.setResponseMessage(responseMessage);

            logRequest.setCreateUser("");
            logRequest.setCreateTime(new Date());
            logRequest.setReferenceNumber(String.valueOf(request.getTransactionId()));
            logRequest.setTransactionChannel(TransactionChannelEnum.API.getCode());

            //MER-1844
            if (logRequest.getSuccessOrFailure().equals("0")) {
                logRequest.setApproveCode(approvalCode);
            } else {
                logRequest.setApproveCode(" ");
            }
            logRequest.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            logRequest.setBatchId(String.valueOf(batchId));
            logRequest.setCardEntryMode("Swiped");
            if (StringUtil.isNotEmpty(request.getNotes())) {
                logRequest.setNotes(request.getNotes());
            }
            log.info("logRequest:{}", JSON.toJSONString(logRequest));
            this.createTransactionData(logRequest);
        }

    }

    private static String createLogLeafletGetTransationType(TransactionRequest request) {
        String transactionType = null;
        if (request.getActionType().equals("1") && request.getTransactionTypeId().equals(705)) {
            transactionType = TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode();
        } else if (request.getActionType().equals("1") && request.getTransactionTypeId().equals(702)) {
            transactionType = TransactionTypeEnum.GIFT_CARD_REDEEM.getCode();
        } else if (request.getActionType().equals("2") && request.getTransactionTypeId().equals(705)) {
            transactionType = TransactionTypeEnum.GIFT_CARD_ACTIVATE_VALIDATE.getCode();
        } else if (request.getActionType().equals("2") && request.getTransactionTypeId().equals(702)) {
            transactionType = TransactionTypeEnum.GIFT_CARD_REDEEM_VALIDATE.getCode();
        }
        return transactionType;
    }

    public CreateCustomerOrderRequest createCustomerOrderByCreateAndIssue(String invoiceNumber, OutletResponse outlet, CustomerInfo customerInfo
            , Voucher voucher, String transactionId, String notes, Integer numOfCards
            , List<CreateCustomerOrderDetailsRequest> detailsRequestList, BigDecimal voucherAmount) {
        CreateCustomerOrderRequest customerOrderRequest = new CreateCustomerOrderRequest();
        //customerOrderRequest.setCustomerOrderCode(transactionId);
        customerOrderRequest.setIssuerCode(voucher.getIssuerCode());
        customerOrderRequest.setOutletCode(outlet.getOutletCode());
        customerOrderRequest.setInvoiceNo(invoiceNumber);
        customerOrderRequest.setPurchaseOrderNo(outlet.getOutletName() + System.currentTimeMillis());
        customerOrderRequest.setMopCode(voucher.getMopCode());
        customerOrderRequest.setVoucherNum(numOfCards);
        customerOrderRequest.setVoucherAmount(voucherAmount);
        customerOrderRequest.setDiscount(new BigDecimal("0"));
        customerOrderRequest.setAmount(BigDecimal.ZERO);
        if (voucher.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
            customerOrderRequest.setVoucherBatchCode(voucher.getVoucherBatchCode());
        }

        customerOrderRequest.setContactFirstName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getFirstName()) ?
                customerInfo.getFirstName() : API_CUSTOMER);
        customerOrderRequest.setContactLastName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getLastName()) ?
                customerInfo.getLastName() : API_CUSTOMER);
        customerOrderRequest.setContactPhone(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getMobile()) ?
                customerInfo.getMobile() : API_CUSTOMER);
        customerOrderRequest.setCompanyName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCorporatename()) ?
                customerInfo.getCorporatename() : API_CUSTOMER);
        customerOrderRequest.setContactEmail(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getEmail()) ?
                customerInfo.getEmail() : API_CUSTOMER);


        customerOrderRequest.setCurrencyCode(API_CUSTOMER);
        customerOrderRequest.setCustomerCode(API_CUSTOMER);
        customerOrderRequest.setCustomerName(API_CUSTOMER);
        customerOrderRequest.setCustomerType(API_CUSTOMER);
        customerOrderRequest.setProductCategoryCode("");
        customerOrderRequest.setDiscountType("");
        customerOrderRequest.setCreateUser("api");
        customerOrderRequest.setMeansOfPaymentCode("");
        customerOrderRequest.setShippingAddress("");
        customerOrderRequest.setCustomerRemarks(notes);
        customerOrderRequest.setStatus(CustomerOrderStatusEnum.API.getStatus());
        customerOrderRequest.setReleaseTime(new Date());

        //detailsRequestList将相同cpgCode的数据的voucherNum相加
        Map<String, CreateCustomerOrderDetailsRequest> map = new HashMap<>();
        for (CreateCustomerOrderDetailsRequest detailsRequest : detailsRequestList) {
            if (map.containsKey(detailsRequest.getCpgCode())) {
                CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = map.get(detailsRequest.getCpgCode());
                createCustomerOrderDetailsRequest.setVoucherNum(createCustomerOrderDetailsRequest.getVoucherNum() + detailsRequest.getVoucherNum());
            } else {
                map.put(detailsRequest.getCpgCode(), detailsRequest);
            }
        }
        //map转list
        List<CreateCustomerOrderDetailsRequest> list = new ArrayList<>();
        for (Map.Entry<String, CreateCustomerOrderDetailsRequest> entry : map.entrySet()) {
            list.add(entry.getValue());
        }



        customerOrderRequest.setCreateCustomerOrderDetailsRequests(list/*Lists.newArrayList(
                CreateCustomerOrderDetailsRequest
                        .builder()
                        .cpgCode(voucher.getCpgCode())
                        .voucherNum(1)
                        .deleteStatus(2)//自动生成
                        .denomination(voucher.getDenomination())
                        .build()
        )*/);
        log.info("createCustomerOrder:{}", JSON.toJSONString(customerOrderRequest));
        Result<String> customerOrder = customerOrderService.createCustomerOrder(customerOrderRequest);
        if (!customerOrder.isSuccess()) {
            throw new GTechBaseException(customerOrder.getCode(), customerOrder.getMessage());
        }
        log.info("createCustomerOrder Result :{}", JSON.toJSONString(customerOrder));
        return customerOrderRequest;
    }


    private void combinedOrder(String transactionId, String numberOfCards, GetCpgResponse cpg, CustomerOrder customerOrder) {
        // 合并订单数据
        customerOrder.setVoucherNum(customerOrder.getVoucherNum() + Integer.parseInt(numberOfCards));
        customerOrder.setVoucherAmount(customerOrder.getVoucherAmount().add(cpg.getDenomination().multiply(new BigDecimal(numberOfCards))));
        // 更新订单数据
        Example customerExample = new Example(CustomerOrder.class);
        customerExample.createCriteria().andEqualTo("customerOrderCode", transactionId);
        customerOrderMapper.updateByConditionSelective(customerOrder, customerExample);
        //详情信息
        //更新订单详情
        CustomerOrderDetails customerOrderDetails = customerOrderDetailsMapper.selectOne(CustomerOrderDetails
                .builder()
                .customerOrderCode(transactionId)
                .cpgCode(cpg.getCpgCode())
                .build());
        if (null != customerOrderDetails) {
            customerOrderDetails.setVoucherAmount(customerOrderDetails.getVoucherAmount().add(cpg.getDenomination().multiply(new BigDecimal(numberOfCards))));
            customerOrderDetails.setAmount(customerOrderDetails.getAmount().add(cpg.getDenomination().multiply(new BigDecimal(numberOfCards))));
            Integer voucherNum = customerOrderDetails.getVoucherNum();
            customerOrderDetails.setVoucherNum(voucherNum+ Integer.parseInt(numberOfCards));
            Example detailExample = new Example(CustomerOrderDetails.class);
            detailExample.createCriteria()
                    .andEqualTo("customerOrderCode", transactionId)
                    .andEqualTo("cpgCode", cpg.getCpgCode());
            log.info("updateByConditionSelective:{}", JSON.toJSONString(customerOrderDetails));
            customerOrderDetailsMapper.updateByConditionSelective(customerOrderDetails, detailExample);
        } else {
            CustomerOrderDetails details = new CustomerOrderDetails();
            details.setAmount(cpg.getDenomination().multiply(new BigDecimal(numberOfCards)));
            details.setDiscount(new BigDecimal("0"));
            details.setDenomination(cpg.getDenomination());
            details.setDeleteStatus(2);
            details.setCreateUser("api");
            details.setCreateTime(new Date());
            details.setUpdateTime(new Date());
            details.setCustomerOrderDetailsCode(gvCodeHelper.generateCustomerOrderDetailCode());
            details.setCustomerOrderCode(transactionId);
            details.setCpgCode(cpg.getCpgCode());
            details.setVoucherAmount(cpg.getDenomination().multiply(new BigDecimal(numberOfCards)));
            details.setVoucherNum(Integer.valueOf(numberOfCards));
            log.info("insertSelective:{}", JSON.toJSONString(details));
            customerOrderDetailsMapper.insertSelective(details);
        }


    }


    @Override
    public void requestLog(TransactionRequest request, String terminalId, ResponseEntity<TransactionResponse> response, String batchId) {
        /*if (request == null || response == null || response.getBody() == null) {
            return;
        }
        
        // 使用缓存的TypeReference
        TransactionResponse responseBody = response.getBody();
        if (responseBody != null) {
            // 避免不必要的JSON序列化
            log.debug("Transaction response - code:{}, message:{}, transactionId:{}", 
                responseBody.getResponseCode(), 
                responseBody.getResponseMessage(),
                responseBody.getTransactionId());
        }
        
        //获取交易记录的成功或者失败
        Integer responseCode = responseBody.getResponseCode();
        String responseMessage = responseBody.getResponseMessage();
        String successOfFailure = "0";
        *//*if (0 == responseCode) {
            //成功
            successOfFailure = "0";

        } else {
            //失败
            successOfFailure = "1";

        }*//*


        Result<PosResponse> pos = posService.getPos(GetPosRequest.builder().machineId(terminalId).build());
        if (null == pos.getData()) {
            return;
        }

        String invoiceNo = request.getInvoiceNumber();
        if (StringUtils.isEmpty(invoiceNo)) {
            invoiceNo = gvCodeHelper.generateInvoiceNumber();
        }

//        String key = String.valueOf(request.getTransactionId())
//                + String.valueOf(response.getBody().getBatchId())
//                + String.valueOf(invoiceNo);
        String approvalCode = (String)redisTemplate.opsForValue().get("APPROVALCODE:" + request.getTransactionId());
        if (StringUtils.isEmpty(approvalCode)) {
            approvalCode = gvCodeHelper.generateApproveCode();
            redisTemplate.opsForValue().set("APPROVALCODE:" + request.getTransactionId(),approvalCode,2, TimeUnit.DAYS);
        }





        List<LineItem> lineItems = request.getLineItems();
        String transactionType = createLogLeafletGetTransationType(request);


        OutletResponse outletResponse;
        try {
            outletResponse = outletService.getOutlet(GetOutletRequest.builder().outletCode(pos.getData().getOutletCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }

        TransactionResponse body = responseBody;

        List<LineItemResponseInfo> invalidLineItems = body.getInvalidLineItems();
        Map<String, String> voucherSuccessStatus = new HashMap<>();
        if (CollectionUtils.isNotEmpty(invalidLineItems)) {
            invalidLineItems.stream()
                    .flatMap(invalidLineItem -> invalidLineItem.getCards().stream())
                    .forEach(card -> voucherSuccessStatus.put(card.getCardNumber(), "1"));
        }




        log.info("开始执行");
        for (LineItem lineItem : lineItems) {
            log.info("执行中");
            if (lineItem.getInputType().equals(String.valueOf(GvPosInputTypesEnum.INDIVIDUAL.getCode()))) {
                if (StringUtil.isNotBlank(lineItem.getCardInfo().getCardNumber())){
                    Voucher voucher = voucherMapper.selectOne(Voucher.builder().voucherCode(lineItem.getCardInfo().getCardNumber()).build());
                    if (null != voucher) {
                        checkVouchersSellTransactionAndCreateCustomerOrder(request, pos, invoiceNo, approvalCode, transactionType, outletResponse, Lists.newArrayList(voucher), String.valueOf(1), successOfFailure, responseMessage, batchId,voucherSuccessStatus);
                    }
                }else {
                    log.error("lineItem.getCardInfo().getCardNumber() 为空:{}", JSONObject.toJSONString(request));
                }
            } else if (lineItem.getInputType().equals(String.valueOf(GvPosInputTypesEnum.RANGE.getCode()))) {
                String startCode = lineItem.getStartCardInfo().getCardNumber();
                String endCode = lineItem.getEndCardInfo().getCardNumber();

                if (StringUtil.isNotBlank(lineItem.getStartCardInfo().getTrackData())//覆盖barCode为券号的场景
                        && StringUtil.isNotBlank(lineItem.getEndCardInfo().getTrackData())
                        && lineItem.getStartCardInfo().getTrackData().matches("\\d{16}")
                        && lineItem.getEndCardInfo().getTrackData().matches("\\d{16}")
                ) {
                    startCode = lineItem.getStartCardInfo().getTrackData();
                    endCode = lineItem.getEndCardInfo().getTrackData();
                } else if (StringUtil.isBlank(startCode) && StringUtil.isBlank(endCode)
                        && StringUtil.isNotBlank(lineItem.getStartCardInfo().getTrackData())
                        && StringUtil.isNotBlank(lineItem.getEndCardInfo().getTrackData())
                ) {
                    startCode = voucherNumberHelper.barCodeToVoucher(lineItem.getStartCardInfo().getTrackData());
                    endCode = voucherNumberHelper.barCodeToVoucher(lineItem.getEndCardInfo().getTrackData());
                }

                if(StringUtil.isBlank(startCode) && StringUtil.isBlank(endCode)){
                    log.error("--------------------------------startCode & endCode 为空,{}",JSON.toJSONString(request));
                    return;
                }

                startCode = startCode.replaceAll("[a-zA-Z]", "");
                endCode = endCode.replaceAll("[a-zA-Z]", "");
                if (StringUtil.isNotBlank(startCode) && StringUtil.isNotBlank(endCode)){
                    Example example = new Example(Voucher.class);
                    example.createCriteria()
                            .andGreaterThanOrEqualTo(Voucher.C_VOUCHER_CODE_NUM, Long.valueOf(startCode))
                            .andLessThanOrEqualTo(Voucher.C_VOUCHER_CODE_NUM, Long.valueOf(endCode));
                    List<Voucher> vouchers = voucherMapper.selectByCondition(example);
                    if (CollectionUtils.isNotEmpty(vouchers)) {
                        checkVouchersSellTransactionAndCreateCustomerOrder(request, pos, invoiceNo, approvalCode, transactionType
                                , outletResponse, vouchers, String.valueOf(vouchers.size()), successOfFailure, responseMessage,batchId, voucherSuccessStatus);
                    }
                }else {
                    log.error("lineItem.getStartCardInfo().getCardNumber(); lineItem.getEndCardInfo().getCardNumber() 券为空:{}", JSONObject.toJSONString(request));
                }


            }
        }


        //失败回滚数据

        if (!request.getActionType().equals("1") || request.getTransactionTypeId() != 702){
            return;
        }




        if (CollectionUtils.isEmpty(invalidLineItems) || invalidLineItems.isEmpty()) {
            return;
        }

        List<LineItemResponseInfo> rollback = body.getLineItems();
        if (CollectionUtils.isEmpty(rollback) || rollback.isEmpty()) {
            return;
        }
        log.info("开始回滚");
        for (LineItemResponseInfo responseInfo : rollback) {
            List<Voucher> vouchers = new ArrayList<>();

            if (StringUtil.isNotBlank(responseInfo.getStartCardNumber()) && StringUtil.isNotBlank(responseInfo.getEndCardNumber())){
                vouchers = voucherService.queryStartAndEndVoucher(responseInfo.getStartCardNumber(), responseInfo.getEndCardNumber());
            }else {
                log.error("responseInfo.getStartCardNumber() responseInfo.getEndCardNumber() 券为空:{}", JSONObject.toJSONString(request));
            }

            log.info("回滚的券数量:{} - {}", responseInfo.getStartCardNumber(), responseInfo.getEndCardNumber());
            if (CollectionUtils.isEmpty(vouchers)) {
                log.info("没有需要回滚的券");
                return;
            }

            vouchers.forEach(x -> {
                //回滚使用场景
                voucherService.cancelRedeem(CancelRedeemRequest.builder()
                        .amount(x.getDenomination())
                        .batchID(gvCodeHelper.generateBatchId())
                        .voucherPin(x.getVoucherPin())
                        .cardNumber(x.getVoucherCode())
                        .dateAtClient(new Date())
                        .notes("手动回滚")
                        .originalApprovalCode(gvCodeHelper.generateApproveCode())
                        .originalBatchNumber(batchId)
                        .originalInvoiceNumber(gvCodeHelper.generateInvoiceNumber())
                        .originalTransactionId(Integer.valueOf(request.getTransactionId()))
                        .terminalId(terminalId)
                        .transactionId(Integer.valueOf(request.getTransactionId()))
                        .build());


            });
            log.info("回滚结束");
        }
*/

    }

    private void checkVouchersSellTransactionAndCreateCustomerOrder(TransactionRequest request, Result<PosResponse> pos, String invoiceNo, String approvalCode
            , String transactionType, OutletResponse outletResponse, List<Voucher> vouchers, String numOfCards, String success, String message, String batchId, Map<String, String> voucherSuccessStatus) {
        log.info("执行添加交易记录");
        createLogLeaflet(request, vouchers, pos.getData(), approvalCode, invoiceNo, transactionType, outletResponse, success, message, batchId, voucherSuccessStatus);
        log.info("执行添加交易记录结束");
        if ((TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode()).equals(transactionType) && GvcoreConstants.SUCCESS.equals(success)) {
            //自动创建customerOrder

            //查询是否有销售记录 有则不创建
            //查询是否有销售记录 有则不创建
            Example tExample = new Example(TransactionData.class);
            tExample.createCriteria()
                    .andIn(TransactionData.C_VOUCHER_CODE, vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()))
                    .andEqualTo(TransactionData.C_SUCCESS_OR_FAILURE, "0");
            List<TransactionData> transactionData = transactionDataMapper.selectByCondition(tExample);
            //分别查询transactionData中transactionType为销售的记录和transactionType为取消销售的记录
            List<TransactionData> sellList = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_SELL.getCode())).collect(Collectors.toList());
            List<TransactionData> cancelSell = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getCode())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(sellList) && cancelSell.size() == sellList.size()) {
                //插入销售记录
                log.info("执行添加销售记录");
                createLogLeaflet(request, vouchers, pos.getData(), approvalCode, invoiceNo, TransactionTypeEnum.GIFT_CARD_SELL.getCode(), outletResponse, success, message, batchId, voucherSuccessStatus);
                log.info("执行添加销售记录结束");
                return;
            }


            //插入销售记录
            log.info("执行添加销售记录");
            createLogLeaflet(request, vouchers, pos.getData(), approvalCode, invoiceNo, TransactionTypeEnum.GIFT_CARD_SELL.getCode(), outletResponse, success, message, batchId, voucherSuccessStatus);
            log.info("执行添加销售记录结束");
            List<GetCpgResponse> cpgByCpgNameList = new ArrayList<>();
            List<CreateCustomerOrderDetailsRequest> detailsRequestList = new ArrayList<>();
            try {
                for (Voucher voucher : vouchers) {
                    GetCpgResponse data = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucher.getCpgCode()).build()).getData();
                    if (null != data) {
                        cpgByCpgNameList.add(data);
                    }
                    detailsRequestList.add(CreateCustomerOrderDetailsRequest
                            .builder()
                            .cpgCode(voucher.getCpgCode())
                            .voucherNum(1)
                            .deleteStatus(2)//自动生成
                            .denomination(voucher.getDenomination())
                            .build());

                }

            } catch (Exception e) {
                //cpg error
                throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
            }
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (GetCpgResponse getCpgResponse : cpgByCpgNameList) {
                totalAmount = totalAmount.add(getCpgResponse.getDenomination());
            }

            createCustomerOrderByCreateAndIssue(request.getInvoiceNumber(), outletResponse, null,
                    Voucher.builder()
                            .mopCode(cpgByCpgNameList.get(0).getMopCode())
                            .issuerCode(cpgByCpgNameList.get(0).getIssuerCode())
                            .voucherBatchCode(vouchers.get(0).getVoucherBatchCode())
                            .build(),
                    String.valueOf(request.getTransactionId()) + String.valueOf(batchId) + invoiceNo, ""
                    , Integer.valueOf(numOfCards), detailsRequestList, totalAmount);

            //修改booklet状态
            if (CollectionUtils.isNotEmpty(vouchers)) {
                voucherBookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                        .voucherCode(vouchers.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList()))
                        .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                        .type("0")
                        .build());
            }

        }
        log.info("执行完毕");
    }


    @Override
    @Async("asyncLogExecutor")
    public void responseLog(ResponseEntity<TransactionResponse> response) {
       /* if (response == null || response.getBody() == null) {
            return;
        }
        TransactionResponse body = response.getBody();
        
        // 避免不必要的JSON序列化
        this.updateTransactionDataResponseMessage(
            String.valueOf(body.getTransactionId()), 
            body.getResponseMessage(), 
            body.getResponseCode(), 
            body
        );*/
    }


    @Override
    public Map<String, BigDecimal> sumAmountGroupByBillNumber(List<String> billNumberList) {

        if (CollectionUtils.isEmpty(billNumberList)) {
            return Collections.emptyMap();
        }

        List<TransactionData> list = transactionDataMapper.sumAmountGroupByBillNumber(billNumberList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream()
                .collect(Collectors.toMap(TransactionData::getBillNumber, TransactionData::getDenomination));
    }



    @Override
    public BatchcloseResponse batchClose(BatchcloseRequest param, String token) {

        StopWatch batchClose = new StopWatch("batchClose");
        batchClose.start("batchClose db query");
        BatchcloseResponse batchcloseResponse = new BatchcloseResponse();
        batchcloseResponse.setReloadCount(0);
        batchcloseResponse.setReloadAmount(BigDecimal.ZERO);
        batchcloseResponse.setActivationCount(0);
        batchcloseResponse.setActivationAmount(BigDecimal.ZERO);
        batchcloseResponse.setRedemptionCount(0);
        batchcloseResponse.setCancelRedeemCount(0);
        batchcloseResponse.setCancelRedeemAmount(BigDecimal.ZERO);
        batchcloseResponse.setCancelLoadAmount(BigDecimal.ZERO);
        batchcloseResponse.setCancelLoadCount(0);
        batchcloseResponse.setCancelActivationCount(0);
        batchcloseResponse.setCancelActivationAmount(BigDecimal.ZERO);
        batchcloseResponse.setApprovalCode(null);
        batchcloseResponse.setNotes(null);
        batchcloseResponse.setErrorCode(null);
        batchcloseResponse.setErrorDescription(null);
        batchcloseResponse.setTransactionType("20");
        batchcloseResponse.setRedemptionAmount(BigDecimal.ZERO);
        batchcloseResponse.setTransactionId(param.getTransactionId());

        //int newBatchNumber = RandomUtils.nextInt(0, 9999999);
        String batchId = gvCodeHelper.generateBatchId();

        batchcloseResponse.setNewBatchNumber(Integer.valueOf(batchId.substring(batchId.length()-4)));

        AuthorizePayload oldAuthorizePayload = (AuthorizePayload) jwtService.resolve(token.substring(7), AuthorizePayload.class);


        AuthorizePayload authorizePayload = new AuthorizePayload();
        BeanUtils.copyProperties(oldAuthorizePayload, authorizePayload);
        authorizePayload.setCurrentBatchNumber(batchId);
        log.info("authorize jwtService.create-Request:{}", JSON.toJSONString(authorizePayload));

        String newToken = jwtService.create(authorizePayload);
        long expireTimes = System.currentTimeMillis() + effectiveHours  * 60 * 1000L;
        redisTemplate.opsForValue().set("GV:AUTHORIZE_BATCHID:" + newToken, batchId, expireTimes, TimeUnit.MILLISECONDS);


        batchcloseResponse.setNewAuthToken(newToken);




        token = token.substring(7);

        //跟距terminalId在redis中获取当前批次号
        String batchNumber = (String) redisTemplate.opsForValue().get("GV:AUTHORIZE_BATCHID:" + token);

        if (batchNumber == null) {


            return batchcloseResponse;
        }


        Example example = new Example(TransactionData.class);
        example.createCriteria()
                .andEqualTo("batchId", batchNumber)
                .andEqualTo(TransactionData.C_SUCCESS_OR_FAILURE, "0");



        // 使用List存储异步任务
        List<CompletableFuture<List<TransactionData>>> futures = new ArrayList<>(64);

        for (int i = 0; i < 64; i++) {
            final String tableName = "gv_transaction_data_" + i;
            CompletableFuture<List<TransactionData>> future = CompletableFuture.supplyAsync(() -> {
                return transactionDataMapper.selectTransactionList(tableName, String.valueOf(batchNumber), "0");
            }, batchProcessExecutor); // 使用批量处理线程池
            futures.add(future);
        }

        // 等待所有future完成并收集结果
        List<TransactionData> transactions = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        batchClose.stop();
        batchClose.start("batchClose data process");
        if (CollectionUtils.isEmpty(transactions)) {
            return batchcloseResponse;
        }

        Map<String, List<TransactionData>> transactionDatas = transactions.stream().collect(Collectors.groupingBy(TransactionData::getTransactionType));
        transactionDatas.forEach((key, value) -> {
            switch (key) {
               /* case "1":
                    batchcloseResponse.setReloadCount(value.size());
                    batchcloseResponse.setReloadAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    break;*/
                case "5":
                    batchcloseResponse.setActivationCount(value.size());
                    batchcloseResponse.setActivationAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    break;
                case "22":
                    batchcloseResponse.setActivationCount(value.size());
                    batchcloseResponse.setActivationAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    break;
                case "1":
                    batchcloseResponse.setRedemptionCount(value.size());
                    batchcloseResponse.setRedemptionAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    break;
                case "11":
                    if (Boolean.TRUE.equals(param.getIsRedeemCancelCountsProvided())) {
                        batchcloseResponse.setCancelRedeemCount(value.size());
                    }
                    if (Boolean.TRUE.equals(param.getIsRedeemCancelAmountsProvided())) {
                        batchcloseResponse.setCancelRedeemAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    break;
                /*case "5":
                    batchcloseResponse.setCancelLoadCount(value.size());
                    batchcloseResponse.setCancelLoadAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    break;*/
                case "12":
                    if (Boolean.TRUE.equals(param.getIsActivationCancelCountsProvided())) {
                        batchcloseResponse.setCancelActivationCount(value.size());
                    }
                    if (Boolean.TRUE.equals(param.getIsActivationCancelAmountsProvided())) {
                        batchcloseResponse.setCancelActivationAmount(value.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    break;
                default:
                    break;
            }
        });


        //数据对比
        if (Boolean.TRUE.equals(param.getBatchValidationRequired())) {
            //param的字段数据和batchcloseResponse的字段对比,判断是否一致
            if (param.getReloadAmount().compareTo(batchcloseResponse.getReloadAmount()) != 0
                    || param.getReloadCount().compareTo(batchcloseResponse.getReloadCount()) != 0
                    || param.getActivationAmount().compareTo(batchcloseResponse.getActivationAmount()) != 0
                    || param.getActivationCount().compareTo(batchcloseResponse.getActivationCount()) != 0
                    || param.getRedemptionCount().compareTo(batchcloseResponse.getRedemptionCount()) != 0
                    || param.getRedemptionAmount().compareTo(batchcloseResponse.getRedemptionAmount()) != 0
                    || param.getCancelRedeemCount().compareTo(batchcloseResponse.getCancelRedeemCount()) != 0
                    || param.getCancelRedeemAmount().compareTo(batchcloseResponse.getCancelRedeemAmount()) != 0
                    || param.getCancelActivationCount().compareTo(batchcloseResponse.getCancelActivationCount()) != 0
                    || param.getCancelActivationAmount().compareTo(batchcloseResponse.getCancelActivationAmount()) != 0
            ) {
                batchcloseResponse.setErrorCode(String.valueOf(GvPosCommonResponseCodesEnum.BATCH_CLOSE_FAILED.getResponseCode()));
                batchcloseResponse.setErrorDescription(GvPosCommonResponseCodesEnum.BATCH_CLOSE_FAILED.getResponseMessage());
                return batchcloseResponse;
            }
        }
        batchClose.stop();
        log.info("batchClose totalTime:{}", batchClose.getTotalTimeSeconds());
        log.info("batchClose info :{}", batchClose.toString());

        return batchcloseResponse;

    }


    @Override
    public CardtransactionhistoryResponse cardtransactionhistory(CardtransactionhistoryRequest param) {
        CardtransactionhistoryResponse response = new CardtransactionhistoryResponse();

        response.setTrackData(null);
        response.setCardNumber(null);
        response.setCardPin(null);
        response.setInvoiceNumber(null);
        response.setAmount(new BigDecimal("0"));
        response.setBillAmount(new BigDecimal("0"));
        response.setOriginalInvoiceNumber(null);
        response.setOriginalTransactionId(0);
        response.setOriginalBatchNumber(0);
        response.setOriginalApprovalCode(null);
        response.setOriginalAmount(null);
        response.setApprovalCode(null);
        response.setAddonCardNumber(null);
        response.setAddonCardTrackData(null);
        response.setTransferCardNumber(null);
        response.setMerchantName(null);
        response.setAdjustmentAmount(new BigDecimal("0"));
        String times = "0001-01-01T00:00:00";
        response.setCardExpiry(times);
        response.setOriginalCardNumber(null);
        response.setOriginalCardPin(null);
        response.setCardProgramID(null);
        response.setCorporateName(null);
        response.setNotes("0");
        response.setSettlementDate(times);
        response.setExpiry(times);
        response.setPurchaseOrderNumber(null);
        response.setPurchaseOrderValue(new BigDecimal("0"));
        response.setDiscountPercentage(new BigDecimal("0"));
        response.setDiscountAmount(new BigDecimal("0"));
        response.setPaymentMode(0);
        response.setPaymentDetails(null);
        response.setBulkType(false);
        response.setExternalCorporateId(null);
        response.setExternalCardNumber(null);
        response.setMerchantOutletName(null);
        response.setMerchantOutletAddress1(null);
        response.setMerchantOutletAddress2(null);
        response.setMerchantOutletCity(null);
        response.setMerchantOutletState(null);
        response.setMerchantOutletPinCode(null);
        response.setMerchantOutletPhone(null);
        response.setMaskCard(null);
        response.setPrintMerchantCopy(null);
        response.setInvoiceNumberMandatory(null);
        response.setNumericUserPwd(null);
        response.setIntegerAmount(null);
        response.setCulture(null);
        response.setCurrencySymbol(null);
        response.setCurrencyPosition(null);
        response.setCurrencyDecimalDigits(null);
        response.setDisplayUnitForPoints(null);
        response.setReceiptFooterLine1(null);
        response.setReceiptFooterLine2(null);
        response.setReceiptFooterLine3(null);
        response.setReceiptFooterLine4(null);
        response.setResult(false);
        response.setTransferCardExpiry(times);
        response.setTransferCardBalance(new BigDecimal("0"));
        response.setCardStatusId(0);
        response.setCardStatus("0");
        response.setCardCurrencySymbol(null);
        response.setActivationDate("0");
        response.setSVRecentTransactions(null);
        response.setCardType("0");
        response.setTransferCardTrackData(null);
        response.setTransferCardPin(null);
        response.setCumulativeAmountSpent(new BigDecimal("0"));
        response.setCurrencyConversionRate(new BigDecimal("0"));
        response.setCurrencyConvertedAmount(new BigDecimal("0"));
        response.setCardHolderName(null);
        response.setStoredValueUnitID(0);
        response.setXactionAmountConvertedValue(new BigDecimal("0"));
        response.setStoredValueConvertedAmount(new BigDecimal("0"));
        response.setPromotionalValue(new BigDecimal("0"));
        response.setEarnedValue(new BigDecimal("0"));
        response.setTransactionAmount(new BigDecimal("0"));
        response.setPreviousBalance(new BigDecimal("0"));
        response.setUpgradedCardProgramGroupName(null);
        response.setNewBatchNumber(0);
        response.setOriginalActivationAmount(new BigDecimal("0"));
        response.setCardCreationType(null);
        response.setErrorCode(null);
        response.setErrorDescription(null);
        response.setCardProgramGroupType("0");
        response.setActivationCode(null);
        response.setActivationURL(null);
        response.setReloadableAmount(new BigDecimal("0"));
        response.setBarcode(null);
        response.setCustomer(null);
        response.setCurrentBatchNumber(0);
        response.setTransactionType("0");
        response.setThemeId("0");
        response.setMerchantId(0);
        response.setMID(null);
        response.setTotalRecordsCount(0);
        response.setActivationDate(times);

        Voucher toBeVerified = null;
        if (StringUtil.isNotBlank(param.getCardNumber())){
            Voucher example1 = new Voucher();
            example1.setVoucherCode(param.getCardNumber());
            toBeVerified = voucherMapper.selectOne(example1);

        }


        if (null == toBeVerified) {
            response.setResponseCode(Integer.valueOf(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.code()));
            response.setResponseMessage(ResultErrorCodeEnum.VOUCHER_CODE_CHECK_FAILED.desc());
            return response;
        }

        Cpg cpg = masterDataCache.getCpg(toBeVerified.getCpgCode());

        GetCpgTypeRequest cpgTypeRequest = new GetCpgTypeRequest();
        cpgTypeRequest.setCpgTypeCode(cpg.getCpgTypeCode());
        Result<Object> cpgType = cpgTypeService.getCpgType(cpgTypeRequest);

        GetCpgTypeResponse cpgTypeData = (GetCpgTypeResponse) cpgType.getData();

        Pos posResponseResult = null;
        try {
            posResponseResult = masterDataCache.getPos(param.getTerminalId());
            if (null == voucherBatchService.getVoucherBatch(GetVoucherBatchRequest.builder().voucherBatchCode(toBeVerified.getVoucherBatchCode()).build()))
                throw new GTechBaseException();
            if (null == posResponseResult) throw new GTechBaseException();
        } catch (GTechBaseException e) {
            throw new GTechBaseException();
        }

        try {
            if (null == masterDataCache.getOutlet(posResponseResult.getOutletCode()))
                throw new GTechBaseException();
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }

        PageMethod.startPage(1, 30,false);
        Example example = new Example(TransactionData.class);
        example.createCriteria()
                .andEqualTo("voucherCode", param.getCardNumber())
                .andEqualTo(TransactionData.C_SUCCESS_OR_FAILURE, "0");
        example.orderBy("createTime").desc();
        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(example);

        ArrayList<SVRecentTransactionsResponse> svr = new ArrayList<>();
        List<Outlet> outlets = masterDataCache.getOutletByOutletCode(transactionData.stream().map(TransactionData::getOutletCode).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList()));
        Map<String, Outlet> outletMap = outlets.stream().collect(Collectors.toMap(Outlet::getOutletCode, x -> x));

        transactionData.forEach(x -> {
            if (Boolean.FALSE.equals(QCTransactionTypeEnum.doesItExist(x.getTransactionType()))) return;
            SVRecentTransactionsResponse jsonCopyBean = BeanCopyUtils.jsonCopyBean(x, SVRecentTransactionsResponse.class);

//            if (x.getSuccessOrFailure().equals("0")) {
//                jsonCopyBean.setTransactionStatus("SUCCESS");
//            } else {
//                jsonCopyBean.setTransactionStatus("FAILED");
//            }
            jsonCopyBean.setTransactionAmount(x.getDenomination());
//            jsonCopyBean.setBatchId(null);
            jsonCopyBean.setLoyalty(BigDecimal.ZERO);
//            jsonCopyBean.setReferenceNumber(null);
//            jsonCopyBean.setTransactionDateTime(DateUtil.format(x.getCreateTime(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
            jsonCopyBean.setVoucherBalance(x.getDenomination());
            jsonCopyBean.setVoucherNumber(x.getVoucherCode());
            jsonCopyBean.setTransactionDateAtServer(DateUtil.format(x.getCreateTime(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
            jsonCopyBean.setTransactionType(QCTransactionTypeEnum.getTypeDesc(String.valueOf(jsonCopyBean.getTransactionType())));
            jsonCopyBean.setTransactionDate(DateUtil.format(x.getTransactionDate(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
            Outlet outlet = null;
            try {
                outlet = outletMap.get(x.getOutletCode());
            } catch (Exception e) {
                outlet = null;
            }
            if (null != outlet) {
                jsonCopyBean.setStoreCode(outlet.getBusinessOutletCode());
                jsonCopyBean.setStoreName(outlet.getOutletName());
            }
            svr.add(jsonCopyBean);
        });

        response.setSVRecentTransactions(svr);
        response.setCardStatus(toBeVerified.getVoucherStatus() == 0
                ? VoucherEnableDisablePosEnum.getDescByCode(toBeVerified.getVoucherStatus())
                : VoucherStatusPosEnum.getDescByCode(toBeVerified.getStatus()));

        response.setCardCurrencySymbol(cpg.getCurrencyCode());
        response.setTotalRecordsCount(response.getSVRecentTransactions().size());
        response.setCardProgramGroupType(cpgTypeData.getCpgTypeName());

        response.setTransactionType("35");
        response.setTransactionId(param.getTransactionId());
        response.setAmount(toBeVerified.getDenomination());
        response.setApprovalCode(gvCodeHelper.generateApproveCode());
        response.setCardExpiry(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
        //BY 王伟
        response.setSettlementDate(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
        response.setExpiry(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
        response.setResponseCode(0);
        response.setResponseMessage("Transaction successful.");
        response.setActivationDate(DateUtil.format(toBeVerified.getVoucherEffectiveDate(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern()));
        response.setCardType(cpg.getCpgName());
        response.setCurrentBatchNumber(RandomUtils.nextInt(0, 9999999));
        response.setThemeId("Birthday");
        response.setCardStatusId(toBeVerified.getVoucherStatus() == 0
                ? Integer.valueOf(Objects.requireNonNull(VoucherEnableDisablePosEnum.getStatusIdByCode(toBeVerified.getVoucherStatus())))
                : Integer.valueOf(Objects.requireNonNull(VoucherStatusPosEnum.getStatusIdByCode(toBeVerified.getStatus()))));


        return response;
    }

    @Override
    public List<SalesDataResultDto> querySalesData(SalesDataDto param) {


        return transactionDataMapper.querySalesData(param);
    }

    @Override
    public BigDecimal countAmountByTransactionIdAndTypes(final String transactionId, final Collection<TransactionTypeEnum> transactionTypes, final String cpgCode) {
        List<BigDecimal> list = this.transactionDataMapper.countAmountByTransactionIdAndType(transactionId
                , transactionTypes.stream().map(TransactionTypeEnum::getCode).collect(Collectors.toList()), cpgCode);
        if (CollectionUtils.isEmpty(list)) return BigDecimal.ZERO;
        return BigDecimal.valueOf(list.stream().mapToDouble(BigDecimal::doubleValue).sum());
    }

    @Override
    public Integer countNumByTransactionIdAndType(final String transactionId, final Collection<TransactionTypeEnum> transactionTypes, final String cpgCode) {
        return this.transactionDataMapper.countNumByTransactionIdAndType(transactionId
                , transactionTypes.stream().map(TransactionTypeEnum::getCode).collect(Collectors.toList()), cpgCode);
    }

    @Override
    public TransactionData getPreviousTransactionData(final String voucherCode, final TransactionTypeEnum transactionType) {

        final Example getCondition = new Example(TransactionData.class);
        getCondition.createCriteria()
                .andEqualTo(TransactionData.C_VOUCHER_CODE, GvConvertUtils.toString(voucherCode, ""))
                .andEqualTo(TransactionData.C_TRANSACTION_TYPE, null == transactionType ? null : transactionType.getCode())
                .andEqualTo(TransactionData.C_SUCCESS_OR_FAILURE, 0);

        getCondition.orderBy(TransactionData.C_CREATE_TIME).desc();

        final List<TransactionData> transactionDataList = this.transactionDataMapper.selectByExampleAndRowBounds(getCondition, new PageRowBounds(0, 1));

        return CollectionUtils.isNotEmpty(transactionDataList) ? transactionDataList.get(0) : null;
    }

    @Override
    public List<TransactionDashboardDto> getTransactionDashboard(Date date, TransactionTypeEnum typeEnum) {

        List<TransactionDashboardDataBase> dataBases = transactionDataMapper.transctionDashboard(date, typeEnum.getCode());
        Map<String, List<TransactionDashboardDataBase>> outletMap = dataBases.stream().collect(Collectors.groupingBy(TransactionDashboardDataBase::getOutletCode));

        ArrayList<TransactionDashboardDto> result = new ArrayList<>();

        outletMap.forEach((k, v) -> {

            BigDecimal amount = v.stream().map(TransactionDashboardDataBase::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<String> transactionId = v.stream().map(TransactionDashboardDataBase::getTransactionId).distinct().collect(Collectors.toList());

            BigDecimal discount = getDiscount(transactionId);

            result.add(TransactionDashboardDto.builder()
                    .issuerCode(v.get(0).getIssuerCode())
                    .voucherCount(v.size())
                    .transactionTime(new Date())
                    .amount(amount)
                    .discount(discount)
                    .outletType(v.get(0).getOutletType())
                    .outletCode(k)
                    .build());
        });

        return result;
    }

    private BigDecimal getDiscount(List<String> transactionId) {


        Example orderExample = new Example(CustomerOrderDetails.class);
        orderExample.createCriteria().andIn(CustomerOrderDetails.C_CUSTOMER_ORDER_CODE, transactionId);
        List<CustomerOrderDetails> customerOrders = customerOrderDetailsMapper.selectByCondition(orderExample);
        return customerOrders.stream().map(CustomerOrderDetails::getDiscount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public String getLastInvoiceNo(String voucherCode) {

        final Example getCondition = new Example(TransactionData.class);
        getCondition.createCriteria()
                .andEqualTo(TransactionData.C_VOUCHER_CODE, GvConvertUtils.toString(voucherCode, ""))
                .andIsNotNull(TransactionData.C_INVOICE_NUMBER);

        getCondition.orderBy(TransactionData.C_ID).desc();

        final List<TransactionData> transactionDataList = this.transactionDataMapper.selectByExampleAndRowBounds(getCondition, new PageRowBounds(0, 1));

        return CollectionUtils.isNotEmpty(transactionDataList) ? transactionDataList.get(0).getInvoiceNumber() : null;
    }

    @Override
    public TransactionData selectLastTransactionDataByVoucherAndType(String voucherCode, TransactionTypeEnum... type) {

        Example example = new Example(TransactionData.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TransactionData.C_VOUCHER_CODE, voucherCode);

        if (null != type && ArrayUtils.isNotEmpty(type))
            criteria.andIn(TransactionData.C_TRANSACTION_TYPE, Stream.of(type).map(TransactionTypeEnum::getCode).collect(Collectors.toList()));

        example.orderBy(TransactionData.C_TRANSACTION_CODE).desc();

        List<TransactionData> transactionDataList = this.transactionDataMapper.selectByExampleAndRowBounds(example, new RowBounds(0, 1));

        return CollectionUtils.isEmpty(transactionDataList) ? null : transactionDataList.get(0);
    }

    @Override
    public Map<String, TransactionData> selectLastTransactionDataByVoucherCodesAndType(List<String> voucherCodes, TransactionTypeEnum... type) {

        if (CollectionUtils.isEmpty(voucherCodes)) return new HashMap<>();

        Example example = new Example(TransactionData.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andIn(TransactionData.C_VOUCHER_CODE, voucherCodes);

        if (null != type && ArrayUtils.isNotEmpty(type))
            criteria.andIn(TransactionData.C_TRANSACTION_TYPE, Stream.of(type).map(TransactionTypeEnum::getCode).collect(Collectors.toList()));

        example.orderBy(TransactionData.C_TRANSACTION_CODE).asc();

        List<TransactionData> transactionDataList = this.transactionDataMapper.selectByCondition(example);

        return CollectionUtils.isEmpty(transactionDataList) ? new HashMap<>() : transactionDataList.stream().collect(Collectors.toMap(TransactionData::getVoucherCode, Function.identity(), (k1, k2) -> k2));

    }
}
