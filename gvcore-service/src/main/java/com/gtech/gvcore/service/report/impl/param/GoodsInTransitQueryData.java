package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月26日
 */
@Getter
@Setter
@Accessors(chain = true)
public class GoodsInTransitQueryData extends PageParam implements ReportQueryParam {

    private Date transactionDateStart;

    private Date transactionDateEnd;

    private List<String> cpgCodeList;

    private List<String> voucherStatusList;

    private List<String> outboundCodeList;

    private List<String> inboundCodeList;

    private List<String> requestIdList;

    private Date voucherEffectiveDateStart;

    private Date voucherEffectiveDateEnd;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

}


