package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.outletproductcategory.*;
import com.gtech.gvcore.common.response.outletproductcategory.OutletProductCategoryResponse;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface OutletProductCategoryService {

    Result<Void> createOutletProductCategory(CreateOutletProductCategoryRequest param);

    Result<Void> updateOutletProductCategory(UpdateOutletProductCategoryRequest param);

    Result<Void> deleteOutletProductCategory(DeleteOutletProductCategoryRequest param);

    PageResult<OutletProductCategoryResponse> queryOutletProductCategoryList(QueryOutletProductCategoryRequest param);

    OutletProductCategoryResponse getOutletProductCategory(GetOutletProductCategoryRequest param);



}
