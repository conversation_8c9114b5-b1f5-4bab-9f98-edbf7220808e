package com.gtech.gvcore.service;


import com.gtech.gvcore.common.request.voucherbatch.CreateVoucherBatchRequest;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.dao.model.VoucherBooklet;

import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @Date 2022/3/2 13:53
 */
public interface VoucherBookletService {



     void createVoucherBooklet(CreateVoucherBatchRequest batchRequest,String bookletCode,String createUser);


    int deleteByCondition(String voucherBatchCode);
    
    List<VoucherBooklet> queryBookletByCodeList(List<String> bookletCodeList);

    String queryMaxBooklet(String s);

    void voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto dto);

    VoucherBooklet getBookletByCode(String bookletCode);


}
