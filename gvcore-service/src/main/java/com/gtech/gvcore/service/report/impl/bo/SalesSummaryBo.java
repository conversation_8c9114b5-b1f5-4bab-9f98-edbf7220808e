package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022年6月22日
 */
@Getter
@Setter
@Accessors(chain = true)
public class SalesSummaryBo {

    private String transactionId;
    private String approveCode;
    private String invoiceNumber;
    private String cpgCode;

    private String merchantCode;
    private String outletCode;

    private Integer countVoucher;
    private BigDecimal amount;

    private String transactionDate;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }


}
