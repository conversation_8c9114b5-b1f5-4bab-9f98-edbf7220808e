package com.gtech.gvcore.service.report.impl.support;

import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.report.impl.param.RedemptionQueryData;

/**
 * @ClassName RedemptionBaseImpl
 * @Description RedemptionBaseImpl
 * <AUTHOR>
 * @Date 2022/12/12 16:50
 * @Version V1.0
 **/
public class RedemptionHelper {

    private RedemptionHelper() {
    }

    public static RedemptionQueryData getQueryData(CreateReportRequest reportRequest) {

        RedemptionQueryData redemptionQueryData = new RedemptionQueryData();

        //transaction
        redemptionQueryData.setTransactionDateEnd(reportRequest.getTransactionDateEnd());
        redemptionQueryData.setTransactionDateStart(reportRequest.getTransactionDateStart());
        redemptionQueryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_REDEEM.getCode());

        // issuer merchant outlet
        redemptionQueryData.setMerchantCodeList(reportRequest.getMerchantCodes());
        redemptionQueryData.setOutletCodeList(reportRequest.getOutletCodes());

        //cpg
        redemptionQueryData.setCpgCodeList(reportRequest.getCpgCodes());

        //voucher code
        redemptionQueryData.setVoucherCodeNumStart(reportRequest.getVoucherCodeNumStart());
        redemptionQueryData.setVoucherCodeNumEnd(reportRequest.getVoucherCodeNumEnd());

        //invoice number
        redemptionQueryData.setInvoiceNumber(reportRequest.getInvoiceNo());

        return redemptionQueryData;
    }

}
