package com.gtech.gvcore.service.report.impl.support.expiry;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.ExpiryGvBo;
import com.gtech.gvcore.service.report.impl.param.ExpiryGvMigrationDataQueryData;
import com.gtech.gvcore.service.report.impl.param.ExpiryGvQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName ExpiryGvBaseImpl
 * @Description 过期时间报表父类
 * <AUTHOR>
 * @Date 2022/9/27 14:03
 * @Version V1.0
 **/
public abstract class ExpiryGvBaseImpl<T> extends ReportSupport implements BusinessReport<ExpiryGvQueryData, T> {

    @Autowired
    private ExpiryMigrationDataSupport expiryMigrationDataSupport;

    @Override
    public ExpiryGvQueryData builderQueryParam(final CreateReportRequest reportParam) {

        ExpiryGvQueryData expiryGvDetailQueryData = new ExpiryGvQueryData();

        expiryGvDetailQueryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        expiryGvDetailQueryData.setOutletCodeList(reportParam.getOutletCodes());
        expiryGvDetailQueryData.setCpgCodeList(reportParam.getCpgCodes());
        expiryGvDetailQueryData.setMerchantCodeList(reportParam.getMerchantCodes());

        if (CollectionUtils.isNotEmpty(reportParam.getCustomerCodes())) {
            expiryGvDetailQueryData.setCustomerCode(reportParam.getCustomerCodes().get(0));
        }

        expiryGvDetailQueryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        expiryGvDetailQueryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());


        expiryGvDetailQueryData.setSelectMigrationDataFlag(reportParam.getSelectMigrationDataFlag());

        return expiryGvDetailQueryData;
    }

    protected List<String> getVoucherCodeList(ExpiryGvQueryData queryData) {

        final ExpiryGvMigrationDataQueryData migrationDataPageData = BeanCopyUtils.jsonCopyBean(queryData, ExpiryGvMigrationDataQueryData.class);

        final List<String> list = Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(this.reportBusinessMapper::selectExpiryGv, queryData))
                // filter gift voucher cancel redeem
                .map(e -> e.stream()
                        .filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType()))
                        .map(ExpiryGvBo::getVoucherCode)
                        .collect(Collectors.toList())
                ).orElse(Collections.emptyList());

        if (!queryData.isSelectMigrationDataFlag()) return list;

        final List<String> migrationDataVoucherList = expiryMigrationDataSupport.queryMigrationData(migrationDataPageData);

        return Stream.concat(list.stream(), migrationDataVoucherList.stream()).distinct().collect(Collectors.toList());

    }

}
