package com.gtech.gvcore.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto;
import com.gtech.gvcore.dao.dto.IssueHandlingTypeRemarkDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class IssueHandlingDetailsServiceImpl implements IssueHandlingDetailsService {

	@Autowired
	private IssueHandlingDetailsMapper issueHandlingDetailsMapper;

	@Transactional(rollbackFor = Exception.class)
	@Override
	public int insertList(List<IssueHandlingDetails> detailList) {
		return issueHandlingDetailsMapper.insertList(detailList);
	}

	@Override
	public List<IssueHandlingDetails> queryByIssueHandlingCode(String issueHandlingCode, Integer processStatus,
	                                                           Integer pageSize, Long startId) {

		IssueHandlingDetailsDto dto = new IssueHandlingDetailsDto();
		dto.setIssueHandlingCode(issueHandlingCode);
		dto.setProcessStatus(processStatus);
		dto.setPageSize(pageSize);
		dto.setStartId(startId);
		List<IssueHandlingDetails> list = issueHandlingDetailsMapper.queryByIssueHandlingCode(dto);
		if(CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		return list;
	}

	@Override
	public PageResult<IssueHandlingDetails> queryByIssueHandlingCodeByCode(String issueHandlingCode, PageParam page) {
		PageHelper.startPage(page.getPageNum(), page.getPageSize());
		Example example = new Example(IssueHandlingDetails.class);
		example.createCriteria().andEqualTo(IssueHandlingDetails.C_ISSUE_HANDLING_CODE, issueHandlingCode);
		List<IssueHandlingDetails> issueHandlingDetails = issueHandlingDetailsMapper.selectByCondition(example);
		PageInfo<IssueHandlingDetails> of = PageInfo.of(issueHandlingDetails);
		return PageResult.ok(of.getList(), of.getTotal());
	}

	@Override
	public int updateByProcess(List<IssueHandlingDetails> details, String updateUser) {

		IssueHandlingDetailsDto dto = new IssueHandlingDetailsDto();
		dto.setProcessStatus(IssueHandlingProcessStatusEnum.CREATED.code());
		dto.setUpdateUser(updateUser);
		dto.setUpdateTime(new Date());
		dto.setDetailList(details);
		return issueHandlingDetailsMapper.updateByProcess(dto);
	}

	@Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteByIssueHandlingCode(String issueHandlingCode) {
        
        if(StringUtils.isBlank(issueHandlingCode)) {
            return 0;
        }
        
        IssueHandlingDetails detail = new IssueHandlingDetails();
        detail.setIssueHandlingCode(issueHandlingCode);
        return issueHandlingDetailsMapper.delete(detail);
    }

    @Override
    public int countByIssueHandlingCode(String issueHandlingCode, Integer processStatus) {

        if (StringUtils.isBlank(issueHandlingCode)) {
            return 0;
        }

        IssueHandlingDetails detail = new IssueHandlingDetails();
        detail.setIssueHandlingCode(issueHandlingCode);
        detail.setProcessStatus(processStatus);
        return issueHandlingDetailsMapper.selectCount(detail);
    }

	public Map<String, String> queryRemarkByVoucherCodeAndIssueType(List<String> codes, IssueHandlingTypeEnum issueType) {

		if (CollectionUtils.isEmpty(codes)) return new HashMap<>();

		if (null == issueType) return new HashMap<>();

		return ListUtils.partition(codes, 1000).stream()
				.map(e -> selectRemarkByVoucherCodeAndIssueType(e, issueType))
				.reduce((a, b) -> {
					a.putAll(b);
					return a;
				}).orElse(new HashMap<>());

	}

	private Map<String, String> selectRemarkByVoucherCodeAndIssueType(List<String> codes, IssueHandlingTypeEnum issueType) {

		return Optional.of(issueHandlingDetailsMapper.selectRemarkByVoucherCodeAndIssueType(codes, issueType.code()))
				.map(list -> list.stream().map(e -> e.setRemark(ConvertUtils.toString(e.getRemark(), StringUtils.EMPTY))).collect(Collectors.toMap(IssueHandlingTypeRemarkDto::getVoucherCode, IssueHandlingTypeRemarkDto::getRemark, (a, b) -> b)))
				.orElse(new HashMap<>());
	}

	@Override
	public Map<String, String> queryReIssuerSourceVoucherCode(List<String> voucherCode) {

		//is empty
		if (CollectionUtils.isEmpty(voucherCode)) return new HashMap<>();

		//find param
		Example example = new Example(IssueHandlingDetails.class);
		example.createCriteria().andIn(IssueHandlingDetails.C_NEW_VOUCHER_CODE, voucherCode)
				.andEqualTo(IssueHandlingDetails.C_ISSUE_TYPE, IssueHandlingTypeEnum.BULK_REISSUE.code());

		//find properties
		example.selectProperties(IssueHandlingDetails.C_NEW_VOUCHER_CODE, IssueHandlingDetails.C_VOUCHER_CODE);

		//find
		List<IssueHandlingDetails> list = issueHandlingDetailsMapper.selectByCondition(example);
		if (CollectionUtils.isEmpty(list)) return new HashMap<>();

		//convert
		return list.stream().collect(Collectors.toMap(IssueHandlingDetails::getNewVoucherCode, IssueHandlingDetails::getVoucherCode, (a, b) -> b));
	}

	@Override
	public List<IssueHandlingDetails> exportIssueHandlingDetails(IssueHandlingTypeEnum issueType, String issueHandlingCode, Date startDate, Date endDate, Long startVoucherNumber, Long endVoucherNumber, String customerEmail, List<String> voucherCodes,Integer pageSize,Integer pageNum) {

		try (Page<Object> page = PageMethod.startPage(pageNum, pageSize)){
			Example example = new Example(IssueHandlingDetails.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(IssueHandlingDetails.C_ISSUE_TYPE, issueType.code());
			criteria.andEqualTo("processStatus", IssueHandlingProcessStatusEnum.SUCCESS.code());
			if (StringUtils.isNotBlank(issueHandlingCode)) {
				criteria.andEqualTo(IssueHandlingDetails.C_ISSUE_HANDLING_CODE, issueHandlingCode);
			}
			if (null != startDate) {
				criteria.andGreaterThanOrEqualTo(IssueHandlingDetails.C_CREATE_TIME, startDate);
			}
			if (null != endDate) {
				criteria.andLessThanOrEqualTo(IssueHandlingDetails.C_CREATE_TIME, endDate);
			}

			if (null != startVoucherNumber) {
				criteria.andGreaterThanOrEqualTo(IssueHandlingDetails.C_VOUCHER_CODE, startVoucherNumber);
			}
			if (null != endVoucherNumber) {
				criteria.andLessThanOrEqualTo(IssueHandlingDetails.C_VOUCHER_CODE, endVoucherNumber);
			}
			if (StringUtils.isNotBlank(customerEmail)) {
				criteria.andEqualTo(IssueHandlingDetails.C_RECEIVER_EMAIL, customerEmail);
			}
			if (CollectionUtils.isNotEmpty(voucherCodes)) {
				criteria.andIn(IssueHandlingDetails.C_VOUCHER_CODE, voucherCodes);
			}


			return issueHandlingDetailsMapper.selectByCondition(example);
		}

	}
}
