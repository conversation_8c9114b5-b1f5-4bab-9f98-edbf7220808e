package com.gtech.gvcore.service.report.impl.support;

import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.CancelSalesBo;
import com.gtech.gvcore.service.report.impl.param.CancelSalesQueryData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName CancelSalesBaseImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/4/14 17:49
 * @Version V1.0
 **/
public abstract class CancelSalesBaseImpl<T> extends ReportSupport implements BusinessReport<CancelSalesQueryData, T> {

    @Autowired
    private CustomerOrderMapper customerOrderMapper;

    @Override
    public CancelSalesQueryData builderQueryParam(CreateReportRequest reportParam) {

        CancelSalesQueryData param = new CancelSalesQueryData();

        //transaction
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        param.setTransactionDateStart(reportParam.getTransactionDateStart());

        // issuer merchant outlet
        param.setIssuerCode(reportParam.getIssuerCode());
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        param.setCpgCodeList(reportParam.getCpgCodes());

        //voucher code
        param.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        param.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        //invoice number
        param.setInvoiceNumber(reportParam.getInvoiceNo());

        //effective date
        param.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        param.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        //customer
        param.setCustomerType(reportParam.getCustomerType());
        param.setCustomerCodeList(reportParam.getCustomerCodes());

        param.setTransactionIdList(this.getTransactionIdByPurchaseOrder(reportParam));

        return param;

    }


    /**
     * 根据采购单号查询交易单号
     * @param reportParam
     * @return
     */
    private List<String> getTransactionIdByPurchaseOrder(final CreateReportRequest reportParam) {

        // empty
        if (StringUtils.isBlank(reportParam.getPurchaseOrderNo())) return Collections.emptyList();

        // select
        List<CustomerOrder> customerOrderList = customerOrderMapper.selectByCondition(Example.builder(CustomerOrder.class)
                .where(Sqls.custom().andEqualTo(CustomerOrder.C_PURCHASE_ORDER_NO, reportParam.getPurchaseOrderNo()))
                .build());

        // filter
        return Optional.ofNullable(customerOrderList)
                .map(e -> e.stream().map(CustomerOrder::getCustomerOrderCode).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    public List<CancelSalesBo> getBoList(CancelSalesQueryData paramBean) {

        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectCancelSales, paramBean))
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_CANCEL_SELL.equalsCode(s.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

}
