package com.gtech.gvcore.service.report;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.schedulerreport.CreateSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.DeleteSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.QuerySchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.UpdateSchedulerReportRequest;
import com.gtech.gvcore.common.request.schedulerreport.UpdateSchedulerReportsStatusRequest;
import com.gtech.gvcore.common.response.schedulerreport.QuerySchedulerReportResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/31 18:03
 */


public interface SchedulerReportService {

    Result<String> createSchedulerReport(CreateSchedulerReportRequest createSchedulerReportRequest);

    Result<Void> updateSchedulerReport(UpdateSchedulerReportRequest updateSchedulerReportRequest);

    PageResult<QuerySchedulerReportResponse> querySchedulerReport(QuerySchedulerReportRequest querySchedulerReportRequest);

    Result<Void> updateSchedulerReportStatus(UpdateSchedulerReportsStatusRequest updateStatusSchedulerReportRequest);

    Result<Void> deleteSchedulerReport(DeleteSchedulerReportRequest deleteSchedulerReportRequest);

}
