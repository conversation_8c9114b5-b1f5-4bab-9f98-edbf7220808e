package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName AgingReportQueryParam
 * @Description aging param
 * <AUTHOR>
 * @Date 2022/10/26 17:11
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class AgingReportQueryParamData extends TransactionDataPageParam implements ReportQueryParam {

    private List<String> issuerCodeList;

    private List<String> outletCodeList;

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    private List<String> customerCodeList;

    private List<String> cpgCodeList;

    @Getter
    @Setter
    public static class AgingReportRedeemQueryParam extends TransactionDataPageParam {

        private List<String> voucherCodeList;

        public AgingReportRedeemQueryParam(List<String> voucherCodeList) {
            this.voucherCodeList = voucherCodeList;
        }

    }

}
