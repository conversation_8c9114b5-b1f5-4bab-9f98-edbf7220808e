package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName LatestGvStatusEnhancedQueryData
 * @Description Latest Gift Voucher Status Enhanced Query Parameters
 * <AUTHOR>
 * @Date 2025-06-17
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLatestStatusQueryData extends PageParam implements ReportQueryParam {

    /**
     * Gift Card Program Group codes
     */
    private List<String> cpgCodeList;

    /**
     * Gift Card status list
     */
    private List<String> cardStatusList;

    /**
     * Gift Card number list from uploaded file
     */
    private List<String> cardNumberList;

    /**
     * Start card number for range query
     */
    private String cardNumberStart;

    /**
     * End card number for range query
     */
    private String cardNumberEnd;

    /**
     * Issuer code filter
     */
    private String issuerCode;

    /**
     * Merchant code filter
     */
    private String merchantCode;

    /**
     * Outlet code filter
     */
    private String outletCode;
}
