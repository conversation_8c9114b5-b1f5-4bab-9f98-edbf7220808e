package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderReceiver;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.impl.bean.BulkOrderSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.BulkOrderReportBasicDataContext;
import com.gtech.gvcore.service.report.impl.param.BulkOrderQueryData;
import com.gtech.gvcore.service.report.impl.support.bulk.BulkOrderReportBasicDataContextFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description:
 */
@Service
public class BulkOrderSummaryImpl extends ReportSupport
        implements BusinessReport<BulkOrderQueryData, BulkOrderSummaryBean>, ReportProportionDataFunction, PollReport {

    @Autowired private TransactionDataMapper transactionDataMapper;
    @Autowired private BulkOrderReportBasicDataContextFactory bulkOrderReportBasicDataContextFactory;
    @Autowired private CustomerOrderMapper customerOrderMapper;
    @Autowired private CustomerOrderDetailsMapper customerOrderDetailsMapper;
    @Autowired private CustomerOrderService customerOrderService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.BULK_ORDER_SUMMARY_REPORT;
    }

    @Override
    public BulkOrderQueryData builderQueryParam(final CreateReportRequest reportParam) {

        // 将 merchantCodes 条件转换为 outletCodes 条件
        ReportParamConvertHelper.convertQueryDateMerchantCodeToOutletCode(reportParam);

        final BulkOrderQueryData queryData = new BulkOrderQueryData();
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setInvoiceNo(reportParam.getInvoiceNo());
        queryData.setCustomerType(reportParam.getCustomerType());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        queryData.setPurchaseOrderNumber(reportParam.getPurchaseOrderNo());
        queryData.setOrderStatusList(reportParam.getOrderStatuses());

        return queryData;
    }

    @Override
    public List<BulkOrderSummaryBean> getExportData(final BulkOrderQueryData queryData) {

        final List<CustomerOrder> customerOrderList = this.customerOrderSummary(queryData);

        if (CollectionUtils.isEmpty(customerOrderList)) return Collections.emptyList();

        final BulkOrderReportBasicDataContext basicDataContext = this.bulkOrderReportBasicDataContextFactory.buildBasicDataContext(customerOrderList);

        return customerOrderList.stream().map(order -> this.customerOrder2BulkOrderSummary(order, basicDataContext)).collect(Collectors.toList());
    }

    private BulkOrderSummaryBean customerOrder2BulkOrderSummary(final CustomerOrder order, final BulkOrderReportBasicDataContext basicDataContext) {

        final BulkOrderSummaryBean summary = new BulkOrderSummaryBean();

        Merchant merchant = null;
        final Outlet outlet = basicDataContext.getOutlet(order.getOutletCode());
        if (null != outlet) {
            summary.setMerchantOutlet(outlet.getOutletName());
            summary.setMerchantOutletCode(outlet.getBusinessOutletCode());
            merchant = basicDataContext.getMerchant(outlet.getMerchantCode());
        }

        if (null != merchant) {
            summary.setMerchant(merchant.getMerchantName());
        }

        summary.setCustomerName(basicDataContext.getCustomerName(order.getCustomerCode()));
        summary.setCompanyName(basicDataContext.getCompanyName(order.getCustomerCode()));
        summary.setVoucherType(basicDataContext.getVoucherTypeText(order.getMopCode()));
        summary.setPoNumber(order.getPurchaseOrderNo());
        summary.setPoDate(DateUtil.format(order.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));
        summary.setStatus(basicDataContext.getOrderStatusText(order.getStatus()));
        summary.setPoValue(super.toAmount(String.valueOf(order.getVoucherAmount().longValue())));
        summary.setDiscountType(basicDataContext.getDiscountTypeText(order.getDiscountType()));
        summary.setDiscountAmount(super.toAmount(order.getAmount()));
        summary.setTotalNetAmount(super.toAmount(order.getVoucherAmount().subtract(order.getAmount()).setScale(0, RoundingMode.UP)));
        //如果是百分比折扣，显示折扣值，否则显示0
        if (ProductCategoryDiscountTypeEnum.PERCENTAGE.equalsCode(order.getDiscountType())) {
            summary.setDiscountPercentage(order.getDiscount().setScale(1)+ PERCENT);
        } else if (ProductCategoryDiscountTypeEnum.AMOUNT.equalsCode(order.getDiscountType())) {
            // AMOUNT 类型折扣值固定为 0
            summary.setDiscountPercentage("0" + PERCENT);
        }else{
            summary.setDiscountPercentage(EMPTY_PROPORTION);
        }
        summary.setInvoiceNumber(order.getInvoiceNo());
        summary.setPaymentMode(basicDataContext.getPaymentMode(order.getMeansOfPaymentCode()));
        summary.setActivatingUserLogin(basicDataContext.getActivatingUserLogin(order.getCreateUser()));
        summary.setTotalCount(Integer.toString(order.getVoucherNum()));
        summary.setSuccessCount(summary.getTotalCount());
        summary.setFailedCount("0");

        final CustomerOrderStatusEnum customerOrderStatus = CustomerOrderStatusEnum.valueOfCode(order.getStatus());
        final List<TransactionData> transactionDataList = this.selectTransactionByCustomerOrderCode(order.getCustomerOrderCode());

        // 统计激活 卡券最新的一条跟 取消激活 or 激活 相关的交易记录
        Collection<TransactionData> activateAndCancelTransaction = transactionDataList.stream()
                .filter(transactionData -> TransactionTypeEnum.GIFT_CARD_ACTIVATE.equalsCode(transactionData.getTransactionType()) || TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE.equalsCode(transactionData.getTransactionType()))
                .collect(Collectors.toMap(TransactionData::getVoucherCode, Function.identity(), (v1, v2) -> v1.getCreateTime().compareTo(v2.getCreateTime()) > 0 ? v1 : v2))
                .values();
        if (CustomerOrderStatusEnum.RELEASE.beforeOrEquals(customerOrderStatus)) {

            //根据卡券最新激活交易记录统计 激活金额和数量
            List<TransactionData> vouchers = activateAndCancelTransaction.stream()
                    .filter(transactionData -> TransactionTypeEnum.GIFT_CARD_ACTIVATE.equalsCode(transactionData.getTransactionType()))
                    .collect(Collectors.toList());

            BigDecimal activationAmount = vouchers.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1);
            summary.setActivationAmount(super.toAmount(activationAmount));

            int size = vouchers.size();
            summary.setActivationCount(size == 0?"0":String.valueOf(size));

        }else {
            summary.setActivationAmount("0");
            summary.setActivationCount("0");
        }

        //根据卡券最新取消激活交易记录统计 取消激活金额和数量
        List<TransactionData> vouchers = activateAndCancelTransaction.stream()
                .filter(transactionData -> TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE.equalsCode(transactionData.getTransactionType()))
                .collect(Collectors.toList());

        BigDecimal cancelActivationAmount = vouchers.stream().map(TransactionData::getDenomination).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1);
        summary.setCancelActivationAmount(super.toAmount(cancelActivationAmount));

        int size = vouchers.size();
        summary.setCancelActivationCount(size == 0?"0":String.valueOf(size));

        summary.setSkippedCount("0");

        final CustomerOrderReceiver customerOrderReceiver = this.customerOrderService.getOrderReceiver(order.getCustomerOrderCode());
        summary.setNotes(null == customerOrderReceiver ? null : customerOrderReceiver.getCustomerRemarks());

        return summary;
    }

    public List<CustomerOrder> customerOrderSummary(final BulkOrderQueryData bulkOrderReportCondition) {

        final Example condition = this.buildCustomerOrderSummaryCondition(bulkOrderReportCondition);
        if (null == condition) return Collections.emptyList();

        return this.customerOrderMapper.selectByExampleAndRowBounds(condition, GvPageHelper.getRowBounds(bulkOrderReportCondition));
    }

    /**
     * 组合 CustomerOrderSummary 报表的查询条件
     *
     * @param bulkOrderReportCondition 查询条件
     * <AUTHOR>
     * @date 2022/7/11 15:19
     * @since 1.0.0
     */
    private Example buildCustomerOrderSummaryCondition(final BulkOrderQueryData bulkOrderReportCondition) {

        // cpgCode 在 gv_customer_order 中不存在,需要先拿 cpgCode 在 gv_customer_order_details 查询出 customer_order_code 集
        if (!CollectionUtils.isEmpty(bulkOrderReportCondition.getCpgCodeList())) {
            final List<String> customerOrderCodeList = this.customerOrderDetailsMapper.queryOrderCodesByCpgCodeList(bulkOrderReportCondition.getCpgCodeList());
            if (CollectionUtils.isEmpty(customerOrderCodeList)) {
                return null;
            }
            bulkOrderReportCondition.setCustomerOrderCodeList(customerOrderCodeList);
        }

        final Example condition = new Example(CustomerOrder.class);
        condition.createCriteria()
                .andIn(CustomerOrder.C_ISSUER_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getIssuerCodeList(), null))
                .andIn(CustomerOrder.C_OUTLET_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getOutletCodeList(), null))
                .andEqualTo(CustomerOrder.C_INVOICE_NO, GvConvertUtils.toString(bulkOrderReportCondition.getInvoiceNo(), null))
                .andEqualTo(CustomerOrder.C_CUSTOMER_TYPE, GvConvertUtils.toString(bulkOrderReportCondition.getCustomerType(), null))
                .andIn(CustomerOrder.C_CUSTOMER_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getCustomerCodeList(), null))
                .andEqualTo(CustomerOrder.C_PURCHASE_ORDER_NO, GvConvertUtils.toString(bulkOrderReportCondition.getPurchaseOrderNumber(), null))
                .andIn(CustomerOrder.C_STATUS, GvConvertUtils.toCollection(bulkOrderReportCondition.getOrderStatusList(), null))
                .andIn(CustomerOrder.C_CUSTOMER_ORDER_CODE, GvConvertUtils.toCollection(bulkOrderReportCondition.getCustomerOrderCodeList(), null))
                .andGreaterThanOrEqualTo(CustomerOrder.C_CREATE_TIME, bulkOrderReportCondition.getTransactionDateStart())
                .andLessThanOrEqualTo(CustomerOrder.C_CREATE_TIME, bulkOrderReportCondition.getTransactionDateEnd());

        return condition;
    }

    public List<TransactionData> selectTransactionByCustomerOrderCode (final String customerOrderCode) {

        Example example = new Example(TransactionData.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TransactionData.C_TRANSACTION_ID, customerOrderCode);

        return this.transactionDataMapper.selectByCondition(example);
    }

}
