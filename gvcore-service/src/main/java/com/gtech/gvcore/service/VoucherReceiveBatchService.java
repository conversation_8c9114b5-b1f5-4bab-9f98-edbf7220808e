package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveBatchRequest;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveBatchResponse;

import java.util.List;

public interface VoucherReceiveBatchService {

	List<VoucherReceiveBatchResponse> queryReceiveBatchList(QueryVoucherReceiveBatchRequest request);

	int saveReceiveBatch(List<VoucherReceiveBatchRequest> receiveBatchList);

	void updateReceivedNum(String voucherReceiveBatchCode, int count);
}
