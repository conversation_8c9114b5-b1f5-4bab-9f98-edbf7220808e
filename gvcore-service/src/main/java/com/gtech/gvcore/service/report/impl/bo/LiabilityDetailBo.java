package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.ConvertUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @ClassName LiabilityDetailBo
 * @Description 责任明细报表业务模型
 * <AUTHOR>
 * @Date 2022/7/12 19:22
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilityDetailBo {

    /**
     * Issuer code.
     */
    private String issuerCode;

    /**
     * Voucher code.
     */
    private String voucherCode;

    /**
     * Cpg code.
     */
    private String cpgCode;

    /**
     * Denomination.
     */
    private BigDecimal denomination;

    public String getConvertIssuerCode() {

        return ConvertUtils.toString(issuerCode, StringUtils.EMPTY);
    }

    public String getConvertVoucherCode() {

        return ConvertUtils.toString(voucherCode, StringUtils.EMPTY);
    }


}
