package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 16:04
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class BulkOrderSummaryBean {

    /**
     * Merchant name
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Merchant Outlet name
     */
    @ExcelProperty(value = "Merchant Outlet")
    private String merchantOutlet;

    /**
     * Merchant Outlet code(非系统唯一值,指用户在Create Merchant Outlet 页面输入的Outlet Code),对应 gv_outlet表中business_outlet_code
     */
    @ExcelProperty(value = "Merchant Outlet Code")
    private String merchantOutletCode;

    /**
     * 公司名称.指用户在 Create Customer 录入的 Company Name(页面为Company Name,落库字段为customer_name),数据在 gc_customer 表 customer_name
     */
    @ExcelProperty(value = "Client Name")
    private String customerName;

    /**
     * 公司名称.指用户在 Create Customer 录入的 Corporate(页面为Corporate,落库字段为company_name), ,数据在 gc_customer 表 company_name
     */
    @ExcelProperty(value = "Company Name")
    private String companyName;

    /**
     * 券类型,其值为 gv_customer_order 表中 mop_code(VCR | VCE),描述信息从 dd_lang 获取,值包括:Physical Vouchers | Digital Vouchers
     */
    @ExcelProperty(value = "Voucher Type")
    private String voucherType;

    /**
     * 订单号. gv_customer_order 表 purchase_order_no
     */
    @ExcelProperty(value = "PO Number")
    private String poNumber;

    /**
     * 订单创建时间
     */
    @ExcelProperty(value = "PO Date")
    private String poDate;

    /**
     * 订单状态.其值为 gv_customer_order 表中 status,描述信息从 dd_lang 获取.DD_CODE = 'CUSTOMER_ORDER_STATUS'
     */
    @ExcelProperty(value = "Status")
    private String status;

    /**
     * 全部 voucher amount(面额) 和
     */
    @ReportAmountValue
    @ExcelProperty(value = "PO Value", converter = ExportExcelNumberConverter.class)
    private String poValue;

    /**
     * 折扣类型,描述值取dd_lang,DD_CODE = 'product_category_discount_type'
     */
    @ExcelProperty(value = "Discount Type")
    private String discountType;

    /**
     * 订单折扣总比例金额,若固定金额优惠,显示固定金额.若比例金额优惠,为订单总面额*比例
     */
    @ReportAmountValue
    @ExcelProperty(value = "Discount Amount", converter = ExportExcelNumberConverter.class)
    private String discountAmount;

    /**
     * 折扣百分比, gv_customer_order 表 discount.非百分比折扣固定为0 仅当 discountType 为 percentage 时表示折扣百分比.
     */
    @ExcelProperty(value = "Discount Percentage")
    private String discountPercentage;

    /**
     * 发票号码.gv_customer_order 表 invoice_no
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * Mop 名, 通过 gv_customer_order 表 means_of_payment_code 从 gv_means_of_payment 表 获取 Mop name
     */
    @ExcelProperty(value = "Payment Mode")
    private String paymentMode;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "Activating  User Login")
    private String activatingUserLogin;

    /**
     * 该订单包含的voucher总数
     */
    @ExcelProperty(value = "Total Count", converter = ExportExcelNumberConverter.class)
    private String totalCount;

    /**
     * gv_customer_order.status 当订单Release后,从 gv_transaction_data 获取 [激活过的] 券总面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Activation Amount", converter = ExportExcelNumberConverter.class)
    private String activationAmount;

    /**
     * 该字段的查询不考虑订单本身的状态,而是去查询 gv_transaction_data ,统计被 [cancel过的] 券的总面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Cancel Activation Amount", converter = ExportExcelNumberConverter.class)
    private String cancelActivationAmount;

    /**
     * 激活过的券数量 gv_customer_order.status 当订单Release后,从 gv_transaction_data 获取激活过的券总数量
     */
    @ExcelProperty(value = "Activation Count", converter = ExportExcelNumberConverter.class)
    private String activationCount;

    /**
     * 根据 gv_customer_order.voucher_batch_code 获取 gv_voucher_batch.status. 如果为成功,填充该字段,值为该订单的券总数
     */
    @ExcelProperty(value = "Success Count", converter = ExportExcelNumberConverter.class)
    private String successCount;

    /**
     * 根据 gv_customer_order.voucher_batch_code 获取 gv_voucher_batch.status. 如果为失败,填充该字段,值为该订单的券总数
     */
    @ExcelProperty(value = "Failed Count", converter = ExportExcelNumberConverter.class)
    private String failedCount;

    /**
     * 固定为0
     */
    @ExcelProperty(value = "Skipped Count")
    private String skippedCount;

    /**
     * 该字段的查询不考虑订单本身的状态,而是去查询 gv_transaction_data ,统计被 cancel 过的券的总数量
     */
    @ExcelProperty(value = "Cancel Activation Count", converter = ExportExcelNumberConverter.class)
    private String cancelActivationCount;

    /**
     * Total Net Amount = PO Amount - Discount Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Net Amount", converter = ExportExcelNumberConverter.class)
    private String totalNetAmount;

    /**
     * 订单提交时的note,gv_customer_order_receiver 表中 customer_remarks,使用customer_order_code 关联
     */
    @ExcelProperty(value = "Notes")
    private String notes;

}
