package com.gtech.gvcore.service.report.impl.support;

import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.GcSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcSalesQueryData;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * @ClassName SalesBaseImpl
 * @Description sales report base impl
 * <AUTHOR>
 * @Date 2023/3/22 15:19
 * @Version V1.0
 **/
public abstract class GcSalesBaseImpl<T> extends ReportSupport implements BusinessReport<GcSalesQueryData, T> {

    @Override
    public final GcSalesQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcSalesQueryData param = new GcSalesQueryData();

        param.setIssuerCode(reportParam.getIssuerCode());
        param.setMerchantCodeList(reportParam.getMerchantCodes());
        param.setOutletCodeList(reportParam.getOutletCodes());

        param.setTransactionDateStart(reportParam.getTransactionDateStart());
        param.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        param.setCpgCodeList(reportParam.getCpgCodes());
        param.setInvoiceNumber(reportParam.getInvoiceNo());
        if (reportParam.getVoucherCode() != null) {
            param.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().trim().split(",")));
        }
        addParam(param, reportParam);

        return param;
    }

    protected abstract void addParam(GcSalesQueryData param, CreateReportRequest reportParam);

    /**
     * find bo list
     *
     * @param param
     * @return
     */
    protected final List<GcSalesBo> getBoList(GcSalesQueryData param) {

        // 直接调用方法获取交易列表
        Collection<GcSalesBo> transactions = PollPageHelper.pollGroupNewTransactionByCodeSelect(gcReportBusinessMapper::gcSalesReport, param);
        if (CollectionUtils.isEmpty(transactions)) return Collections.emptyList();

        // 如果transactions为null，返回一个空列表；否则，过滤并返回符合条件的列表
        return CollectionUtils.isEmpty(transactions) ? Collections.emptyList() : new ArrayList<>(transactions);
    }

    @Override
    public final List<T> getExportData(GcSalesQueryData param) {

        List<GcSalesBo> boList = this.getBoList(param);
        if (CollectionUtils.isEmpty(boList)) return Collections.emptyList();
        return getExportData(boList);
    }

    protected abstract List<T> getExportData(List<GcSalesBo> boList);
}
