package com.gtech.gvcore.service.impl;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gtech.gvcore.dao.mapper.ExtendLogMapper;
import com.gtech.gvcore.dao.model.ExtendLog;
import com.gtech.gvcore.service.ExtendApiService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ExtendApiServiceImpl implements ExtendApiService {
	
	@Autowired
	private ExtendLogMapper extendLogMapper;

	@Override
	public void saveExtendApiList(List<ExtendLog> extendLogList) {
		if (CollectionUtils.isEmpty(extendLogList)) {
			return;
		}
		for (ExtendLog extendLog : extendLogList) {
			try {
				extendLogMapper.insert(extendLog);
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
	}

}
