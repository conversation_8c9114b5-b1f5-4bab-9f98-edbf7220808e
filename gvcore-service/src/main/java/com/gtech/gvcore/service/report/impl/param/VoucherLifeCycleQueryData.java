package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName VoucherLifeCycleQueryData
 * @Description
 * <AUTHOR>
 * @Date 2023/1/9 18:55
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherLifeCycleQueryData extends PageParam implements ReportQueryParam {

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    private String voucherCode;

    private List<String> issueCodeList;

    private Long voucherCodeIndex;
}
