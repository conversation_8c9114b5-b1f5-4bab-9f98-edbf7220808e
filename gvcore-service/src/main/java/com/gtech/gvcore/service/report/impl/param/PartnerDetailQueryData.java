package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName PartnerSalesDetailQueryData
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 14:43
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class PartnerDetailQueryData extends TransactionDataPageParam implements ReportQueryParam {

    public static final PartnerDetailQueryData EMPTY_PARAM = new PartnerDetailQueryData();

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    //cpg code list
    private List<String> cpgCodeList;

    private List<String> customerCodeList;

    private String transactionCorporateName;

    //invoice number 发票编号
    private String invoiceNumber;

    //outlet code
    private String outletCode;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;
}
