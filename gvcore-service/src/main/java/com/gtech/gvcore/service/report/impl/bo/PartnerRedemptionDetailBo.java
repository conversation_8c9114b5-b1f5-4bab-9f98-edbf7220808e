package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023-01-06 11:36
 */

@Setter
@Getter
@Accessors(chain = true)
public class PartnerRedemptionDetailBo {

    private String cardNumber;

    private String bookletNumber;

    private String sbuCompanyName;

    private String merchant;

    private String outlet;

    private String outletCode;

    // 留空
    private String outletGroup;

    private String outletType;

    private String region;

    private String corporateName;

    private String departmentDivisionBranch;

    private String customerFullName;

    private String firstName;

    private String lastName;

    private String email;

    private String transactionDate;

    private String transactionTime;

    private String invoiceDate;

    private String invoiceTime;

    private String posName;

    private String initiatedBy;

    private String voucherProgramGroup;

    private String voucherProgramGroupType;

    private String transactionType = "GIFT VOUCHER REDEEM";

    private String baseCurrency;

    private String amount;

    private String transactingMerchantCurrency;

    private String invoiceNumber;

    private String responseMessage;

    private String denomination;

    private String expiryDate;

    private String issuanceYear;

    private String referenceNumber;

    private String originalCardNumberBeforeReissue;

    private String cardEntryMode;

    private String batchNumber;

    private String approvalCode;

    private String mopCode;
}
