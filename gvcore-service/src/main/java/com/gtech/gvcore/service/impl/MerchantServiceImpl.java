package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.company.GetCompanyRequest;
import com.gtech.gvcore.common.request.merchant.CreateMerchantRequest;
import com.gtech.gvcore.common.request.merchant.DeleteMerchantRequest;
import com.gtech.gvcore.common.request.merchant.GetMerchantRequest;
import com.gtech.gvcore.common.request.merchant.QueryMerchantByCompanyCodesRequest;
import com.gtech.gvcore.common.request.merchant.QueryMerchantRequest;
import com.gtech.gvcore.common.request.merchant.UpdateMerchantRequest;
import com.gtech.gvcore.common.request.merchant.UpdateMerchantStatusRequest;
import com.gtech.gvcore.common.response.company.CompanyResponse;
import com.gtech.gvcore.common.response.merchant.MerchantResponse;
import com.gtech.gvcore.dao.mapper.MerchantMapper;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CompanyService;
import com.gtech.gvcore.service.MerchantService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/2/17 15:26
 */
@Service
public class MerchantServiceImpl implements MerchantService {


    @Autowired
    private MerchantMapper merchantMapper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private GvCodeHelper codeHelper;

    @Override
    public Result<String> createMerchant(CreateMerchantRequest param) {
        Merchant entity = BeanCopyUtils.jsonCopyBean(param, Merchant.class);
        entity.setMerchantCode(codeHelper.generateMerchantCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        try {
            merchantMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok(entity.getMerchantCode());
    }

    @Override
    public Result<Void> updateMerchant(UpdateMerchantRequest param) {
        Merchant entity = BeanCopyUtils.jsonCopyBean(param, Merchant.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Merchant.class);
        example.createCriteria()
                .andEqualTo(Merchant.C_MERCHANT_CODE, param.getMerchantCode());

        try {
            merchantMapper.updateByConditionSelective(entity, example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteMerchant(DeleteMerchantRequest param) {
        Example example = new Example(Merchant.class);
        example.createCriteria()
                .andEqualTo(Merchant.C_MERCHANT_CODE, param.getMerchantCode());
        merchantMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<MerchantResponse> queryMerchantList(QueryMerchantRequest param) {

        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        Example example = new Example(Merchant.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtil.isNotEmpty(param.getMerchantName())) {
            criteria.andLike(Merchant.C_MERCHANT_NAME, "%" + param.getMerchantName() + "%");
        }
        if (StringUtil.isNotEmpty(param.getMerchantCode())) {
            criteria.andEqualTo(Merchant.C_MERCHANT_CODE, param.getMerchantCode());
        }
        if (StringUtil.isNotEmpty(param.getCompanyCode())) {
            criteria.andEqualTo(Merchant.C_COMPANY_CODE, param.getCompanyCode());
        }
        /*
        MER-1884
        if (StringUtil.isNotEmpty(param.getIssuerCode())) {
            criteria.andEqualTo(Merchant.C_ISSUER_CODE, param.getIssuerCode());
        }*/
        if (StringUtil.isNotEmpty(param.getStatus())) {
            criteria.andEqualTo(Merchant.C_STATUS, param.getStatus());
        }
        if (CollectionUtils.isNotEmpty(param.getMerchantCodeList())) {
       	 criteria.andIn(Merchant.C_MERCHANT_CODE, param.getMerchantCodeList());
       }
        //创建时间倒序
        example.orderBy(Merchant.C_MERCHANT_NAME);

        List<Merchant> gvMerchantEntities = merchantMapper.selectByCondition(example);
        PageInfo<Merchant> info = PageInfo.of(gvMerchantEntities);
        List<MerchantResponse> merchantResponses = BeanCopyUtils.jsonCopyList(info.getList(), MerchantResponse.class);
        for (MerchantResponse response : merchantResponses) {
            GetCompanyRequest request = new GetCompanyRequest();
            request.setCompanyCode(response.getCompanyCode());
            CompanyResponse company = companyService.getCompany(request);
            if (null!= company){
                response.setCompanyName(company.getCompanyName());
            }
        }

        return new PageResult<>(merchantResponses, info.getTotal());
    }

    @Override
    public List<Merchant> queryMerchantByCompanyCodesNotPageParam(List<String> companyCodes) {

        if (CollectionUtils.isEmpty(companyCodes)) return Collections.emptyList();

        Example example = new Example(Merchant.class);
        example.createCriteria()
                .andIn(Merchant.C_COMPANY_CODE, companyCodes);

        return merchantMapper.selectByCondition(example);
    }

    @Override
    public MerchantResponse getMerchant(GetMerchantRequest param) {
        Merchant entity = BeanCopyUtils.jsonCopyBean(param, Merchant.class);
        Merchant merchant = merchantMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(merchant, MerchantResponse.class);
    }

    @Override
    public Result<Void> updateMerchantStatus(UpdateMerchantStatusRequest param) {
        Merchant entity = BeanCopyUtils.jsonCopyBean(param, Merchant.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(Merchant.class);
        example.createCriteria()
                .andEqualTo(Merchant.C_MERCHANT_CODE, param.getMerchantCode());

        merchantMapper.updateByConditionSelective(entity, example);

        return Result.ok();
    }

    @Override
    public PageResult<MerchantResponse> queryMerchantByCompanyCodes(QueryMerchantByCompanyCodesRequest request) {

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        Example example = new Example(Merchant.class);
        example.createCriteria().andIn(Merchant.C_COMPANY_CODE,request.getCompanyCodes());

        List<Merchant> gvMerchantEntities = merchantMapper.selectByCondition(example);
        PageInfo<Merchant> info = PageInfo.of(gvMerchantEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(), MerchantResponse.class), info.getTotal());
    }

    @Override
    public Map<String, Merchant> queryMerchantMapByMerchantCodeList(List<String> merchantCodeList) {

        if (CollectionUtils.isEmpty(merchantCodeList)) {
            return Collections.emptyMap();
        }

        List<Merchant> list = this.queryMerchantListByCodeList(merchantCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(Merchant::getMerchantCode, v -> v));
    }

    public List<Merchant> queryMerchantListByCodeList(List<String> merchantCodeList) {

        if (CollectionUtils.isEmpty(merchantCodeList)) return Collections.emptyList();

        Example example = new Example(Merchant.class);
        example.createCriteria().andIn(Merchant.C_MERCHANT_CODE, merchantCodeList);

        List<Merchant> list = merchantMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

	public List<Merchant> queryAllMerchant() {
		return merchantMapper.selectAll();
	}

}
