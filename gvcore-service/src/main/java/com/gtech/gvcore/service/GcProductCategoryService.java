package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.productcategory.*;
import com.gtech.gvcore.common.response.productcategory.*;

/**
 * GC商品类别Service接口
 */
public interface GcProductCategoryService {
    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<CreateProductCategoryResponse> createProductCategory(CreateProductCategoryRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<Void> updateProductCategory(UpdateProductCategoryRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<Void> updateProductCategoryStatus(UpdateProductCategoryStatusRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    PageResult<QueryProductCategoryByPageResponse> queryProductCategoryByPage(
            QueryProductCategoryByPageRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<QueryProductCategoryCpgResponse> queryProductCategoryCpg(QueryProductCategoryCpgRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    Result<Void> createOrUpdateProductCategoryCpg(CreateOrUpdateProductCategoryCpgRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月24日
     */
    Result<QueryProductCategoryDisscountResponse> queryProductCategoryDisscount(
            QueryProductCategoryDisscountRequest request);

    /**
     *
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月24日
     */
    Result<Void> createOrUpdateProductCategoryDisscount(CreateOrUpdateProductCategoryDisscountRequest request);

    DiscountInfoResponse calculateDiscountAmount(CalculateDiscountAmountRequest request);

} 