package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.domain.service.GcSalesDomainService;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class GcIssueHandlerCancelSalesService extends GcIssueHandlerValidateService implements IssueHandlerBaseService {


    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_CANCEL_SALES;
    }

    @Override
    public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkInvoiceNumberAndApprovalCode(details, getIssueHandlingType());
        checkOutletName(details);
        return details;
    }

    @Override
    public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        List<IssueHandlingDetails> validate = validate(details, issuerCode);
        List<String> successVoucherCodes = validate.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(successVoucherCodes)) {
            performAction(successVoucherCodes);
        }
        return validate;
    }


    private void performAction(List<String> voucherCodes) {
        // 更新卡券状态为销毁
        giftCardMapper.setSalesNull(voucherCodes);
    }

}


