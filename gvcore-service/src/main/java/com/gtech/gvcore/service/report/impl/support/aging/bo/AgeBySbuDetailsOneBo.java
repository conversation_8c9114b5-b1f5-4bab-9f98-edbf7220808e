package com.gtech.gvcore.service.report.impl.support.aging.bo;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.service.report.extend.ReportAmountSupport;
import com.gtech.gvcore.service.report.impl.bean.aging.AgeBySbuDetailsOneBean;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName AgeBySbuDetailsOneBo
 * @Description Age By Sbu Details One Bo
 * <AUTHOR>
 * @Date 2022/11/4 15:44
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class AgeBySbuDetailsOneBo implements ReportAmountSupport {

    private String customerCode;

    private String customerName;

    private String sbuName;

    private BigDecimal oneValue = BigDecimal.ZERO;

    private BigDecimal twoValue = BigDecimal.ZERO;

    private BigDecimal threeValue = BigDecimal.ZERO;

    private BigDecimal fourValue = BigDecimal.ZERO;

    private BigDecimal sixValue = BigDecimal.ZERO;

    public void addOneValue(BigDecimal value) {
        oneValue = this.oneValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
    }
    public void addTwoValue(BigDecimal value) {
        twoValue = this.twoValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
    }
    public void addThreeValue(BigDecimal value) {
        threeValue = this.threeValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
    }
    public void addFourValue(BigDecimal value) {
        fourValue = this.fourValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
    }
    public void addSixValue(BigDecimal value) {
        sixValue = this.sixValue.add(ConvertUtils.toBigDecimal(value, BigDecimal.ZERO));
    }

    public BigDecimal getTotal() {
        return oneValue.add(twoValue)
                .add(threeValue)
                .add(fourValue)
                .add(sixValue);
    }

    public void add(AgeBySbuDetailsOneBo bean) {

        this.addOneValue(bean.getOneValue());
        this.addTwoValue(bean.getTwoValue());
        this.addThreeValue(bean.getThreeValue());
        this.addFourValue(bean.getFourValue());
        this.addSixValue(bean.getSixValue());

    }

    public AgeBySbuDetailsOneBean bean() {

        return new AgeBySbuDetailsOneBean()
                .setCustomerName(this.getCustomerName())
                .setSbuName(this.getSbuName())
                .setOneValue(toAmount(this.getOneValue()))
                .setTwoValue(toAmount(this.getTwoValue()))
                .setThreeValue(toAmount(this.getThreeValue()))
                .setFourValue(toAmount(this.getFourValue()))
                .setSixValue(toAmount(this.getSixValue()))
                .setTotal(toAmount(this.getTotal()));
    }





}
