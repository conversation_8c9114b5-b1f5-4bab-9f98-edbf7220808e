package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName VoucherRequestAllocateReceiveBo
 * @Description VoucherRequestAllocateReceiveBo
 * <AUTHOR>
 * @Date 2023/2/10 17:11
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherRequestAllocateReceiveBo {

    // request id
    private String requestId;

    // request source no
    private String requestSource;

    // cpg code
    private String cpgCode;

    // number of voucher
    private BigDecimal numberOfVoucher;

    // voucher amount
    private BigDecimal voucherAmount;

    // create time
    private String createTime;

    // create user
    private String createUser;

    // booklet start no
    private String bookletStartNo;

    // booklet end no
    private String bookletEndNo;

    // voucher start no
    private String voucherStartNo;

    // voucher end no
    private String voucherEndNo;

    // allocate status
    private String allocateStatus;

    /**
     * get booklet number
      * @return booklet number start ~ end (number)
     */
    public String getBookletNumber() {

        // empty
        if (bookletStartNo == null || bookletEndNo == null) {
            return StringUtils.EMPTY;
        }

        // booklet number
        BigDecimal number = new BigDecimal(bookletEndNo).subtract(new BigDecimal(bookletStartNo)).add(BigDecimal.ONE);

        // booklet number start ~ end (number)
        return bookletStartNo + " ~ " + bookletEndNo + " (" + number.toPlainString() + ")";
    }

    /**
     * get voucher number
     * @return voucher number start ~ end (number)
     */
    public String getVoucherNumber() {

        // empty
        if (voucherStartNo == null || voucherEndNo == null) {
            return StringUtils.EMPTY;
        }

        // voucher number
        BigDecimal number = new BigDecimal(voucherEndNo).subtract(new BigDecimal(voucherStartNo)).add(BigDecimal.ONE);

        // voucher number start ~ end (number)
        return voucherStartNo + " ~ " + voucherEndNo + " (" + AmountUtils.idrCommaFormat(number) + ")";
    }

    /**
     * get create by
     * @param userMap user map
     * @return create by (first name + last name)
     */
    public String getCreateBy(JoinDataMap<UserAccount> userMap) {

        // find non value
        UserAccount user = userMap.findValue(createUser);

        // create by (first name + last name)
        return StringUtils.join(new String[] {user.getFirstName(), user.getLastName()}, " ");
    }

    public String getSourceCreateTime() {

        // return create time (String)
        return createTime;
    }
    public Date getCreateTime() {

        // return create time (Date)
        return DateUtil.parseDate(createTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}

