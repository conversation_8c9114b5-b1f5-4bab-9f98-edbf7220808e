package com.gtech.gvcore.service.report.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.export.file.ReportExcelUtils;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.PerformanceBean;
import com.gtech.gvcore.service.report.impl.bo.PerformanceBo;
import com.gtech.gvcore.service.report.impl.bo.PerformanceStatisticBo;
import com.gtech.gvcore.service.report.impl.param.PerformanceQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName PerformanceImpl
 * @Description Performance report
 * <AUTHOR>
 * @Date 2022/12/29 15:05
 * @Version V1.0
 **/
@Service
public class PerformanceImpl extends ReportSupport
        implements BusinessReport<PerformanceQueryData, PerformanceBean>, SingleReport {

    public static final String TOTAL_ALL = "Total All";
    public static final String OUTLET_TOTAL = "Total Amount Per Outlet";
    public static final String DELIMITER = "_";

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.PERFORMANCE_REPORT;
    }

    @Override
    public PerformanceQueryData builderQueryParam(CreateReportRequest reportParam) {

        PerformanceQueryData performanceQueryData = new PerformanceQueryData();

        performanceQueryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        performanceQueryData.setMerchantCodeList(reportParam.getMerchantCodes());
        performanceQueryData.setOutletCodeList(reportParam.getOutletCodes());

        performanceQueryData.setCpgCodeList(reportParam.getCpgCodes());

        performanceQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        performanceQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        return performanceQueryData;
    }

    @Override
    public List<PerformanceBean> getExportData(PerformanceQueryData param) {

        final Map<String, PerformanceStatisticBo> boMap = new HashMap<>();

        PollPageHelper.pollSelect(reportBusinessMapper::selectPerformance, param, e -> group(e, boMap));

        final Collection<PerformanceStatisticBo> statisticBos = boMap.values();

        if (CollectionUtils.isEmpty(statisticBos)) return Collections.emptyList();

        final List<PerformanceStatisticBo> result = new ArrayList<>();

        statisticBos.stream().collect(Collectors.groupingBy(PerformanceStatisticBo::getIssuerCode)).forEach((is, iv) ->
                iv.stream().collect(Collectors.groupingBy(PerformanceStatisticBo::getMerchantCode)).forEach((mk, mv) -> {

            final PerformanceStatisticBo merchantTotalValueSource = mv.stream().findFirst().orElseGet(PerformanceStatisticBo::new);
            final PerformanceStatisticBo merchantTotal = new PerformanceStatisticBo()
                    .setIssuerCode(merchantTotalValueSource.getIssuerCode())
                    .setMerchantCode(merchantTotalValueSource.getMerchantCode())
                    .setCpgCode(TOTAL_ALL);

            final List<PerformanceStatisticBo> merchantBo = new ArrayList<>();

            mv.stream().collect(Collectors.groupingBy(PerformanceStatisticBo::getOutletCode)).forEach((ok, ov) -> {

                final PerformanceStatisticBo outletTotalValueSource = ov.stream().findFirst().orElseGet(PerformanceStatisticBo::new);

                final PerformanceStatisticBo outletTotal = new PerformanceStatisticBo()
                        .setIssuerCode(outletTotalValueSource.getIssuerCode())
                        .setMerchantCode(outletTotalValueSource.getMerchantCode())
                        .setOutletCode(outletTotalValueSource.getOutletCode())
                        .setOutletType(outletTotalValueSource.getOutletType())
                        .setCpgCode(OUTLET_TOTAL);

                merchantBo.addAll(ov);
                merchantBo.add(settingTotal(ov, outletTotal));
            });

            result.addAll(merchantBo);
            result.add(settingTotal(mv, merchantTotal));

        }));



        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(result, PerformanceStatisticBo::getCpgCode, Cpg.class);
        final JoinDataMap<Issuer> issuerMap = super.getMapByCode(result, PerformanceStatisticBo::getIssuerCode, Issuer.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(result, PerformanceStatisticBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(result, PerformanceStatisticBo::getOutletCode, Outlet.class);

        cpgMap.put(TOTAL_ALL, new Cpg().setCpgName(TOTAL_ALL));
        cpgMap.put(OUTLET_TOTAL, new Cpg().setCpgName(OUTLET_TOTAL));

        return result.stream()
                .map(e -> new PerformanceBean()
                        .setIssuer(issuerMap.findValue(e.getIssuerCode()).getIssuerName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setOutlet(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setOutletCode(outletMap.findValue(e.getOutletCode()).getBusinessOutletCode())
                        .setOutletType(outletMap.findValue(e.getOutletCode()).getOutletType())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setNetSalesCount(String.valueOf(e.getNetSalesCount()))
                        .setNetSalesAmount(super.toAmount(e.getNetSalesAmount()))
                        .setNetRedemptionCount(String.valueOf(e.getNetRedemptionCount()))
                        .setNetRedemptionAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getNetRedemptionAmount(), BigDecimal.ZERO).multiply(new BigDecimal("-1"))))
                )
                .collect(Collectors.toList());
    }

    private void group(final Collection<PerformanceBo> performanceBos, final Map<String, PerformanceStatisticBo> boMap) {

        if (CollectionUtils.isEmpty(performanceBos)) return;

        final JoinDataMap<Outlet> outletMap = super.getMapByCode(performanceBos, PerformanceBo::getOutletCode, Outlet.class);

        performanceBos
                .stream()
                .filter(e -> outletMap.containsKey(e.getOutletCode()))
                .forEach(e -> {

                    Outlet outlet = outletMap.findValue(e.getOutletCode());

                    String key = String.join(DELIMITER, e.getIssuerCode(), e.getMerchantCode(), e.getOutletCode(), outlet.getOutletType(), e.getCpgCode());
                    Function<String, PerformanceStatisticBo> absentFunction =
                            k -> new PerformanceStatisticBo()
                                    .setIssuerCode(ConvertUtils.toString(e.getIssuerCode(), ""))
                                    .setMerchantCode(ConvertUtils.toString(e.getMerchantCode(), ""))
                                    .setOutletCode(ConvertUtils.toString(e.getOutletCode(), ""))
                                    .setOutletType(ConvertUtils.toString(outlet.getOutletType(), ""))
                                    .setCpgCode(ConvertUtils.toString(e.getCpgCode(), ""));

                    PerformanceStatisticBo performanceStatisticBo = boMap.computeIfAbsent(key, absentFunction);

                    //统计
                    if (TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(e.getTransactionType())) performanceStatisticBo.addNetRedemptionCount(1).addNetRedemptionAmount(e.getDenomination());
                    else if (TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM.equalsCode(e.getTransactionType())) performanceStatisticBo.addNetRedemptionCount(-1).addNetRedemptionAmount(e.getDenomination().negate());
                    else if (TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(e.getTransactionType())) performanceStatisticBo.addNetSalesCount(1).addNetSalesAmount(e.getDenomination());
                    else if (TransactionTypeEnum.GIFT_CARD_CANCEL_SELL.equalsCode(e.getTransactionType())) performanceStatisticBo.addNetSalesCount(-1).addNetSalesAmount(e.getDenomination().negate());
                });

    }

    public PerformanceStatisticBo settingTotal(List<PerformanceStatisticBo> list, PerformanceStatisticBo total) {

        list.forEach(e -> total.addNetSalesCount(e.getNetSalesCount())
                .addNetSalesAmount(e.getNetSalesAmount())
                .addNetRedemptionCount(e.getNetRedemptionCount())
                .addNetRedemptionAmount(e.getNetRedemptionAmount()));

        return total;
    }

    @Override
    public void customContext(ReportContextBuilder builder) {

        builder.bindFileContext(new PerformanceFileContext());
    }

    /**
     * performance excel context
     * @Description performance 表格上下文
     */
    public static class PerformanceFileContext implements FileContext {

        private static final String TEMPLATE_NAME = ReportExportTypeEnum.PERFORMANCE_REPORT.getTemplateName();

        private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        private ExcelWriter excelWriter;

        @Override
        public void init() {

            excelWriter = EasyExcelFactory.write(outputStream)
                    .registerWriteHandler(new PerformanceCellWriteHandler())
                    // 设置在内存中构建 , 由于该报表属于统计性质的因此用此参数来解决并行时可能存在的问题
                    .inMemory(true)
                    .withTemplate(Thread.currentThread().getContextClassLoader().getResourceAsStream(TEMPLATE_NAME))
                    .build();
        }

        @Override
        public void doFill(List<?> list) {

            WriteSheet writeSheet = EasyExcelFactory.writerSheet(ReportExportTypeEnum.PERFORMANCE_REPORT.getSheetName()).build();

            excelWriter.write(list, writeSheet);
        }

        @Override
        public String finish() {

            excelWriter.finish();

            return ReportUploadHelper.fileUpload(ReportContextHelper.findContext(), new ByteArrayInputStream(outputStream.toByteArray()));
        }

        /**
         * performance cell write handler
         * @Description performance report 样式处理
         */
        public static class PerformanceCellWriteHandler implements CellWriteHandler {

            private CellStyle cellStyle;

            public CellStyle getCellStyle(Workbook workbook) {

                if (this.cellStyle == null) {
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    this.cellStyle = cellStyle;
                }

                return this.cellStyle;
            }


            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {

                //数据准备
                Cell cell = context.getCell();//当前格子
                Sheet sheet = cell.getSheet();
                int rowIndex = cell.getRowIndex();//当前行
                int columnIndex = cell.getColumnIndex();//当前列

                //设置样式
                cell.setCellStyle(getCellStyle(context.getWriteWorkbookHolder().getWorkbook()));

                //只判断前三列
                if (columnIndex > 2) return;

                //index 定位
                int beginRowIndex = getBeginRowIndex(cell, sheet, rowIndex, columnIndex);

                //第三列需要
                if (columnIndex == 2) {
                    //outlet

                    ReportExcelUtils.mergeRow(sheet, beginRowIndex, rowIndex, columnIndex);
                    ReportExcelUtils.mergeRow(sheet, beginRowIndex, rowIndex, 3);
                    ReportExcelUtils.mergeRow(sheet, beginRowIndex, rowIndex, 4);

                } else {
                    //issuer merchant
                    ReportExcelUtils.mergeRow(sheet, beginRowIndex, rowIndex, columnIndex);
                }


            }

            /**
             * 获得数据起始index
             */
            private int getBeginRowIndex(Cell cell, Sheet sheet, int rowIndex, int columnIndex) {

                //当前列数据
                String value = cell.getStringCellValue();

                //起始行(0 行为title)
                int beginRowIndex = 1;

                //当前行向上循环
                for (int i = rowIndex - 1; i > 0; i--) {

                    //获得列苏剧值
                    String nowCellValue = ReportExcelUtils.getCell(sheet, i, columnIndex).getStringCellValue();

                    //不相等跳出
                    if (!value.equals(nowCellValue)) {
                        beginRowIndex = i + 1;
                        break;
                    }

                }

                //结果
                return beginRowIndex;
            }


        }

    }


}
