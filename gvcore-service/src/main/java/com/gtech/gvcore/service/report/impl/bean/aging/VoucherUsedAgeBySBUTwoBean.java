package com.gtech.gvcore.service.report.impl.bean.aging;

import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherUsedAgeBySBUBo
 * @Description voucher used age by sbu bo
 * <AUTHOR>
 * @Date 2022/11/3 15:49
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherUsedAgeBySBUTwoBean {
    @ReportLabel(value = "Details")
    private String sbuName;

    @ReportLabel(value = "1 month")
    private String oneValue;

    @ReportLabel(value = "2 month")
    private String twoValue;

    @ReportLabel(value = "3 month")
    private String threeValue;

    @ReportLabel(value = "4 to 6 month")
    private String fourValue;

    @ReportLabel(value = "6 to 12 month")
    private String sixValue;

    @ReportLabel(value = "Grand Total")
    private String total;

}
