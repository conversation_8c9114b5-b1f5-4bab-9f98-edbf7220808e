package com.gtech.gvcore.service.report.impl.support.liability.model;


import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.ReportTempLiabilitySStructure;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @ClassName LiabilitySummaryStatisticDto
 * @Description LiabilitySummaryStatisticDto
 * <AUTHOR>
 * @Date 2023/4/15 10:29
 * @Version V1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class LiabilitySummaryStatisticDto {

    private Date lastMoonTime;

    private String issuerCode;

    private String merchantCode;

    private String outletCode;

    private String cpgCode;

    private AtomicReference<BigDecimal> activatedAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> purchasedAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> deactivatedAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> expiredAmount = new AtomicReference<>(BigDecimal.ZERO);

    private AtomicReference<BigDecimal> recentlyExpiredAmount = new AtomicReference<>(BigDecimal.ZERO);

    public static LiabilitySummaryStatisticDto convert(final LiabilityVoucherBo bo) {

        // init
        final LiabilityVoucherMode voucher = GvConvertUtils.toObject(bo.getVoucher(), new LiabilityVoucherMode());
        final BigDecimal denomination = GvConvertUtils.toBigDecimal(voucher.getDenomination(), BigDecimal.ZERO);
        final ReportVoucherStatusEnum status = bo.getVoucherStatusEnum();
        final LiabilityTransactionModel transactionData = GvConvertUtils.toObject(bo.getStatusTransactionData(), new LiabilityTransactionModel());
        final LiabilitySummaryStatisticDto dto = new LiabilitySummaryStatisticDto()
                .setIssuerCode(ConvertUtils.toString(transactionData.getIssuerCode(), ""))
                .setMerchantCode(ConvertUtils.toString(transactionData.getMerchantCode(), ""))
                .setOutletCode(ConvertUtils.toString(transactionData.getOutletCode(), ""))
                .setCpgCode(voucher.getCpgCode())
                .setLastMoonTime(bo.getLastMoonTime());

        if (status == ReportVoucherStatusEnum.VOUCHER_DEACTIVATED) dto.deactivatedAmount.updateAndGet(v -> v.add(denomination));
        else if (status == ReportVoucherStatusEnum.VOUCHER_PURCHASED) dto.purchasedAmount.updateAndGet(v -> v.add(denomination));
        else if (status == ReportVoucherStatusEnum.VOUCHER_ACTIVATED) dto.activatedAmount.updateAndGet(v -> v.add(denomination));
        else if (status == ReportVoucherStatusEnum.VOUCHER_EXPIRED) {

            dto.expiredAmount.updateAndGet(v -> v.add(denomination));

            // 最近过期
            final ReportVoucherStatusEnum lastMoonTimeStatus = ReportVoucherStatusConvertUtils.getVoucherStatus(voucher.convertVoucher(), dto.lastMoonTime);
            if (lastMoonTimeStatus != ReportVoucherStatusEnum.VOUCHER_EXPIRED) dto.recentlyExpiredAmount.updateAndGet(v -> v.add(denomination));

        }

        return dto;
    }

    public static String getGroupKey(LiabilitySummaryStatisticDto bean) {

        return StringUtils.join("_", bean.getIssuerCode(), bean.getMerchantCode(), bean.getOutletCode(), bean.getCpgCode());
    }

    public LiabilitySummaryStatisticDto merge(LiabilitySummaryStatisticDto bo) {

        this.deactivatedAmount.updateAndGet(v -> v.add(bo.deactivatedAmount.get()));
        this.purchasedAmount.updateAndGet(v -> v.add(bo.purchasedAmount.get()));
        this.activatedAmount.updateAndGet(v -> v.add(bo.activatedAmount.get()));
        this.expiredAmount.updateAndGet(v -> v.add(bo.expiredAmount.get()));
        this.recentlyExpiredAmount.updateAndGet(v -> v.add(bo.recentlyExpiredAmount.get()));

        return this;
    }

    public ReportTempLiabilitySStructure toEntity() {

        return new ReportTempLiabilitySStructure()
                .setIssuerCode(this.issuerCode)
                .setMerchantCode(this.merchantCode)
                .setOutletCode(this.outletCode)
                .setCpgCode(this.cpgCode)
                .setActivatedAmount(this.activatedAmount.get())
                .setPurchasedAmount(this.purchasedAmount.get())
                .setDeactivatedAmount(this.deactivatedAmount.get())
                .setExpiredAmount(this.expiredAmount.get())
                .setRecentlyExpiredAmount(this.recentlyExpiredAmount.get());

    }

}
