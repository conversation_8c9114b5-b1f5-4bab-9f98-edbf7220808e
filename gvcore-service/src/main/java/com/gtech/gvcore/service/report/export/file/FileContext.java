package com.gtech.gvcore.service.report.export.file;

import java.util.List;

/**
 * @ClassName FileContext
 * @Description 报表文件上下文
 * <AUTHOR>
 * @Date 2022/10/28 14:22
 * @Version V1.0
 **/
public interface FileContext {

    /**
     * 初始化
     */
    default void init() {}

    /**
     * 追加数据
     */
    default void doFill(List<?> beanList) {}

    /**
     * 结束方法
     * @return 文件路径
     */
    String finish();

    /**
     * 错误时被调用
     */
    default void error() {}
}