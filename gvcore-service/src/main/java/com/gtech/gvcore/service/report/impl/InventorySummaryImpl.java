package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.impl.bean.InventorySummaryBean;
import com.gtech.gvcore.service.report.impl.bo.InventorySummaryBo;
import com.gtech.gvcore.service.report.impl.param.InventoryQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName InventorySummaryImpl
 * @Description InventorySummaryImpl
 * <AUTHOR>
 * @Date 2022/9/21 14:07
 * @Version V1.0
 **/
@Service
@Slf4j
public class InventorySummaryImpl extends ReportSupport
        implements BusinessReport<InventoryQueryData, InventorySummaryBean>, PollReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.INVENTORY_SUMMARY_REPORT;
    }


    @Override
    public InventoryQueryData builderQueryParam(CreateReportRequest reportParam) {
        ReportParamConvertHelper.convertQueryDateMerchantCodeToOutletCode(reportParam);

        InventoryQueryData inventoryReport = new InventoryQueryData();

        inventoryReport.setIssuerCodes(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        inventoryReport.setOutletCodes(reportParam.getOutletCodes());
        inventoryReport.setCpgCodes(reportParam.getCpgCodes());
        inventoryReport.setOrderStatus(reportParam.getOrderStatuses());
        inventoryReport.setBookletStatus(reportParam.getBookletStatus());
        inventoryReport.setStartBookletNo(reportParam.getBookletStart());
        inventoryReport.setEndBookletNo(reportParam.getBookletEnd());
        inventoryReport.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        inventoryReport.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        return inventoryReport;
    }

    @Override
    public List<InventorySummaryBean> getExportData(InventoryQueryData queryData) {

        List<InventorySummaryBo> inventorySummaryList = reportBusinessMapper.inventorySummary(queryData, GvPageHelper.getRowBounds(queryData));
        if (CollectionUtils.isEmpty(inventorySummaryList)) return Collections.emptyList();

        inventorySummaryList.forEach(x -> x.setCardStatus(VoucherStatusEnum.getByCode(Integer.valueOf(x.getCardStatus()))));

        List<InventorySummaryBean> inventorySummaryBeans = new ArrayList<>(inventorySummaryList.size());

        inventorySummaryList.forEach(summaryResponse -> {

            if (summaryResponse.getCardStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getDesc())){
                return;
            }

            InventorySummaryBean inventorySummaryBean = new InventorySummaryBean();

            inventorySummaryBean.setIssuer(summaryResponse.getIssuer());
            inventorySummaryBean.setMerchant(summaryResponse.getMerchant());
            inventorySummaryBean.setOutletCode(summaryResponse.getOutletCode());
            inventorySummaryBean.setMerchantOutletName(summaryResponse.getOutletName());
            inventorySummaryBean.setVpg(summaryResponse.getVoucherProgramGroup());
            inventorySummaryBean.setVpgType(summaryResponse.getCardProgramGroupType());
            inventorySummaryBean.setCardStatus(StringUtils.toRootUpperCase(summaryResponse.getCardStatus()));
            inventorySummaryBean.setCardsCount(summaryResponse.getCardsCount());
            inventorySummaryBean.setExpiryDate(DateUtil.format(DateUtil.parseDate(summaryResponse.getExpiryDate().replace("T"," "),DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));
            /*inventorySummaryBean.setBookletCount(summaryResponse.getBookletCount());
            if (StringUtil.isNotEmpty(summaryResponse.getBookletStatus())){
                inventorySummaryBean.setBookletStatus(BookletStatusEnum.getStatus(Integer.valueOf(summaryResponse.getBookletStatus())).getDesc());
            }*/
            inventorySummaryBeans.add(inventorySummaryBean);
        });

        sliceStatistics(inventorySummaryBeans, ReportContextHelper.findContext());

        return inventorySummaryBeans;
    }


    @Override
    public void dataFinish(ReportContext reportContext) {

        this.finishStatistics(reportContext);
    }

    private void finishStatistics(ReportContext context) {

        Integer total = context.getCache(TOTAL_KEY, Integer.class);
        if (Objects.isNull(total)) return;

        try {
            InventorySummaryBean totalBean = new InventorySummaryBean();
            totalBean.setIssuer(TOTAL_KEY);
            totalBean.setCardsCount(String.valueOf(total));
            context.appendDate(Collections.singletonList(totalBean));
        } catch (Exception e) {
            log.error("统计错误", e);
        }
    }

    private static final String TOTAL_KEY = "total";

    private void sliceStatistics(List<InventorySummaryBean> dataList, ReportContext context) {

        Integer cardsCount = dataList.stream().map(InventorySummaryBean::getCardsCount)
                .mapToInt(Integer::parseInt).sum();

        Integer total = context.getCache(TOTAL_KEY, Integer.class);
        if (Objects.isNull(total)) context.putCache(TOTAL_KEY, cardsCount);
        else {
            try {
                context.putCache(TOTAL_KEY, total + cardsCount);
            } catch (Exception e) {
                log.error("统计错误", e);
                context.putCache(TOTAL_KEY, 0);
            }
        }

    }

}
