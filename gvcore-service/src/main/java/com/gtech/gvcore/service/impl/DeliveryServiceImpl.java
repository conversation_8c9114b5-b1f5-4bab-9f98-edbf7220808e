package com.gtech.gvcore.service.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.delivery.InsertDeliveryRequest;
import com.gtech.gvcore.dao.mapper.DeliveryMapper;
import com.gtech.gvcore.dao.model.GvDelivery;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.DeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/2/17 16:33
 */
@Service
public class DeliveryServiceImpl implements DeliveryService {

    @Autowired
    private DeliveryMapper deliveryMapper;

    @Autowired
    private GvCodeHelper codeHelper;

    @Override
    public void insertDelivery(InsertDeliveryRequest request) {

        GvDelivery gvDelivery = BeanCopyUtils.jsonCopyBean(request, GvDelivery.class);
        gvDelivery.setDeliveryCode(codeHelper.generateDeliveryCode());
        deliveryMapper.insertSelective(gvDelivery);
    }
}
