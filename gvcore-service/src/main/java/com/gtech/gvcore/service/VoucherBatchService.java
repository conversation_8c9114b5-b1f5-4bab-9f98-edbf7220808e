package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.senddigitalvoucherexceltoemail.SendDigitalVoucherExcelToEmailRequest;
import com.gtech.gvcore.common.request.voucher.SendVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.*;
import com.gtech.gvcore.common.response.voucherbatch.QueryStartCodeResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.dao.model.VoucherBatch;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/2 10:45
 */
public interface VoucherBatchService {


    Result<String> createVoucherBatch(CreateVoucherBatchRequest request);

    Result<Void> regenerateVoucherBatch(RegenerateVoucherBatchRequest param);


    void cancelVoucherBatch(CancelVoucherBatchRequest param);

    List<VoucherBatch> queryVoucherBatchByCodeList(List<String> codeList);

    PageResult<VoucherBatchResponse> queryVoucherBatch(QueryVoucherBatchRequest request);

    VoucherBatchResponse getVoucherBatch(GetVoucherBatchRequest request);

    Result<Void> updateVoucherBatchStatus(UpdateVoucherBatchStatusRequest param);

    Result<Void> updateVoucherBatch(UpdateVoucherBatchRequest param);

    Result<Integer> realTimeProgressBar(QueryRealTimeProgressBarRequest param);

    QueryStartCodeResponse queryStartCode(QueryStartCodeRequest request);


    String export(SendVoucherRequest request) throws IOException;

    Result<String> generateDigitalVoucher(GenerateDigitalVouchersRequest request) throws IOException;

    void sendDigitalVoucherExcelToEmail(SendDigitalVoucherExcelToEmailRequest request) throws IOException;

    void resendVoucherExcelEmail(String emailCode);

    String barCodeToCode(BarCodeToCodeRequest barCode);
}
