package com.gtech.gvcore.service.report.impl.param;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName PerformanceQueryData
 * @Description PerformanceQueryData
 * <AUTHOR>
 * @Date 2022/9/20 17:12
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherMovementDetailQueryData extends VoucherMovementSummaryQueryData {

    //Voucher code number end
    private List<String> voucherStatusList;

    private List<String> issuerCodeList;

    private List<String> outletCodeList;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;


}
