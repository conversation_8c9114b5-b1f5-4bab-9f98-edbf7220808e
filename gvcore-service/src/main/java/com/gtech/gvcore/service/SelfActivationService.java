package com.gtech.gvcore.service;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.selfactivation.ActivateVouchersRequest;
import com.gtech.gvcore.common.request.selfactivation.GetActivationInfoRequest;
import com.gtech.gvcore.common.request.selfactivation.RequestOtpRequest;
import com.gtech.gvcore.common.request.selfactivation.ResendActivationEmailRequest;
import com.gtech.gvcore.common.response.selfactivation.ActivateVouchersResponse;
import com.gtech.gvcore.common.response.selfactivation.GetActivationInfoResponse;
import com.gtech.gvcore.common.response.selfactivation.RequestOtpResponse;

/**
 * Self-activation service interface for delayed activation functionality
 */
public interface SelfActivationService {
    
    /**
     * Get activation page information by token
     * 
     * @param request GetActivationInfoRequest
     * @return Result<GetActivationInfoResponse>
     */
    Result<GetActivationInfoResponse> getActivationInfo(GetActivationInfoRequest request);
    
    /**
     * Request OTP for activation
     * 
     * @param request RequestOtpRequest
     * @return Result<RequestOtpResponse>
     */
    Result<RequestOtpResponse> requestOtp(RequestOtpRequest request);
    
    /**
     * Activate vouchers with OTP verification
     * 
     * @param request ActivateVouchersRequest
     * @return Result<ActivateVouchersResponse>
     */
    Result<ActivateVouchersResponse> activateVouchers(ActivateVouchersRequest request);
    
    /**
     * Admin function to resend activation email
     * 
     * @param request ResendActivationEmailRequest
     * @return Result<String>
     */
    Result<String> resendActivationEmail(ResendActivationEmailRequest request);
    
    /**
     * Create self-activation log and send initial activation email
     * Called during order processing (Receive node)
     * 
     * @param customerOrderCode Customer order code
     * @param customerEmail Customer email
     * @return Result<String>
     */
    Result<String> createSelfActivationTask(String customerOrderCode, String customerEmail);
}
