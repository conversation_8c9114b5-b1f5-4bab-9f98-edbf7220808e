package com.gtech.gvcore.service.report.impl.support.life.builder;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.CardLifeCycleMovementBean;
import com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.life.VoucherLifeCycleAbstract;
import com.gtech.gvcore.service.report.impl.support.life.bo.VoucherLifeCycleMovementBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CardLifeCycleMovementBuilder
 * @Description 卡券移动信息报表构建
 * <AUTHOR>
 * @Date 2023/1/5 17:53
 * @Version V1.0
 **/
@Service
public class VoucherLifeCycleMovementBuilder extends VoucherLifeCycleAbstract<CardLifeCycleMovementBean> {

    public List<CardLifeCycleMovementBean> builder (VoucherLifeCycleQueryData queryData) {

        return builder(queryData.getVoucherCode());
    }

    @Override
    public String getFillKey() {
        return "movement";
    }

    private List<CardLifeCycleMovementBean> builder (String voucherCode) {

        List<VoucherLifeCycleMovementBo> list = super.reportBusinessMapper.selectVoucherLifeCycleVoucherMovement(voucherCode);

        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, Arrays.asList(VoucherLifeCycleMovementBo::getInbound, VoucherLifeCycleMovementBo::getOutbound), Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);

        return list.stream()
                .map(e-> {

                    final Outlet inbound = outletMap.findValue(e.getInbound());
                    final Outlet outbound = outletMap.findValue(e.getOutbound());
                    final Merchant merchant = merchantMap.findValue(inbound.getMerchantCode());

                    Date receiveDate = super.reportBusinessMapper.selectVoucherLifeCycleReceiveTime(voucherCode, e.getReceiverCode());
                    return new CardLifeCycleMovementBean()
                            .setMerchant(merchant.getMerchantName())
                            .setMerchantOutlet(inbound.getOutletName())
                            .setMerchantOutletCode(inbound.getBusinessOutletCode())
                            .setInbound(inbound.getOutletName())
                            .setOutbound(outbound.getOutletName())
                            .setVoucherRequestStatus(null == receiveDate ? "Allocated" : "Complete")
                            .setTimeZone(GvDateUtil.TIME_ZONE_DISPLAY_NAME)
                            .setVoucherNumber(voucherCode)
                            .setRequestNumber(e.getRequestId())
                            .setRequestedDate(DateUtil.format(e.getRequestTime(), GvDateUtil.FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_SS))
                            .setApprovedDate(DateUtil.format(e.getApprovedTime(), GvDateUtil.FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_SS))
                            .setAllocateDate(DateUtil.format(e.getAllLocationTime(), GvDateUtil.FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_SS))
                            .setReceiveDate(DateUtil.format(receiveDate, GvDateUtil.FORMAT_US_DATETIME_DD_MM_YYYY_HH_MM_SS));
                })
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.CARD_LIFE_CYCLE_MOVEMENT_REPORT;
    }

    @Override
    public Class<?> getExportDataClass() {
        return CardLifeCycleMovementBean.class;
    }
}
