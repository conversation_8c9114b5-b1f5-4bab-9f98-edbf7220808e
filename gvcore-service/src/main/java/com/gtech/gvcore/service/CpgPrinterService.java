package com.gtech.gvcore.service;

import java.util.List;
import java.util.Map;

import com.gtech.gvcore.dao.model.CpgPrinter;

/**
 * <AUTHOR>
 * @date 2022年3月7日
 */
public interface CpgPrinterService {

    /**
     * 
     * <AUTHOR>
     * @param list
     * @return
     * @date 2022年3月7日
     */
    int insertList(List<CpgPrinter> list);

    /**
     * 
     * <AUTHOR>
     * @param cpgCode
     * @return
     * @date 2022年3月7日
     */
    Map<String, CpgPrinter> queryMapByCpgCode(String cpgCode);

    /**
     * 
     * <AUTHOR>
     * @param cpgPrinter
     * @return
     * @date 2022年3月7日
     */
    int updateById(CpgPrinter cpgPrinter);

    /**
     * 
     * <AUTHOR>
     * @param cpgCode
     * @return
     * @date 2022年3月7日
     */
    List<String> queryPrinterCodeByCpgCode(String cpgCode);

}
