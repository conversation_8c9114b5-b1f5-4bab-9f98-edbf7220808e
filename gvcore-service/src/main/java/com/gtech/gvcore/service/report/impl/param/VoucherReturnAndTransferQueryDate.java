package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName VoucherReturnAndTransferQueryDate
 * @Description
 * <AUTHOR>
 * @Date 2023/2/14 19:09
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherReturnAndTransferQueryDate extends PageParam implements ReportQueryParam {

    private boolean findPendingTransfer;

    private boolean findPendingReturn;

    private List<String> issuerCodeList;

    private Date createTimeStart;

    private Date createTimeEnd;

    private String requestId;

    private List<String> cpgCodeList;

    private List<String> voucherReturnAndTransferStatusList;

    private List<String> fromStoreCodeList;

    private List<String> toStoreCodeList;

}