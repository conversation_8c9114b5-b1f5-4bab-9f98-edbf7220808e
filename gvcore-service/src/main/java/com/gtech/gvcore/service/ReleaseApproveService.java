package com.gtech.gvcore.service;

import java.util.List;
import java.util.function.Function;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.CreateLogRecode;
import com.gtech.gvcore.common.request.releaseapprove.GetNextAmountRequest;
import com.gtech.gvcore.common.request.releaseapprove.QueryApproveNodeRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountRequest;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import com.gtech.gvcore.common.response.releaseapprove.GetNextAmountResponse;
import com.gtech.gvcore.common.response.releaseapprove.ReleaseApproveAmountResponse;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.UserAccount;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 15:04
 */


public interface ReleaseApproveService {
    Result<List<String>> settingApproveConfig(List<ReleaseApproveAmountRequest> releaseApproveAmountRequests);

	Result<List<ReleaseApproveAmountResponse>> queryApproveConfig(String issuerCode);

    /**
     * -1：No permission
     * 0：No permission，System automatic approve
     * 1：Have permission
     */
	Result<Integer> approveAble(ReleaseApproveAbleRequest releaseApproveAbleRequest);

	Result<ApproveNodeRecord> approve(ApproveNodeRecordRequest approveNodeRecordRequest);

    /**
     * Create a log,record approve data
     *
     * @param createLogRecode param
     * @return approveNodeRecordCode
     */
    Result<String> createLogRecord(CreateLogRecode createLogRecode);

    /**
     * Get a approveType of audit record for businessCode
     *
     * @param businessCode business code
     * @param approveType  传递approveType查询指定type的log数据，传递null查询当前businessCode的所有log记录
     *                     Pass approveType to query log data of the specified type, pass NULL to
     *                     query all log records of the current businessCode
     * @return Log list
     */
    Result<List<ApproveNodeRecordResponse>> queryLogByBusinessCode(String businessCode, String approveType);

    Result<GetNextAmountResponse> getNextAmount(GetNextAmountRequest getNextAmountRequest);

    /**
     * 
     * @param businessCode
     * @param updateUser
     * @param releaseApproveAmountType
     * <AUTHOR>
     * @date 2022年5月17日
     */
    void updateByDeleted(String businessCode, String updateUser, String releaseApproveAmountType);
    
    /**
     * 
     * @param <T>
     * @param request
     * @param handlMethod
     * <AUTHOR>
     * @date 2022年5月18日
     */
    <T> void automaticApproveAndNoticeNextNode(ApproveNodeRecordRequest request,
            Function<ApproveNodeRecordRequest, Result<T>> handlMethod);

	/**
	 * query pending approve user
	 * 
	 * @param queryApproveNodeRequest
	 * @return
	 */
	List<UserAccount> queryUserByApproveNode(QueryApproveNodeRequest queryApproveNodeRequest);

}
