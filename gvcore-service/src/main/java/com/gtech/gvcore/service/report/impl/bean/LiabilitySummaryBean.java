package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 11:36
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class LiabilitySummaryBean {

    @ExcelProperty(index = 0, value = "Issuer")
    private String issuer;

    @ExcelProperty(index = 1, value = "Merchant")
    private String merchant;

    @ExcelProperty(index = 2, value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ReportAmountValue
    @ExcelProperty(index = 3, value = "Activated", converter = ExportExcelNumberConverter.class)
    private String activated;

    @ReportAmountValue
    @ExcelProperty(index = 4, value = "Purchased", converter = ExportExcelNumberConverter.class)
    private String purchased;

    @ReportAmountValue
    @ExcelProperty(index = 5, value = "Deactivated", converter = ExportExcelNumberConverter.class)
    private String deactivated;

    @ReportAmountValue
    @ExcelProperty(index = 6, value = "Expired", converter = ExportExcelNumberConverter.class)
    private String expired;

    @ReportAmountValue
    @ExcelProperty(index = 7, value = "Total", converter = ExportExcelNumberConverter.class)
    private String total;

    @ExcelProperty(index = 8, value = "As On Date")
    private String asOnDate;

    @ReportAmountValue
    @ReportLabel(dynamic = true, value = "Expired In({this#expiredIn})")
    @ExcelProperty(index = 9, converter = ExportExcelNumberConverter.class)
    private String expiredIn;

}
