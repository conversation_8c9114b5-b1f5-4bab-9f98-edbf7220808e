package com.gtech.gvcore.service;

import java.util.Date;
import java.util.List;

import com.gtech.gvcore.dao.model.SysLogger;
import com.gtech.gvcore.service.report.impl.param.AuditTrailReportQueryData;

/**
 * @ClassName SystemLoggerService
 * @Description 系统日志服务
 * <AUTHOR>
 * @Date 2022/10/20 10:39
 * @Version V1.0
 **/
public interface SystemLoggerService {

	SysLogger getLoggerById(int id, Date operationTime);

    List<SysLogger> queryLoggerReport(AuditTrailReportQueryData queryData);

    void addSysLogger(SysLogger sysLogger);

}
