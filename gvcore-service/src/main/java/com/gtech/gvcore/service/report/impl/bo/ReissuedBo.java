package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName ReissuedBo
 * @Description
 * <AUTHOR>
 * @Date 2023/5/10 14:28
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class ReissuedBo {

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

    private String voucherCode;

    private String merchantCode;

    private String outletCode;

    private String cpgCode;

    private String effectiveDate;

    private BigDecimal denomination;

    private String transactionDate;

    private String invoiceNumber;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getEffectiveDate() {

        return DateUtil.parseDate(effectiveDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
