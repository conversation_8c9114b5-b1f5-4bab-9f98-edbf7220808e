package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName AuditTrailReportQueryData
 * @Description
 * <AUTHOR>
 * @Date 2022/10/10 13:55
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class AuditTrailReportQueryData extends PageParam implements ReportQueryParam {

    private String requestId;

    private Date operateTimeBegin;

    private Date operateTimeEnd;

    private String userCode;

    private Date findEndTime;
}
