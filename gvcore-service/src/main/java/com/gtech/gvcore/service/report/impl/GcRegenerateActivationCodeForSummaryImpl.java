package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.IssueHandlingReportStatusEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.impl.bean.GcRegenerateActivationCodeBean;
import com.gtech.gvcore.service.report.impl.param.GcRegenerateActivationCodeQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName GcRegenerateActivationCodeImpl
 * @Description Gift Card Regenerate Activation Code Report Implementation
 * <AUTHOR> based on RegenerateActivationCodeImpl
 * @Date 2025/6/19
 * @Version V1.0
 **/
@Service
@Slf4j
public class GcRegenerateActivationCodeForSummaryImpl
        extends ReportSupport
        implements BusinessReport<GcRegenerateActivationCodeQueryData, GcRegenerateActivationCodeBean>, SingleReport {

    @Autowired
    private GvUserAccountService userAccountService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REGENERATE_ACTIVATION_CODE_REPORT;
    }

    @Override
    public GcRegenerateActivationCodeQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcRegenerateActivationCodeQueryData param = new GcRegenerateActivationCodeQueryData();

        // 设置时间范围
        param.setStartDate(reportParam.getTransactionDateStart());
        param.setEndDate(reportParam.getTransactionDateEnd());

        // 设置请求ID
        param.setIssueHandlingCode(reportParam.getVoucherRequestId());
        param.setFileName(reportParam.getUploadedFileName());
        param.setStatus(reportParam.getOrderStatuses());

        if (CollectionUtils.isNotEmpty(reportParam.getVoucherCodeList())) {
            param.setCardNumbers(reportParam.getVoucherCodeList());
        }
        if (reportParam.getVoucherCode() != null) {
            param.setCardNumbers(Arrays.stream(reportParam.getVoucherCode().split(",")).distinct().collect(Collectors.toList()));
        }
        return param;
    }

    @Override
    public List<GcRegenerateActivationCodeBean> getExportData(GcRegenerateActivationCodeQueryData param) {
        // 获取数据
        List<GcRegenerateActivationCodeBean> boList = getBoList(param);

        // 如果为空直接返回
        if (CollectionUtils.isEmpty(boList)) {
            return new ArrayList<>();
        }
        return boList;
    }

    /**
     * 获取基础数据列表
     */
    private List<GcRegenerateActivationCodeBean> getBoList(GcRegenerateActivationCodeQueryData param) {
        List<GcRegenerateActivationCodeBean> gcRegenerateActivationCodeBeans =
                gcReportBusinessMapper.selectGcRegenerateActivationCodeSummary(param);
        if (!gcRegenerateActivationCodeBeans.isEmpty()) {
            String totalAmount = gcRegenerateActivationCodeBeans.stream().map(x -> new BigDecimal(x.getTotalAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).toString();
            List<String> collect = gcRegenerateActivationCodeBeans.stream().map(GcRegenerateActivationCodeBean::getRequestor).distinct().collect(Collectors.toList());
            Map<String, String> userMap = userAccountService.queryFullNameByCodeList(collect);
            gcRegenerateActivationCodeBeans.forEach(x -> {
                x.setStatus(Objects.requireNonNull(IssueHandlingReportStatusEnum.valueOfCode(Integer.valueOf(x.getStatus()))).desc());
                x.setRequestor(userMap.get(x.getRequestor()));
                x.setTotalAmount(toAmount(x.getTotalAmount()));
            });
            GcRegenerateActivationCodeBean bean = new GcRegenerateActivationCodeBean();
            bean.setIssueHandlingCode("Total");
            bean.setTotalCards(gcRegenerateActivationCodeBeans.stream().map(GcRegenerateActivationCodeBean::getTotalCards).reduce(Integer::sum).orElse(0));
            bean.setTotalAmount(toAmount(totalAmount));
            gcRegenerateActivationCodeBeans.add(bean);
            return gcRegenerateActivationCodeBeans;
        }

        return Collections.emptyList();
    }

}
