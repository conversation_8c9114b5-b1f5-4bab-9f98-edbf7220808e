package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;


/**
 * @ClassName CancelSalesBo
 * @Description Cancel Sales BO
 * <AUTHOR>
 * @Date 2023/4/14 15:33
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcBalanceCorrectionBo {
    private String cardNumber;
    private String cpgCode;
    private String merchantCode;
    private String outletCode;
    private Date createTime;
    private String initialBalance;
    private BigDecimal correctionBalance;
    private String afterBalance;
    private String transactionType;
    private String customerCode;
    private Date expiryDate;
    private String notes;

}
