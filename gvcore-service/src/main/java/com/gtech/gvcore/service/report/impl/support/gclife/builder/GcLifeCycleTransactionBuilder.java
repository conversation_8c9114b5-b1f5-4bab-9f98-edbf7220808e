package com.gtech.gvcore.service.report.impl.support.gclife.builder;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.GcTransactionTypeEnum;
import com.gtech.gvcore.common.enums.RedemptionStatusEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.giftcard.domain.service.GcBlockDomainService;
import com.gtech.gvcore.giftcard.domain.service.GcExtensionDomainService;
import com.gtech.gvcore.giftcard.domain.service.GcSalesDomainService;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcActivationEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcRedemptionEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcActivationMapper;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcRedemptionMapper;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcSalesMapper;
import com.gtech.gvcore.service.report.impl.bean.GcLifeCycleTransactionBean;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.GcLifeCycleAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName GcLifeCycleTransactionBuilder
 * @Description Gift Card Life Cycle Transaction Builder
 * <AUTHOR> based on VoucherLifeCycleTransactionBuilder
 * @Date 2025年6月19日
 * @Version V1.0
 **/
@Slf4j
@Service
public class GcLifeCycleTransactionBuilder extends GcLifeCycleAbstract<GcLifeCycleTransactionBean> {


    @Autowired
    private GcSalesMapper gcSalesMapper;

    @Autowired
    private GcActivationMapper gcActivationMapper;

    @Autowired
    private GcRedemptionMapper gcRedemptionMapper;

    @Autowired
    private GiftCardApplicationService cardApplicationService;

    @Autowired
    private GcBlockDomainService gcBlockDomainService;

    @Autowired
    private GcSalesDomainService gcSalesDomainService;

    @Autowired
    private GcExtensionDomainService gcExtensionDomainService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_LIFE_CYCLE_TRANSACTION_REPORT;
    }

    @Override
    public Class<?> getExportDataClass() {
        return GcLifeCycleTransactionBean.class;
    }

    @Override
    public String getFillKey() {
        return "transaction";
    }

    @Override
    public List<GcLifeCycleTransactionBean> builder(GcLifeCycleQueryData queryData) {

        // 查询礼品卡信息
        List<String> cardNumber = queryData.getCardNumber();
        if (cardNumber == null || cardNumber.isEmpty()) {
            return Collections.emptyList();
        }

        List<GiftCardEntity> giftCard = cardApplicationService.queryByCardNumberList(null, cardNumber);

        if (null == giftCard || giftCard.isEmpty()) return Collections.emptyList();

        List<GcLifeCycleTransactionBean> transactionList = new ArrayList<>();

        // 查询所有相关交易表的数据
        // 使用单表查询，避免JOIN操作
        // 按照创建时间升序排列，显示完整的生命周期历史
        for (GiftCardEntity giftCardEntity : giftCard) {
            // 1. 查询销售交易 (gc_sales)
            addSalesTransactions(giftCardEntity.getCardNumber(), transactionList);

            // 2. 查询激活交易 (gc_activation)
            addActivationTransactions(giftCardEntity.getCardNumber(), transactionList);

            // 3. 查询兑换交易 (gc_redemption)
            addRedemptionTransactions(giftCardEntity.getCardNumber(), transactionList);

            // 4. 查询冻结交易 (gc_block)
            addBlockTransactions(giftCardEntity.getCardNumber(), transactionList);

            //5. 查询解冻交易 (gc_unblock)
            addUnblockTransactions(giftCardEntity.getCardNumber(), transactionList);

            // 6. 查询取消销售交易 (gc_cancel_sales)
            addCancelSalesTransactions(giftCardEntity.getCardNumber(), transactionList);

            //7. 查询延长激活期交易(gc_extend_activation_period)
            addExtendActivationTransactions(giftCardEntity.getCardNumber(), transactionList);
        }
        // 按创建时间升序排序
        transactionList.sort((a, b) -> {
            Date dateA = DateUtil.parseDate(a.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            Date dateB = DateUtil.parseDate(b.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            return dateA.compareTo(dateB);
        });

        return transactionList;
    }

    /**
     * 添加销售交易记录
     */
    private void addSalesTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            List<GcSalesEntity> salesList = gcSalesMapper.selectByCardNumber(cardNumber);
            for (GcSalesEntity sales : salesList) {
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        GcTransactionTypeEnum.GIFT_CARD_SELL.getDesc(),
                        DateUtil.format(sales.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        sales.getOutletCode()
                );
                bean.setInvoiceNumber(sales.getInvoiceNumber());
                bean.setTransactionAmount(super.toAmount(sales.getDenomination()));
                bean.setResponseMessage("Transaction Successful");
                bean.setSbuCompanyName(getSbuCompanyName(sales.getOutletCode()));
                transactionList.add(bean);
            }
        } catch (Exception e) {
            log.warn("Failed to query sales transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 添加激活交易记录
     */
    private void addActivationTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            GcActivationEntity activation = gcActivationMapper.selectByCardNumber(cardNumber);
            if (activation != null) {
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        GcTransactionTypeEnum.GIFT_CARD_ACTIVATE.getDesc(),
                        DateUtil.format(activation.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        activation.getOutletCode()
                );
                bean.setInvoiceNumber(activation.getInvoiceNumber());
                bean.setResponseMessage("Transaction Successful");
                bean.setSbuCompanyName(getSbuCompanyName(activation.getOutletCode()));
                transactionList.add(bean);
            }
        } catch (Exception e) {
            log.warn("Failed to query activation transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 添加兑换交易记录
     */
    private void addRedemptionTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            List<GcRedemptionEntity> redemptionList = gcRedemptionMapper.selectByCardNumber(cardNumber);
            for (GcRedemptionEntity redemption : redemptionList) {
                String type;
                if (Objects.equals(redemption.getTransactionType(), RedemptionStatusEnum.CANCELLED_REDEEMED.getCode())) {
                    type = GcTransactionTypeEnum.GIFT_CARD_CANCEL_REDEEM.getDesc();
                } else {
                    type = GcTransactionTypeEnum.GIFT_CARD_REDEEM.getDesc();
                }
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        type,
                        DateUtil.format(redemption.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        redemption.getOutletCode()
                );
                bean.setInvoiceNumber(redemption.getInvoiceNumber());
                bean.setTransactionAmount(super.toAmount(redemption.getAmount()));
                bean.setResponseMessage("Transaction Successful");
                bean.setSbuCompanyName(getSbuCompanyName(redemption.getOutletCode()));
                transactionList.add(bean);
            }
        } catch (Exception e) {
            log.warn("Failed to query redemption transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 添加冻结交易记录
     */
    private void addBlockTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            gcBlockDomainService.getBlockRecordsByCardNumber(cardNumber).forEach(blockRecord -> {
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        GcTransactionTypeEnum.GIFT_CARD_DEACTIVATE.getDesc(),
                        DateUtil.format(blockRecord.getBlockTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        blockRecord.getOutletCode()
                );
                bean.setResponseMessage("Transaction Successful");
                bean.setSbuCompanyName(getSbuCompanyName(blockRecord.getOutletCode()));
                bean.setInvoiceNumber(blockRecord.getInvoiceNumber());
                bean.setTransactionAmount(toAmount(blockRecord.getDenomination()));
                transactionList.add(bean);
            });
        } catch (Exception e) {
            log.warn("Failed to query block transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 添加解冻交易记录
     */
    private void addUnblockTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            gcBlockDomainService.getUnblockRecordsByCardNumber(cardNumber).forEach(unblockRecord -> {
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        GcTransactionTypeEnum.GIFT_CARD_REACTIVATE.getDesc(),
                        DateUtil.format(unblockRecord.getUnblockTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        unblockRecord.getOutletCode()
                );
                bean.setResponseMessage("Transaction Successful");
                bean.setInvoiceNumber(unblockRecord.getInvoiceNumber());
                bean.setTransactionAmount(toAmount(unblockRecord.getDenomination()));
                bean.setSbuCompanyName(getSbuCompanyName(unblockRecord.getOutletCode()));
                transactionList.add(bean);
            });
        } catch (Exception e) {
            log.warn("Failed to query unblock transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 添加取消销售交易记录
     */
    private void addCancelSalesTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            gcSalesDomainService.getCancelSalesRecordByCardNumber(cardNumber).forEach(cancelRecord -> {
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        GcTransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getDesc(),
                        DateUtil.format(cancelRecord.getCancelTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        cancelRecord.getOutletCode()
                );
                bean.setTransactionAmount(super.toAmount(cancelRecord.getDenomination()));
                bean.setInvoiceNumber(cancelRecord.getInvoiceNumber());
                bean.setResponseMessage("Transaction Successful");
                bean.setSbuCompanyName(getSbuCompanyName(cancelRecord.getOutletCode()));
                transactionList.add(bean);
            });
        } catch (Exception e) {
            log.warn("Failed to query cancel sales transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 添加延长激活期交易记录
     */
    private void addExtendActivationTransactions(String cardNumber, List<GcLifeCycleTransactionBean> transactionList) {
        try {
            gcExtensionDomainService.getExtensionRecordsByCardNumber(cardNumber).forEach(extensionRecord -> {
                GcLifeCycleTransactionBean bean = createTransactionBean(
                        cardNumber,
                        GcTransactionTypeEnum.GIFT_CARD_ACTIVATION_EXTENSION.getDesc(),
                        DateUtil.format(extensionRecord.getExtensionTime(), DateUtil.FORMAT_YYYYMMDDHHMISS),
                        extensionRecord.getOutletCode()
                );
                bean.setResponseMessage("Transaction Successful");
                bean.setInvoiceNumber(extensionRecord.getInvoiceNumber());
                bean.setTransactionAmount(super.toAmount(extensionRecord.getDenomination()));
                bean.setSbuCompanyName(getSbuCompanyName(extensionRecord.getOutletCode()));
                transactionList.add(bean);
            });
        } catch (Exception e) {
            log.warn("Failed to query extend activation transactions for card: {}", cardNumber, e);
        }
    }

    /**
     * 创建交易Bean的通用方法
     */
    private GcLifeCycleTransactionBean createTransactionBean(String cardNumber, String transactionType,
                                                             String transactionDate, String outletCode) {
        GcLifeCycleTransactionBean bean = new GcLifeCycleTransactionBean();

        // 设置基本信息
        bean.setTransactionDate(transactionDate);
        bean.setGiftCardNumber(cardNumber);
        bean.setTransactionType(transactionType);

        // 设置网点和商户信息
        if (outletCode != null) {
            try {
                Outlet outlet = super.nonNullGetByCode(outletCode, Outlet.class);
                Merchant merchant = super.nonNullGetByCode(outlet.getMerchantCode(), Merchant.class);
                bean.setMerchantName(merchant.getMerchantName());
                bean.setOutletName(outlet.getOutletName());
            } catch (Exception e) {
                log.warn("Failed to get outlet/merchant info for outlet: {}", outletCode, e);
                // 设置默认值
                bean.setMerchantName("Unknown");
                bean.setOutletName("Unknown");
            }
        } else {
            bean.setMerchantName("System");
            bean.setOutletName("System");
        }

        return bean;
    }

    /**
     * 获取SBU公司名称
     */
    private String getSbuCompanyName(String outletCode) {
        try {
            if (outletCode != null) {
                Outlet outlet = super.nonNullGetByCode(outletCode, Outlet.class);
                Merchant merchant = super.nonNullGetByCode(outlet.getMerchantCode(), Merchant.class);
                // 根据商户信息获取SBU公司名称，这里需要根据实际业务逻辑实现
                // 暂时返回商户名称
                return merchant.getMerchantName();
            }
        } catch (Exception e) {
            log.warn("Failed to get SBU company name for outlet: {}", outletCode, e);
        }
        return "Unknown";
    }
}
