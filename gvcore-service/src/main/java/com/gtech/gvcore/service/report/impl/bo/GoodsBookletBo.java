package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import static com.gtech.gvcore.service.report.impl.GoodsInTransitDetailedImpl.VOUCHER_NUMBER_PREFIX;

/**
 * @ClassName BookletGoodsModel
 * @Description
 * <AUTHOR>
 * @Date 2023/5/24 17:14
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GoodsBookletBo {

    private String bookletCode;
    private Long voucherStartNo;
    private Long voucherEndNo;

    private String voucherStartNoSource;

    public void setVoucherStartNo(String voucherStartNo) {

        this.voucherStartNoSource = voucherStartNo;
        this.voucherStartNo = StringUtils.isBlank(voucherStartNo) ? 0L : Long.parseLong(voucherStartNo.replaceAll(VOUCHER_NUMBER_PREFIX, ""));
    }

    public void setVoucherEndNo(String voucherEndNo) {

        this.voucherEndNo = StringUtils.isBlank(voucherEndNo) ? 0L : Long.parseLong(voucherEndNo.replaceAll(VOUCHER_NUMBER_PREFIX, ""));
    }

    public boolean match(long voucherNum) {

        return  voucherNum >= voucherStartNo && voucherNum <= voucherEndNo;
    }
}
