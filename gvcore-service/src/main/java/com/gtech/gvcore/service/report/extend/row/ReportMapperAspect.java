package com.gtech.gvcore.service.report.extend.row;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * @ClassName ReportMapperAspect
 * @Description
 *  拦截 ReportBusinessMapper 中的方法
 *  利用表下标的方式来解决分表时轮询查询的问题
 *  sing:
 *      List * (ReportPageParam+, RowBounds)
 *  case:
 *    在存在       table_0, table_1, table_2, table_3
 *    其数据分别为  1000, 5000, 2000, 3000
 *    且 pageSize = 5000
 *      default:
 *          多线程并行查询
 *          查询时第一页 table_0 offset 0 limit 5000 + table_1 offset 0 limit 5000 + table_2 offset 0 limit 5000 + table_3 offset 0 limit 5000
 *          查询时第二页 table_0 offset 0 limit 10000 + table_1 offset 0 limit 10000 + table_2 offset 0 limit 10000 + table_3 offset 0 limit 10000
 *          查询时第三页 table_0 offset 0 limit 15000 + table_1 offset 0 limit 15000 + table_2 offset 0 limit 15000 + table_3 offset 0 limit 15000
 *          ...
 *          该实现方式导致数据量越大查询效率越慢 无效数据越多 给数据库发布过多无效任务 且大量数据时导致数据库响应超时
 *          但少量数据时效率较高 且[数据有序]
 *      ReportMapperAspect:
 *          单线程查询
 *          第一页查询 table_0 offset 0 limit 5000 + table_1 offset 0 limit 4000
 *          第二页查询 table_1 offset 4000 limit 5000 + table_2 offset 0 limit 4000 + table_3 offset 0 limit 2000
 *          第三页查询 table_3 offset 2000 limit 5000
 *          该实现方式在大量数据时不会给数据库发送无效sql任务 仅关注当前分页所需的数据 但 [数据无序]
 *          且在数据量较少同时分布的表又相对较多时 会导致查询效率较低
 *          不会造成数据库响应超时问题
 * <AUTHOR>
 * @Date 2023/4/21 18:14
 * @Version V1.0
 **/
@Slf4j
@Aspect
@Component
public class ReportMapperAspect {

    /**
     * 切入点
     * 返回值为List
     * 且第一个参数为ReportPageParam的字类实现
     * 且第二个参数为RowBounds
     * 的所有方法
     */
    @Pointcut("execution(java.util.List com.gtech.gvcore.dao.mapper.ReportBusinessMapper.*(com.gtech.gvcore.service.report.extend.row.ReportPageParam+, org.apache.ibatis.session.RowBounds))")
    public void proceed() {
        // NO SONAR
    }

    @Around(value = "proceed()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        // 获取参数
        final Object[] args = proceedingJoinPoint.getArgs();

        // 获取分页参数
        final ReportPageParam reportPageParam = (ReportPageParam) args[0];

        // 获取分页参数
        final List<Object> listResult = new ArrayList<>();

        log.info("report page aspect [execute] _begin_. pageSize: {}, pageNumber: {}, tableIndex: {}", reportPageParam.getPageSize(), reportPageParam.getPageNum(), reportPageParam.getTableIndex());

        // 第一次查询
        final Object[] fistParam = {reportPageParam, reportPageParam.generateRowBounds(reportPageParam.getPageSize())};
        listResult.addAll(this.execute(proceedingJoinPoint, fistParam));

        // case 如果第一次查询结果已经满足分页条件则直接返回
        // case 如果当前表格已经循环到 MAX_TABLE_INDEX 但不满足前置条件 查询出来的数据 < pageSize 则直表明该查询已到达最后一页 直接返回数据
        if (listResult.size() == reportPageParam.getPageSize() || reportPageParam.getTableIndex() == ReportPageParam.MAX_TABLE_INDEX) {

            log.info("report page aspect [execute] _stop_. pageSize: {}, pageNumber: {}, tableIndex: {}", reportPageParam.getPageSize(), reportPageParam.getPageNum(), reportPageParam.getTableIndex());

            return listResult;
        }
        // 最后一次查询limit
        int offset = 0;

        // 循环查询
        while (listResult.size() < reportPageParam.getPageSize() && reportPageParam.getTableIndex() < ReportPageParam.MAX_TABLE_INDEX) {

            // 自增并更新当前表下标
            reportPageParam.incrementAndUpdateTable();
            reportPageParam.setPageNum(0);
            reportPageParam.setPageNum(reportPageParam.getPageNum() + 1);

            log.info("report page aspect execute [loop]. pageSize: {}, pageNumber: {}, tableIndex: {}, resultSize: {}", reportPageParam.getPageSize(), reportPageParam.getPageNum(), reportPageParam.getTableIndex(), listResult.size());

            // 重置 分页参数
            final int limit = reportPageParam.getPageSize() - listResult.size();
            final Object[] nextTableParam = {reportPageParam, reportPageParam.generateRowBounds(limit)};
            // 循环查询
            List<Object> nextTableResult = this.execute(proceedingJoinPoint, nextTableParam);

            // 追加结果
            listResult.addAll(nextTableResult);

            // 重置偏移量
            offset = limit;
        }

        log.info("report page aspect [execute] _stop_. pageSize: {}, pageNumber: {}, tableIndex: {}", reportPageParam.getPageSize(), reportPageParam.getPageNum(), reportPageParam.getTableIndex());

        // 最后一次查询的size(limit)  = 当前表中需要排除的数据偏移量
        reportPageParam.settingTableOffset(offset);
        reportPageParam.setPageNum(0);

        // 截取结果
        return listResult;
    }

    private List<Object> execute(ProceedingJoinPoint proceedingJoinPoint, Object[] nextTableParam) throws Throwable {

        return (List<Object>) proceedingJoinPoint.proceed(nextTableParam);
    }

}
