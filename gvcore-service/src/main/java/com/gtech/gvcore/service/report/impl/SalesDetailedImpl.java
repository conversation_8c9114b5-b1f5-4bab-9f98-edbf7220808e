package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.SalesDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.SalesBo;
import com.gtech.gvcore.service.report.impl.param.SalesQueryData;
import com.gtech.gvcore.service.report.impl.support.SalesBaseImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:01
 * @Description: 1m31.20s 0.24 - 2.03
 */
@Service
public class SalesDetailedImpl extends SalesBaseImpl<SalesDetailedBean>
        implements BusinessReport<SalesQueryData, SalesDetailedBean>, SingleReport {

    @Autowired private TransactionDataService transactionDataService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.SALES_DETAILED_REPORT;
    }

    @Override
    protected void addParam(SalesQueryData param, CreateReportRequest reportParam) {
        // No code
    }

    @Override
    public List<SalesDetailedBean> getExportData(List<SalesBo> boList) {

        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(boList, SalesBo::getVoucherCode, Voucher.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, SalesBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, SalesBo::getCpgCode, Cpg.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(boList, SalesBo::getPosCode, Pos.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, SalesBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);
        final Map<String, BigDecimal> billAmountMap = transactionDataService.sumAmountGroupByBillNumber(super.getCodeList(boList, SalesBo::getBillNumber));


        return boList.stream()
                .map(e -> {
                    final Pos pos = posMap.findValue(e.getPosCode());
                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    final String transactionDate = DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
                    final String posName = null != pos && StringUtil.isNotEmpty(pos.getPosName()) ? pos.getPosName() : "GV POS";
                    final String denomination = super.toAmount(e.getDenomination());
                    final String issuerYear = DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY);
                    final String effectiveDate = DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);

                    return new SalesDetailedBean()
                            .setCardNumber(e.getVoucherCode())
                            .setSubCompanyName(company.getCompanyName())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setTransactionDate(transactionDate)
                            .setPosName(posName)
                            .setProgramGroup(cpg.getCpgName())
                            .setTransactionType(TransactionTypeEnum.GIFT_CARD_SELL.getDesc())
                            .setAmount(denomination)
                            .setBillAmount(super.toAmount(billAmountMap.get(e.getBillNumber())))
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setResponseMessage(e.getResponseMessage())
                            .setDateAtClient(transactionDate)
                            .setBookletNumber(voucher.getBookletCode())
                            .setDenomination(denomination)
                            .setIssuanceYear(issuerYear)
                            .setExpiryDate(effectiveDate)
                            .setBatchNumber(voucher.getVoucherBatchCode())
                            .setReferenceNumber(e.getReferenceNumber())
                            .setCardEntryMode(posName)
                            .setRequestAmount(denomination)
                            .setApprovalCode(e.getApproveCode());
                }).collect(Collectors.toList());
    }


}
