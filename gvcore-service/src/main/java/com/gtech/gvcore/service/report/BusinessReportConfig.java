package com.gtech.gvcore.service.report;

import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;

/**
 * @ClassName BusinessReportConfig
 * @Description 业务报表接口配置
 * <AUTHOR>
 * @Date 2022/10/20 19:07
 * @Version V1.0
 **/
public interface BusinessReportConfig {

    /**
     * 报表结果快照单片大小
     * @return
     */
    default int snapshotSize() { return 3000; }

    /**
     * 自定义上下文
     * @param builder
     */
    default void customContext(ReportContextBuilder builder) {}

}
