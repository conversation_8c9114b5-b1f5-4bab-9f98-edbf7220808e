package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AgingBo
 * @Description aging report bo
 * <AUTHOR>
 * @Date 2022/10/27 17:06
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class AgingBo implements GroupNewTransactionByVoucherCodeSupport {

    private String voucherCode;

    private String transactionDate;

    private String customerCode;

    private String merchantCode;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    private String transactionType;

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

}
