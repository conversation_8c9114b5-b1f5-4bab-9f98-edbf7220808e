package com.gtech.gvcore.service;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.poscpg.CreatePosCpgRequest;
import com.gtech.gvcore.common.request.poscpg.DeletePosCpgByPosCodeRequest;
import com.gtech.gvcore.common.response.pos.PosCpgResponse;

import java.util.List;

public interface PosCpgService {


    Result<String> createPosCpg(CreatePosCpgRequest request);

    void deletePosCpgByPosCode(DeletePosCpgByPosCodeRequest build);

    List<PosCpgResponse> queryPosCpgListByPos(String posCode);
    List<PosCpgResponse> queryPosCpgListByMachineId(String machineId);
}
