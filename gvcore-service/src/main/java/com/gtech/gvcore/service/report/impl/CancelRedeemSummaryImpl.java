package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.CancelRedeemSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.CancelRedeemBo;
import com.gtech.gvcore.service.report.impl.param.CancelRedeemQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:58
 * @Description:
 */
@Service
public class CancelRedeemSummaryImpl extends ReportSupport
        implements BusinessReport<CancelRedeemQueryData, CancelRedeemSummaryBean>, SingleReport {


    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.CANCEL_REDEEMED_SUMMARY_REPORT;
    }

    @Override
    public CancelRedeemQueryData builderQueryParam(final CreateReportRequest reportParam) {

        final CancelRedeemQueryData cancelRedeemQueryData = new CancelRedeemQueryData();
        cancelRedeemQueryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        cancelRedeemQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        cancelRedeemQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        cancelRedeemQueryData.setMerchantCodeList(reportParam.getMerchantCodes());
        cancelRedeemQueryData.setOutletCodeList(reportParam.getOutletCodes());
        cancelRedeemQueryData.setCpgCodeList(reportParam.getCpgCodes());
        cancelRedeemQueryData.setInvoiceNo(reportParam.getInvoiceNo());

        return cancelRedeemQueryData;
    }

    /**
     * 查询 CancelRedeemedSummaryReport 的报表数据
     * 数据来源：<Issue Handling_Cancel Redeem>中Cancel Redeem的券以CPG的维度进行统计数量及汇总的券金额。
     *
     * @param queryData
     * @return java.util.List<com.gtech.gvcore.common.export.bean.CancelRedeemSummaryBean> CPG数据集
     * <AUTHOR>
     * @date 2022/7/7 17:24
     * @since 1.0.0
     */
    @Override
    public List<CancelRedeemSummaryBean> getExportData(final CancelRedeemQueryData queryData) {

        final Collection<CancelRedeemSummaryBo> cancelRedeemSummaryDtoList = getBoList(queryData);
        if (CollectionUtils.isEmpty(cancelRedeemSummaryDtoList)) return Collections.emptyList();

        final Map<String, Merchant> merchantCodeMerchantMap = super.getMapByCode(cancelRedeemSummaryDtoList, CancelRedeemSummaryBo::getMerchantCode, Merchant.class);
        final Map<String, Outlet> outletCodeOutletMap = super.getMapByCode(cancelRedeemSummaryDtoList, CancelRedeemSummaryBo::getOutletCode, Outlet.class);
        final Map<String, Cpg> cpgCodeCpgMap = this.getMapByCode(cancelRedeemSummaryDtoList, CancelRedeemSummaryBo::getCpgCode, Cpg.class);

        return cancelRedeemSummaryDtoList
                .stream()
                .map(e -> {

                    final CancelRedeemSummaryBean cancelRedeemSummaryBean = new CancelRedeemSummaryBean();

                    final Merchant merchant = merchantCodeMerchantMap.get(e.getMerchantCode());
                    cancelRedeemSummaryBean.setMerchant(null == merchant ? null : merchant.getMerchantName());

                    final Outlet outlet = outletCodeOutletMap.get(e.getOutletCode());
                    cancelRedeemSummaryBean.setOutlet(null == outlet ? null : outlet.getOutletName());

                    final Cpg cpg = cpgCodeCpgMap.get(e.getCpgCode());
                    cancelRedeemSummaryBean.setVoucherProgramGroup(null == cpg ? null : cpg.getCpgName());

                    cancelRedeemSummaryBean.setNumberOfVouchers(Integer.toString(e.getNumberOfVouchers()));

                    cancelRedeemSummaryBean.setTotalAmount(toAmount(e.getDenomination()));

                    return cancelRedeemSummaryBean;

                })
                .collect(Collectors.toList());
    }


    public Collection<CancelRedeemSummaryBo> getBoList(final CancelRedeemQueryData queryData) {

        List<CancelRedeemBo> cancelRedeemBoList = Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(this.reportBusinessMapper::selectCancelRedeem, queryData))
                // filter gift voucher cancel redeem
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM.equalsCode(s.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        return cancelRedeemBoList.stream()
                .map(CancelRedeemSummaryBo::convert)
                .collect(Collectors.toMap(
                                // key
                                CancelRedeemSummaryBo::getGroupKey,
                                // value
                                CancelRedeemSummaryBo::newInstance,
                                // merge
                                CancelRedeemSummaryBo::merge)
                        //to map
                ).values();

    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class CancelRedeemSummaryBo {

        /**
         * Merchant Code
         */
        private String merchantCode;

        /**
         * Outlet Code
         */
        private String outletCode;

        /**
         * Cpg Code / VPG Code
         */
        private String cpgCode;

        /**
         * 券数量
         */
        private Integer numberOfVouchers = 1;

        /**
         * 该CPG券的面额
         */
        private BigDecimal denomination = BigDecimal.ZERO;


        public static CancelRedeemSummaryBo convert(CancelRedeemBo bo) {

            return new CancelRedeemSummaryBo()
                    .setMerchantCode(bo.getMerchantCode())
                    .setOutletCode(bo.getOutletCode())
                    .setCpgCode(bo.getCpgCode())
                    .setDenomination(bo.getDenomination());
        }

        public static String getGroupKey(CancelRedeemSummaryBo bean) {

            return StringUtils.join("_", bean.getMerchantCode(), bean.getOutletCode(), bean.getCpgCode());
        }

        public static CancelRedeemSummaryBo newInstance(CancelRedeemSummaryBo bean) {

            return new CancelRedeemSummaryBo()
                    .setMerchantCode(bean.getMerchantCode())
                    .setOutletCode(bean.getOutletCode())
                    .setCpgCode(bean.getCpgCode())
                    .setDenomination(bean.getDenomination());
        }

        public CancelRedeemSummaryBo merge(CancelRedeemSummaryBo bo) {

            numberOfVouchers += ConvertUtils.toInteger(bo.getNumberOfVouchers(), 0);
            denomination = denomination.add(bo.getDenomination());

            return this;
        }

    }

}
