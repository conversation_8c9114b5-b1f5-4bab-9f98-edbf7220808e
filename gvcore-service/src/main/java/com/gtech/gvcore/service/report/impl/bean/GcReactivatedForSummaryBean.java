package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description: Gift Card Reactivated (Block) Report Summary Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcReactivatedForSummaryBean {

    /**
     * Merchant Name
     */
    @ExcelProperty(value = "Merchant Name")
    private String merchant;

    /**
     * Outlet
     */
    @ExcelProperty(value = "Outlet")
    private String merchantOut;

    /**
     * Gift Card Program
     */
    @ExcelProperty(value = "Gift Card Program")
    private String vpg;

    /**
     * Total Cards
     */
    @ExcelProperty(value = "Total Cards", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    /**
     * Total Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    /**
     * Customer Name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * Reactivated Reason
     */
    @ExcelProperty(value = "Reactivated Reason")
    private String reactivatedReason;
}
