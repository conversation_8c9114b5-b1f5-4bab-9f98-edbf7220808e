package com.gtech.gvcore.service.report.extend;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.gtech.basic.filecloud.oss.OssManager;
import com.gtech.basic.filecloud.oss.model.OssBucketType;
import com.gtech.basic.filecloud.oss.model.OssNamespace;
import com.gtech.basic.masterdata.web.entity.MasterDataDdLangEntity;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.utils.LocalCacheUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.helper.FileCompressionHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ReportOssHelper
 * @Description 报表oss帮助类
 * <AUTHOR>
 * @Date 2023/2/17 15:59
 * @Version V1.0
 **/
@Slf4j
@Component
public class ReportUploadHelper {

    @Setter(AccessLevel.PRIVATE)
    private static OssHelper ossHelper;

    @Setter(AccessLevel.PRIVATE)
    private static MasterDataDdLangService masterDataDdLangService;

    @Autowired
    private static OssManager ossManager;

    @Autowired
    private OssManager oss;

    @PostConstruct
    public void staticVarAssignment() {
        this.ossManager= oss;
    }

    public static final String REPORT_FILE_MAX_SIZE_LOCAL_CACHE_KEY = "report_file_max_size";
    public static final long REPORT_FILE_MAX_SIZE_LOCAL_CACHE_TIMEOUT = 30 * 1000L;
    public static final String REPORT_FILE_MAX_SIZE_DD_LANG_KEY = "REPORT_FILE_MAX_SIZE";

    private static final int DEFAULT_FILE_MAX_SIZE = 200_000;

    public ReportUploadHelper(OssHelper ossHelper, MasterDataDdLangService masterDataDdLangService) {
        setOssHelper(ossHelper);
        setMasterDataDdLangService(masterDataDdLangService);
    }

    /**
     * get excel file max size
     * @return max size
     */
    public static int getExcelFileMaxSize() {

        Integer load = LocalCacheUtil.load(REPORT_FILE_MAX_SIZE_LOCAL_CACHE_KEY, Integer.class);

        if (null != load) return load;

        MasterDataDdLangEntity entity = new MasterDataDdLangEntity();
        entity.setDdCode(REPORT_FILE_MAX_SIZE_DD_LANG_KEY);
        MasterDataDdLangEntity masterDataDdLang = masterDataDdLangService.getByCode(entity);

        if (null == masterDataDdLang) return DEFAULT_FILE_MAX_SIZE;

        LocalCacheUtil.save(REPORT_FILE_MAX_SIZE_LOCAL_CACHE_KEY, Integer.valueOf(masterDataDdLang.getDdValue()), REPORT_FILE_MAX_SIZE_LOCAL_CACHE_TIMEOUT);

        return getExcelFileMaxSize();
    }

    /**
     * inputStream to local file
     * @param inputStream
     * @param name
     * @return
     */
    public static String inputStreamToUrl(InputStream inputStream, String name) {

        if (null == inputStream) return null;

        return ossHelper.upload(inputStream, name);
    }

    /**
     * generate local file name
     * @param context report context
     * @param page page number
     * @return file name
     */
    public static String getTempFileName(ReportContext context, int page, String suffix) {

        return context.getReportExportTypeEnum().getExportName() + "_" + context.getReportCode()  + "(" + page + ")" + suffix;
    }

    /**
     * generate oss file name
     * @param reportExportTypeEnum report type
     * @param reportCode report code
     * @param suffix file suffix
     * @return file name
     */
    public static String getOssFileName(ReportExportTypeEnum reportExportTypeEnum, String reportCode, String suffix) {

        return reportExportTypeEnum.getExportName() + "_" + reportCode +suffix;
    }


    public static String fileUpload(ReportContext context, InputStream inputStream) {

        return ossHelper.upload(inputStream, getOssFileName(context.getReportExportTypeEnum(), context.getReportCode(), ExcelTypeEnum.XLSX.getValue()));
    }

    public static String fileUpload(InputStream inputStream, String fileName) {

        return ossHelper.upload(inputStream, fileName);
    }

    /**
     * file upload to oss
     * @param context report context
     * @param fileMap file path list
     * @return oss file url
     */
    public static String fileUpload(ReportContext context, Map<String, String> fileMap) {

        try {

            List<FileCompressionHelper.CompressionParameter> zipParamList = new ArrayList<>();
            fileMap.forEach((k, v) -> zipParamList.add(FileCompressionHelper.CompressionParameter.builder().fileUrl(v).fileName(k).build()));

            if (CollectionUtils.isEmpty(zipParamList)) return "";

            if (zipParamList.size() == 1) return zipParamList.get(0).getFileUrl();

            zipParamList.forEach(x -> {
                final URL url = ossManager.grantAccessUrl(OssNamespace.of(GvcoreConstants.APP_CODE, GvcoreConstants.TITAN_DOMAIN_CODE, OssBucketType.PRIVATE), x.getFileUrl());
                x.setFileUrl(url.toString());
            });
            InputStream inputStream = FileCompressionHelper.zip(zipParamList);

            return ossHelper.upload(inputStream, getOssFileName(context.getReportExportTypeEnum(), context.getReportCode(), ".zip"));

        } catch (IOException e) {

            throw new RuntimeException(e);
        }
    }

}
