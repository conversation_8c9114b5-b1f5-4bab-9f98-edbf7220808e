package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.outletcpg.*;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface OutletCpgService {

    Result<Void> createOutletCpg(CreateOutletCpgRequest param);

    Result<Void> updateOutletCpg(UpdateOutletCpgRequest param);

    Result<Void> deleteOutletCpg(DeleteOutletCpgRequest param);

    Result<Void> deleteOutletCpgByOutletCode(DeleteOutletCpgByOutletCodeRequest param);

    PageResult<OutletCpgResponse> queryOutletCpgList(QueryOutletCpgRequest param);

    OutletCpgResponse getOutletCpg(GetOutletCpgRequest param);

    List<OutletCpgResponse> queryOutletCpgListByOutlet(String outletCode);
    List<OutletCpgResponse> queryOutletCpgListByOutletList(List<String> outletCodeList);



}
