package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.CancelSalesSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.CancelSalesBo;
import com.gtech.gvcore.service.report.impl.param.CancelSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.CancelSalesBaseImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:58
 * @Description:
 */
@Service
public class CancelSalesSummaryImpl extends CancelSalesBaseImpl<CancelSalesSummaryBean>
        implements BusinessReport<CancelSalesQueryData, CancelSalesSummaryBean>, SingleReport {

    @Override
    public List<CancelSalesSummaryBean> getExportData(CancelSalesQueryData queryData) {

        Collection<CancelSalesSummaryBo> list = getList(queryData);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, CancelSalesSummaryBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, CancelSalesSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, CancelSalesSummaryBo::getOutletCode, Outlet.class);

        //convert result
        return list.stream()
                .map(e -> new CancelSalesSummaryBean()
                        .setTotalAmount(toAmount(ConvertUtils.toBigDecimal(e.getTotalAmount(), BigDecimal.ZERO)))
                        .setNumberOfVouchers(ConvertUtils.toString(e.getNumberOfVouchers()))
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setOutlet(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName()))
                .collect(Collectors.toList());
    }

    private Collection<CancelSalesSummaryBo> getList(CancelSalesQueryData queryData) {

        //find
        List<CancelSalesBo> boList = super.getBoList(queryData);

        return boList.stream()
                .map(CancelSalesSummaryBo::convert)
                .collect(Collectors.toMap(
                                // key
                                CancelSalesSummaryBo::getGroupKey,
                                // value
                                CancelSalesSummaryBo::newInstance,
                                // merge
                                CancelSalesSummaryBo::merge)
                        //to map
                ).values();
    }


    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.CANCEL_SALES_SUMMARY_REPORT;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class CancelSalesSummaryBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount = BigDecimal.ZERO;


        public static CancelSalesSummaryBo convert(CancelSalesBo bo) {

            return new CancelSalesSummaryBo()
                    .setMerchantCode(bo.getMerchantCode())
                    .setOutletCode(bo.getOutletCode())
                    .setCpgCode(bo.getCpgCode())
                    .setTotalAmount(bo.getDenomination());
        }

        public static String getGroupKey(CancelSalesSummaryBo bean) {

            return StringUtils.join("_", bean.getMerchantCode(), bean.getOutletCode(), bean.getCpgCode());
        }

        public static CancelSalesSummaryBo newInstance(CancelSalesSummaryBo bean) {

            return new CancelSalesSummaryBo()
                    .setMerchantCode(bean.getMerchantCode())
                    .setOutletCode(bean.getOutletCode())
                    .setCpgCode(bean.getCpgCode())
                    .setTotalAmount(bean.getTotalAmount());
        }

        public CancelSalesSummaryBo merge(CancelSalesSummaryBo bo) {

            numberOfVouchers += ConvertUtils.toInteger(bo.getNumberOfVouchers(), 0);
            totalAmount = totalAmount.add(bo.getTotalAmount());

            return this;
        }


    }
}
