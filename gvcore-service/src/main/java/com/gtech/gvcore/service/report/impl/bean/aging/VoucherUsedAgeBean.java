package com.gtech.gvcore.service.report.impl.bean.aging;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherUsedAgeBean
 * @Description Voucher Used Age Bean
 * <AUTHOR>
 * @Date 2022/11/2 15:13
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherUsedAgeBean {

    @ExcelProperty(value = "Details", index = 0)
    private String detail;

    @ExcelProperty(index = 1)
    @ReportLabel(value = "Used")
    private String used;

    @ExcelProperty(index = 3)
    @ReportLabel(value = {"Contribution Redemption Age of the Used Voucher ", "1 month"})
    private String auOne;

    @ExcelProperty(index = 4)
    @ReportLabel(value = {"Contribution Redemption Age of the Used Voucher ", "2 month"})
    private String auTwo;

    @ExcelProperty(index = 5)
    @ReportLabel(value = {"Contribution Redemption Age of the Used Voucher ", "3 month"})
        private String auThree;

    @ExcelProperty(index = 6)
    @ReportLabel(value = {"Contribution Redemption Age of the Used Voucher ", "4 to 6 month"})
    private String auFour;

    @ExcelProperty(index = 7)
    @ReportLabel(value = {"Contribution Redemption Age of the Used Voucher ", "6 to 12 month"})
    private String auSix;

}
