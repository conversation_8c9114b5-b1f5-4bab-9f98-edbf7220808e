package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.CustomerMapper;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName CustomerQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:47
 * @Version V1.0
 **/
@Component
public class CustomerQueryImpl implements QuerySupport<Customer> {

    private static final Customer EMPTY = new Customer();

    @Autowired
    private CustomerMapper customerMapper;

    @Override
    public List<Customer> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Customer.class);
        example.createCriteria().andIn(Customer.C_CUSTOMER_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Customer> customers = customerMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(customers)) return Collections.emptyList();

        return customers;
    }

    @Override
    public Function<Customer, String> codeMapper() {
        return Customer::getCustomerCode;
    }

    @Override
    public Customer emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Customer> supportType() {
        return Customer.class;
    }
}
