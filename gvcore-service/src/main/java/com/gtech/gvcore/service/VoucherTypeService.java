package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.vouchertype.*;
import com.gtech.gvcore.common.response.vouchertype.VoucherTypeResponse;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface VoucherTypeService {

    Result<Void> createVoucherType(CreateVoucherTypeRequest param);

    Result<Void> updateVoucherType(UpdateVoucherTypeRequest param);

    Result<Void> deleteVoucherType(DeleteVoucherTypeRequest param);

    PageResult<VoucherTypeResponse> queryVoucherTypeList(QueryVoucherTypeRequest param);

    VoucherTypeResponse getVoucherType(GetVoucherTypeRequest param);


    Result<Void> updateVoucherTypeStatus(UpdateVoucherTypeStatusRequest param);
}
