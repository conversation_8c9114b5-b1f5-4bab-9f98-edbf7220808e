package com.gtech.gvcore.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.mapper.SysLoggerMapper;
import com.gtech.gvcore.dao.model.SysLogger;
import com.gtech.gvcore.helper.RedisLockHelper;
import com.gtech.gvcore.service.SystemLoggerService;
import com.gtech.gvcore.service.report.impl.param.AuditTrailReportQueryData;

import cn.hutool.core.bean.BeanUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName SystemLoggerServiceImpl
 * @Description 系统日志服务
 * <AUTHOR>
 * @Date 2022/10/20 10:42
 * @Version V1.0
 **/
@Service
@EnableScheduling
public class SystemLoggerServiceImpl implements SystemLoggerService {

    @Autowired
    private SysLoggerMapper loggerMapper;

    @Override
	public SysLogger getLoggerById(int id, Date operationTime) {

		return loggerMapper.selectOne(getTableIndex(operationTime), id);
	}

	private int getTableIndex(Date date) {
		return date.getMonth() + 1;
	}

	private int getReportTableIndex(Date date) {
		return (date.getMonth() + 1) %3;
	}

	public static final String SCHEDULER_SYS_LOGGER_TRUNCATE_KEY = "SCHEDULER:SYS_LOGGER_TRUNCATE_TASK";

	// 每个月的28号晚上执行3次,防止一次截断失败
	@Scheduled(cron = "59 45,50,55 23 28 * ?")
	public void truncate() {
		// 锁定业务 30秒
		boolean lock = RedisLockHelper.tryLock(SCHEDULER_SYS_LOGGER_TRUNCATE_KEY, 3 * 1000L, 30 * 1000L);
		if (!lock) {
			return;
		}
		// 截断下个月的表
		Date date = DateUtil.addMonth(1);
		loggerMapper.truncate(getTableIndex(date));

		loggerMapper.truncateReportTable(getReportTableIndex(date));
    }

    @Override
    public List<SysLogger> queryLoggerReport(AuditTrailReportQueryData queryData) {

		if (queryData.getOperateTimeBegin() == null) {
			return Collections.emptyList();
		}
		PageHelper.startPage(queryData.getPageNum(), queryData.getPageSize(), false);
		if (queryData.getFindEndTime() != null) {
			queryData.setOperateTimeBegin(GvDateUtil.min(queryData.getOperateTimeBegin(), queryData.getFindEndTime()));
		}
		Map<String, Object> map = BeanUtil.beanToMap(queryData);
		map.put("month", getTableIndex(queryData.getOperateTimeBegin()));
		return this.loggerMapper.query(map);
    }

    @Override
    public void addSysLogger(SysLogger sysLogger) {

		Date now = new Date();
		sysLogger.setCreateTime(now);
		int month = getTableIndex(now);
		loggerMapper.insert(month, sysLogger);
    }

    private Example getLoggerReportExample(AuditTrailReportQueryData queryData) {
        Example example = new Example(SysLogger.class, true);

        example.createCriteria()
                .andEqualTo(SysLogger.C_ID, queryData.getRequestId())
                .andLessThan(SysLogger.C_CREATE_TIME, queryData.getOperateTimeEnd())
                .andGreaterThanOrEqualTo(SysLogger.C_CREATE_TIME, GvDateUtil.min(queryData.getOperateTimeBegin(), queryData.getFindEndTime()))
                .andEqualTo(SysLogger.C_USER_CODE, ConvertUtils.toString(queryData.getUserCode(), null));

        example.selectProperties(SysLogger.C_ID, SysLogger.C_USER_CODE, SysLogger.C_CREATE_TIME, SysLogger.C_REQUEST_PATH);
        return example;
    }

}
