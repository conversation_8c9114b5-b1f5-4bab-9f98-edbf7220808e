package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.SalesSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.SalesAnyVoucherCodeBo;
import com.gtech.gvcore.service.report.impl.bo.SalesBo;
import com.gtech.gvcore.service.report.impl.bo.SalesVoucherToRemarkBo;
import com.gtech.gvcore.service.report.impl.param.SalesQueryData;
import com.gtech.gvcore.service.report.impl.support.SalesBaseImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:01
 * @Description: 0.24 - 1.02 59.58s,
 */
@Service
public class SalesSummaryImpl extends SalesBaseImpl<SalesSummaryBean>
        implements BusinessReport<SalesQueryData, SalesSummaryBean>, SingleReport {

    @Autowired protected IssueHandlingService issueHandlingService;
    @Autowired protected CustomerOrderMapper customerOrderMapper;
    @Autowired protected TransactionDataMapper transactionDataMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.SALES_SUMMARY_REPORT;
    }

    @Override
    protected void addParam(SalesQueryData param, CreateReportRequest reportParam) {

        // po transaction id
        List<String> poTransactionIdList = getTransactionIdByPurchaseOrder(reportParam);
        // order status transaction id
        List<String> statusTransactionIdList = getTransactionIdByOrderStatus(reportParam);

        // merge
        List<String> transactionIdList = new ArrayList<>(poTransactionIdList);
        transactionIdList.addAll(statusTransactionIdList);

        // set
        param.setTransactionIdList(transactionIdList.stream().distinct().collect(Collectors.toList()));
    }

    private List<String> getTransactionIdByOrderStatus(CreateReportRequest reportParam) {

        // order status
        List<String> orderStatuses = reportParam.getOrderStatuses();

        // empty
        if (CollectionUtils.isEmpty(orderStatuses)) return Collections.emptyList();

        // select
        List<CustomerOrder> customerOrderList = customerOrderMapper.selectByCondition(Example.builder(CustomerOrder.class)
                .where(Sqls.custom().andIn(CustomerOrder.C_STATUS, orderStatuses))
                .build());

        // filter
        return Optional.ofNullable(customerOrderList)
                .map(e -> e.stream().map(CustomerOrder::getCustomerOrderCode).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 根据采购单号查询交易单号
     * @param reportParam
     * @return
     */
    private List<String> getTransactionIdByPurchaseOrder(final CreateReportRequest reportParam) {

        // purchaseOrderNo
        final String purchaseOrderNo = reportParam.getPurchaseOrderNo();

        // empty
        if (StringUtils.isBlank(purchaseOrderNo)) return Collections.emptyList();

        // select
        List<CustomerOrder> customerOrderList = customerOrderMapper.selectByCondition(Example.builder(CustomerOrder.class)
                .where(Sqls.custom().andEqualTo(CustomerOrder.C_PURCHASE_ORDER_NO, purchaseOrderNo))
                .build());

        // filter
        return Optional.ofNullable(customerOrderList)
                .map(e -> e.stream().map(CustomerOrder::getCustomerOrderCode).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }


    @Override
    public List<SalesSummaryBean> getExportData(List<SalesBo> boList) {

        final Map<String, SalesSummaryBo> resultMap = new HashMap<>();

        boList.forEach(e -> {

            final String key = StringUtils.join(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_DEFAULT), e.getTransactionId()
                    , e.getMerchantCode(), e.getOutletCode(), e.getCpgCode(), e.getApproveCode(), e.getInvoiceNumber());

            final SalesSummaryBo salesSummaryBo = resultMap.computeIfAbsent(key, k -> SalesSummaryBo.newInstance(e));

            salesSummaryBo.addSalesCount().addSalesAmount(e.getDenomination());
        });
       
        final List<SalesSummaryBo> resultList = new ArrayList<>(resultMap.values());

        final JoinDataMap<Outlet> outletMap = super.getMapByCode(resultList, SalesSummaryBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(resultList, SalesSummaryBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(resultList, SalesSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);
        final Map<String, String> remarkMap = this.getIssuerHandlingRemarkMap(resultList);

        return resultList.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    return new SalesSummaryBean()
                            .setDate(e.getDate())
                            .setSubCompanyName(company.getCompanyName())
                            .setMerchantName(merchant.getMerchantName())
                            .setMerchantOutletName(outlet.getOutletName())
                            .setMerchantOutletCode(outlet.getBusinessOutletCode())
                            .setApprovalCode(e.getApprovalCode())
                            .setBillNumber(e.getInvoiceNumber())
                            .setSalesCount(e.getSalesCount().toString())
                            .setSalesAmount(super.toAmount(e.getSalesAmount()))
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setNotes(remarkMap.getOrDefault(e.getTransactionId(), StringUtils.EMPTY));
                }).collect(Collectors.toList());
    }


    private Map<String, String> getIssuerHandlingRemarkMap(final List<SalesSummaryBo> resultList) {

        final Map<String, String> resultMap = new ConcurrentHashMap<>();

        final List<String> transactionIdList = resultList.stream()
                .filter(e -> e.getSalesCount() > 0)
                .map(SalesSummaryBo::getTransactionId).distinct().collect(Collectors.toList());

        ListUtils.partition(transactionIdList, 1000)
                .parallelStream()
                .forEach(e -> Optional.of(e)
                        //select voucher code
                        .map(list -> list.parallelStream()
                                .map(code -> new SalesAnyVoucherCodeBo().setTransactionId(code)
                                        .setVoucherCode(
                                                Optional.ofNullable(transactionDataMapper.selectByExampleAndRowBounds(
                                                        Example.builder(TransactionData.class)
                                                                        .where(Sqls.custom().andEqualTo(TransactionData.C_TRANSACTION_ID, code))
                                                                        .select(TransactionData.C_VOUCHER_CODE)
                                                                        .orderByDesc(TransactionData.C_CREATE_TIME)
                                                                        .build()
                                                                , GvPageHelper.LIMIT_1))
                                                        .filter(CollectionUtils::isNotEmpty)
                                                        .map(t -> t.get(0))
                                                        .map(TransactionData::getVoucherCode)
                                                        .orElse(StringUtils.EMPTY)
                                        )).collect(Collectors.toList())
                        // filter
                        ).filter(CollectionUtils::isNotEmpty)
                        .ifPresent(list -> {
                            //select remark
                            final List<SalesVoucherToRemarkBo> remarkList = reportBusinessMapper.selectSalesIssuerHandlingRemarkByVoucherCode(list.stream().map(SalesAnyVoucherCodeBo::getVoucherCode).collect(Collectors.toList()));
                            final Map<String, String> remarkMap = remarkList.stream().collect(Collectors.toMap(SalesVoucherToRemarkBo::getVoucherCode, SalesVoucherToRemarkBo::getRemark));
                            //match
                            list.forEach(v -> remarkMap.put(v.getTransactionId(), remarkMap.getOrDefault(v.getVoucherCode(), StringUtils.EMPTY)));
                        })
                );

        return resultMap;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SalesSummaryBo {

        private String date;

        private String transactionId;

        private String merchantCode;

        private String outletCode;

        private String approvalCode;

        private String invoiceNumber;

        private String cpgCode;

        private Integer salesCount = 0;

        private BigDecimal salesAmount = BigDecimal.ZERO;

        public static SalesSummaryBo newInstance(SalesBo bo) {

            return newInstance(DateUtil.format(bo.getTransactionDate(), DateUtil.FORMAT_DEFAULT), bo.getTransactionId(), bo.getMerchantCode(), bo.getOutletCode(), bo.getApproveCode(), bo.getInvoiceNumber(), bo.getCpgCode());
        }

        public static SalesSummaryBo newInstance(String date, String transactionId, String merchantCode, String outletCode, String approvalCode, String invoiceNumber, String cpgCode) {

            return new SalesSummaryBo()
                    .setDate(date)
                    .setTransactionId(transactionId)
                    .setMerchantCode(merchantCode)
                    .setOutletCode(outletCode)
                    .setApprovalCode(approvalCode)
                    .setInvoiceNumber(invoiceNumber)
                    .setCpgCode(cpgCode);
        }

        public SalesSummaryBo addSalesCount() {
            this.salesCount += 1;
            return this;
        }

        public void addSalesAmount(BigDecimal amount) {
            this.salesAmount = this.salesAmount.add(amount);
        }

    }

}
