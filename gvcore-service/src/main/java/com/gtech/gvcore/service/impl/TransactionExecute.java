package com.gtech.gvcore.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ApproveNodeRecordTypeEnum;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveStatusEnum;
import com.gtech.gvcore.common.request.customerorder.GetCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.request.customerorder.ReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.ReleaseRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.receive.GetReceiveByCustomerOrderRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.senddigitalvoucherexceltoemail.SendDigitalVoucherExcelToEmailRequest;
import com.gtech.gvcore.common.request.voucherbatch.GenerateDigitalVouchersRequest;
import com.gtech.gvcore.config.SelfActivationProperties;
import com.gtech.gvcore.service.SelfActivationService;
import com.gtech.gvcore.common.response.allocation.VoucherAllocationBatchResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderEmailResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderResponse;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import com.gtech.gvcore.common.response.useraccount.UserAccountResponse;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveResponse;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderEmailMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderReceiverMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.CustomerOrderEmail;
import com.gtech.gvcore.dao.model.CustomerOrderReceiver;
import com.gtech.gvcore.dao.model.GvOperateLogEntity;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.helper.CustomerOrderPdfHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvOperateLogService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.MeansOfPaymentService;
import com.gtech.gvcore.service.ReleaseApproveService;
import com.gtech.gvcore.service.VoucherAllocationBatchService;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherBatchService;
import com.gtech.gvcore.service.VoucherReceiveService;
import com.gtech.gvcore.service.VoucherService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.util.StringUtil;

/**
 * <AUTHOR>
 * @Date 2023/7/15 16:31
 */
@Component
@Slf4j
public class TransactionExecute {


    @Lazy
    @Autowired
    private CustomerOrderService customerOrderService;

    @Autowired
    private CustomerOrderReceiverMapper customerOrderReceiverMapper;
    @Autowired
    private ReleaseApproveService releaseApproveService;
    @Autowired
    private ApproveNodeRecordMapper approveNodeRecordMapper;
    @Autowired
    private VoucherAllocationService voucherAllocationService;
    @Autowired
    private VoucherBatchService voucherBatchService;
    @Lazy
    @Autowired
    private VoucherReceiveService voucherReceiveService;

    @Lazy
    @Autowired
    private VoucherService voucherService;

    @Autowired
    private FlowNoticeService flowNoticeService;
    @Autowired
    private GvUserAccountService userAccountService;
    @Autowired
    private CustomerOrderPdfHelper customerOrderPdfHelper;

    @Autowired
    private VoucherAllocationBatchService voucherAllocationBatchService;
    @Autowired
    private GvOperateLogService operateLogService;


    @Autowired
    private MeansOfPaymentService mopService;

    @Autowired
    private CpgService cpgService;

    @Autowired
    private GvCodeHelper gvCodeHelper;




    @Autowired
    private CustomerOrderEmailMapper customerOrderEmailMapper;

    @Autowired
    private SelfActivationService selfActivationService;

    @Autowired
    private SelfActivationProperties selfActivationProperties;


    private static final ThreadPoolExecutor EXECUTOR =
            new ThreadPoolExecutor(10, 50, 100, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
                    new ThreadPoolExecutor.CallerRunsPolicy());

    @Transactional(rollbackFor = Exception.class)
    public void issuanceExecute(IssuanceRequest request, CustomerOrder customerOrder, List<CustomerOrderDetails> details) {
        log.info("issuance 开始执行：{}", JSON.toJSONString(customerOrder));
        Result<String> success;

        try {
            if (GvcoreConstants.MOP_CODE_VCR.equals(customerOrder.getMopCode())) {
                success =  issuanceVcr(request, customerOrder, details);
            } else if (GvcoreConstants.MOP_CODE_VCE.equals(customerOrder.getMopCode())) {
                success = issuanceVce(request, customerOrder, details);
            }else {
                throw new GTechBaseException("未知的MOP ",new Exception());
            }
        } catch (Exception e) {
            throw new GTechBaseException("VCR 发行过程中发生错误: " + e.getMessage(), e);
        }

        ApproveNodeRecordRequest approveNodeRecordRequest = createApproveNodeRecordRequest(customerOrder);
        releaseApproveService.automaticApproveAndNoticeNextNode(approveNodeRecordRequest, x -> customerOrderService.approveCustomerOrderRelease(approveNodeRecordRequest));

        if (success.isSuccess()) {
            customerOrderService.updateStatus(customerOrder.getCustomerOrderCode(),
                    CustomerOrderStatusEnum.ISSUANCE.getStatus(),
                    CustomerOrderStatusEnum.LOADING.getStatus(),
                    request.getUpdateUser());

        }
    }

    private ApproveNodeRecordRequest createApproveNodeRecordRequest(CustomerOrder customerOrder) {
        ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
        approveNodeRecordRequest.setIssuerCode(customerOrder.getIssuerCode());
        approveNodeRecordRequest.setBusinessCode(customerOrder.getCustomerOrderCode());
        approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType());
        approveNodeRecordRequest.setVoucherAmount(customerOrder.getVoucherAmount());
        approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.RELEASE.getType());
        approveNodeRecordRequest.setExtendParams(getExtendsParams(customerOrder.getCustomerOrderCode()));
        return approveNodeRecordRequest;
    }



    /*@Transactional(rollbackFor = Exception.class)
    public void issuanceExecute(IssuanceRequest request, CustomerOrder customerOrder, List<CustomerOrderDetails> details) {

		log.info("issuance 开始执行：{}", JSON.toJSONString(customerOrder));
        int i = loadingOrder(request.getCustomerOrderCode(), CustomerOrderStatusEnum.APPROVAL);

        if (i == 0) {
            log.error("修改订单状态失败---------------------------------------issuance");
        }
        Result<String> resultVcr = null;
        if (GvcoreConstants.MOP_CODE_VCR.equals(customerOrder.getMopCode())) {
            try {
                resultVcr = issuanceVcr(request, customerOrder, details);
            } catch (Exception e) {
                updateStatus(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.APPROVAL.getStatus(),
                        CustomerOrderStatusEnum.LOADING.getStatus(), request.getUpdateUser());
                throw new RuntimeException(e);
            }
        } else {
            resultVcr = issuanceVce(request, customerOrder, details);
        }


        *//*ApproveNodeRecordRequest approveNodeRecordRequest = createApproveNodeRecordRequest(customerOrder);
        releaseApproveService.automaticApproveAndNoticeNextNode(approveNodeRecordRequest, x -> customerOrderService.approveCustomerOrderRelease(approveNodeRecordRequest));
*//*

        Integer updateResult = null;
        if (resultVcr.isSuccess()) {
            updateResult = updateStatus(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.ISSUANCE.getStatus(),
                    CustomerOrderStatusEnum.LOADING.getStatus(), request.getUpdateUser());
        } else {
            log.error("{} vcr issuance error",request.getCustomerOrderCode());
        }

        if (updateResult == 0) {
            log.error("{} 修改订单状态失败---------------------------------------issuance",request.getCustomerOrderCode());
        } else {
            log.info("{} 修改订单状态成功---------------------------------------issuance",request.getCustomerOrderCode());
        }
    }*/


    private Result<String> issuanceVcr(IssuanceRequest request, CustomerOrder customerOrder, List<CustomerOrderDetails> details) {
        log.info("issuanceVcr {}开始执行",request.getCustomerOrderCode());
        Result<String> allocateResult = voucherAllocationService.allocateByCustomerOrder(request, customerOrder, details);
        if (!allocateResult.isSuccess()) {
            log.info("allocateByCustomerOrderfailed CustomerOrderCode={}, allocateResult={}",
                    customerOrder.getCustomerOrderCode(), JSON.toJSONString(allocateResult));
            throw new GTechBaseException(allocateResult.getCode(), "Issuance failed, {0}",
                    allocateResult.getMessage());
        }
        return allocateResult;

    }

    @Transactional(rollbackFor = Exception.class)
    public void releaseExecute(ReleaseRequest request, CustomerOrder customerOrder) {
        try {

            CustomerOrderDto dto = new CustomerOrderDto();
            dto.setUpdateTime(new Date());
            if (StringUtils.isBlank(customerOrder.getSalesNoteUrl())) {

                String salesNotePdfUrl = customerOrderPdfHelper.generateSalesNoteHtml(customerOrder,
                        customerOrderService.queryCustomerOrderDetails(request.getCustomerOrderCode()),
                        this.getUserName(customerOrder.getUpdateUser()));
                dto.setSalesNoteUrl(salesNotePdfUrl);
            }

            boolean isRelease = Boolean.TRUE.equals(request.getStatus());
            String status = null;
            String flowNodeCode = null;
            if (isRelease) {
                status = CustomerOrderStatusEnum.RELEASE.getStatus();
                flowNodeCode = FlowNodeEnum.RELEASE.getCode();
                if (GvcoreConstants.MOP_CODE_VCE.equals(customerOrder.getMopCode())) {
                    status = CustomerOrderStatusEnum.COMPLETED.getStatus();
                    flowNodeCode = FlowNodeEnum.COMPLETED.getCode();
                }
                dto.setReleaseTime(dto.getUpdateTime());
            } else {
                status = CustomerOrderStatusEnum.REJECTED.getStatus();
                flowNodeCode = FlowNodeEnum.REJECTED.getCode();
            }
            dto.setCustomerOrderCode(customerOrder.getCustomerOrderCode());
            dto.setStatus(status);
            dto.setOldStatus(CustomerOrderStatusEnum.LOADING.getStatus());
            dto.setUpdateUser(request.getUpdateUser());


             updateVoucherByRelease(request, customerOrder, isRelease);

             log.info("券Release完毕，开始发送邮件{}",JSON.toJSONString(customerOrder));
            if (CustomerOrderStatusEnum.REJECTED.getStatus().equals(status)) {
                releaseApproveService.updateByDeleted(customerOrder.getCustomerOrderCode(), request.getUpdateUser(),
                        ApproveNodeRecordTypeEnum.RELEASE.getType());
            }


            String finalFlowNodeCode = flowNodeCode;

            EXECUTOR.execute(()->{
                // Submiter
                List<String> emails = getOperateEmail(customerOrder.getCustomerOrderCode(), GvcoreConstants.OPERATE_METHOD_SUBMIT, !isRelease);
                sendEmail(customerOrder.getCustomerOrderCode(), finalFlowNodeCode, emails.stream().distinct().collect(Collectors.toList()));
                sendDigitalVoucherExcelToEmail(customerOrder, request, isRelease);
            });

            log.info("邮件发送完毕，开始修改状态为完成{}",JSON.toJSONString(customerOrder));

            int i = customerOrderService.updateByRelease(dto);
            //TODO 修改为失败状态
            if (i == 0) {
                log.error("修改订单状态失败---------------------------------------release");
            }


        } catch (Exception e) {
            log.error("修改订单状态失败---------------------------------------release");
            log.error("release failed, {}", e.getMessage());
            throw e;


        }
    }

    /**
     * @param customerOrder
     * @param request
     * @param isRelease
     * <AUTHOR>
     * @date 2022年4月22日
     */
    private void sendDigitalVoucherExcelToEmail(CustomerOrder customerOrder, ReleaseRequest request,
                                                boolean isRelease) {

        if (GvcoreConstants.MOP_CODE_VCE.equals(customerOrder.getMopCode()) && isRelease) {
            SendDigitalVoucherExcelToEmailRequest emailRequest = new SendDigitalVoucherExcelToEmailRequest();
            emailRequest.setVoucherBatchCode(customerOrder.getVoucherBatchCode());
            emailRequest.setInvoiceNumber(customerOrder.getInvoiceNo());
            emailRequest.setEmail(customerOrder.getContactEmail());
            emailRequest.setCustomerOrderCode(customerOrder.getCustomerOrderCode());
            emailRequest.setCreateUser(request.getUpdateUser());
            try {
                voucherBatchService.sendDigitalVoucherExcelToEmail(emailRequest);
            } catch (Exception e) {
                log.warn("sendDigitalVoucherExcelToEmailException voucherBatchCode="
                        + customerOrder.getVoucherBatchCode() + ", Exception=" + e.getMessage(), e);
            }
        }
    }
    private GvOperateLogEntity getOperateLog(String customerOrderCode, String method) {
        List<GvOperateLogEntity> list = operateLogService.queryLogByBusiness(customerOrderCode);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (GvOperateLogEntity entity : list) {
            if (method.equals(entity.getMethod())) {
                return entity;
            }
        }
        return null;
    }

    /**
     * 检查客户订单是否可以取消。
     * 首先判断客户订单的状态是否等于CREATED、SUBMIT、APPROVAL、ISSUANCE、REJECTED中的一种，如果是，则返回true，表示可以取消；
     * 如果客户订单的状态是RELEASE、RECEIVE、DELIVER中的一种，则查询最新的审批节点记录，如果记录存在且创建时间和今天相同
     * MER-1957 新增 Completed 状态订单，增加Cancel按钮
     *
     * @param customerOrder 客户订单
     * @return true：可以取消；false：不可以取消
     */
    private boolean checkCancelAble(CustomerOrder customerOrder) {
        if (CustomerOrderStatusEnum.CREATED.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.SUBMIT.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.APPROVAL.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.ISSUANCE.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.REJECTED.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.COMPLETED.getStatus().equals(customerOrder.getStatus())
        ) {
            return true;
        }
        if (CustomerOrderStatusEnum.RELEASE.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.RECEIVE.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.DELIVER.getStatus().equals(customerOrder.getStatus())) {
            ApproveNodeRecord approveNodeRecord = approveNodeRecordMapper.selectNewNote(customerOrder.getCustomerOrderCode(), ApproveNodeRecordTypeEnum.RELEASE.getType());
            if (approveNodeRecord == null || approveNodeRecord.getCreateTime() == null) {
                return false;
            }
            return Integer.parseInt(DateUtil.compareDateWithToday(approveNodeRecord.getCreateTime())) == 0;
        }
        return false;
    }
    public Result<GetCustomerOrderResponse> getCustomerOrder(GetCustomerOrderRequest getCustomerOrderRequest) {
        CustomerOrder customerOrder = customerOrderService.getCustomerOrder(getCustomerOrderRequest.getCustomerOrderCode());
        GetCustomerOrderResponse getCustomerOrderResponse = BeanCopyUtils.jsonCopyBean(customerOrder, GetCustomerOrderResponse.class);
        getCustomerOrderResponse.setCancelAble(checkCancelAble(customerOrder));
        getCustomerOrderResponse.setReleaseAble(false);
        if (CustomerOrderStatusEnum.ISSUANCE.getStatus().equals(customerOrder.getStatus()) && !StringUtil.isEmpty(getCustomerOrderRequest.getRoleList())) {
            ReleaseApproveAbleRequest build = ReleaseApproveAbleRequest
                    .builder()
                    .releaseType(ApproveNodeRecordTypeEnum.RELEASE.getType())
                    .approveRoleCode(getCustomerOrderRequest.getRoleList())
                    .approveUser(getCustomerOrderRequest.getUserCode())
                    .voucherAmount(customerOrder.getVoucherAmount())
                    .releaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType())
                    .businessCode(customerOrder.getCustomerOrderCode())
                    .issuerCode(customerOrder.getIssuerCode())
                    .build();
            Result<Integer> booleanResult = releaseApproveService.approveAble(build);
            getCustomerOrderResponse.setReleaseAble(booleanResult.isSuccess());
        }
        List<GetCustomerOrderDetailsResponse> getCustomerOrderDetailsResponses = customerOrderService
                .selectCustomerDetails(getCustomerOrderResponse.getCustomerOrderCode(), GvcoreConstants.DELETE_STATUS_DISABLE);
        getCustomerOrderResponse.setGetCustomerOrderDetailsResponses(getCustomerOrderDetailsResponses);
        VoucherAllocation voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(customerOrder.getCustomerOrderCode(), VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
        if (voucherAllocationByCode != null) {
            getCustomerOrderResponse.setVoucherAllocationCode(voucherAllocationByCode.getVoucherAllocationCode());
        }
        CustomerOrderReceiver customerOrderReceiver = customerOrderReceiverMapper.selectOne(new CustomerOrderReceiver(getCustomerOrderRequest.getCustomerOrderCode()));
        Result<List<ApproveNodeRecordResponse>> listResult = releaseApproveService.queryLogByBusinessCode(customerOrder.getCustomerOrderCode(), null);
        if (listResult == null) {
            return Result.ok(getCustomerOrderResponse);
        }
        getCustomerOrderResponse.setNotes(listResult.getData());
        BeanCopyUtils.copyProps(customerOrderReceiver, getCustomerOrderResponse);
        getCustomerOrderResponse.setQuotationUrl(customerOrderReceiver.getQuotation());
        getCustomerOrderResponse.setInvoiceUrl(customerOrderReceiver.getInvoice());
        getCustomerOrderResponse.setSalesOrderUrl(customerOrderReceiver.getSalesOrder());

        if (StringUtil.isNotEmpty(customerOrderReceiver.getPaymentVoucher())) {
            getCustomerOrderResponse.setPaymentVoucherUrl(customerOrderReceiver.getPaymentVoucher());
            Map<String, String> stringStringMap = userAccountService.queryFullNameByCodeList(Collections.singletonList(customerOrderReceiver.getUpdateUser()));
            if (stringStringMap.get(customerOrderReceiver.getUpdateUser()) != null) {
                getCustomerOrderResponse.setPaymentVoucherUploader(stringStringMap.get(customerOrderReceiver.getUpdateUser()));
            } else {
                getCustomerOrderResponse.setPaymentVoucherUploader(customerOrderReceiver.getUpdateUser());
            }
        }

        //cancelAble
        if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {
            getCustomerOrderResponse.setCancelAble(isCancel(customerOrder.getUpdateTime()));
        }
        getAlloctionBatchList(getCustomerOrderResponse);

        this.getSendEmailList(getCustomerOrderResponse);

        return Result.ok(getCustomerOrderResponse);
    }

    /**
     * 该方法获得 客户订单所关联的邮件发送记录
     *
     * @param customerOrder
     */
    private void getSendEmailList(GetCustomerOrderResponse customerOrder) {

        //null
        if (null == customerOrder) return;

        //find
        List<CustomerOrderEmail> result = customerOrderEmailMapper.select(new CustomerOrderEmail()
                .setCustomerOrderCode(ConvertUtils.toString(customerOrder.getCustomerOrderCode(), "")));

        //is empty
        if (CollectionUtils.isEmpty(result)) return;

        //setting result
        customerOrder.setSendEmailList(BeanCopyUtils.jsonCopyList(result, GetCustomerOrderEmailResponse.class));
    }


    private static Boolean isCancel(Date updateTime) {
        Date date = DateUtils.addDays(updateTime, 1);
        Date date1 = DateUtils.truncate(date, Calendar.DATE);
        Date date2 = DateUtils.addSeconds(date1, -1);
        Boolean before = new Date().before(date2);
        return before;
    }
    private void getAlloctionBatchList(GetCustomerOrderResponse getCustomerOrderResponse) {
        String allocationCode = getCustomerOrderResponse.getVoucherAllocationCode();
        if (StringUtil.isEmpty(allocationCode)) {
            return;
        }
        List<VoucherAllocationBatch> alloctionBatchList = voucherAllocationBatchService.queryByVoucherAllocationCode(allocationCode);
        if (CollectionUtils.isEmpty(alloctionBatchList)) {
            return;
        }
        List<String> createUsers = alloctionBatchList.stream().map(VoucherAllocationBatch::getCreateUser).distinct().collect(Collectors.toList());
        List<String> cpgCodeList = alloctionBatchList.stream().map(VoucherAllocationBatch::getCpgCode).collect(Collectors.toList());
        Map<String, Cpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(cpgCodeList);
        Map<String, String> userMap = userAccountService.queryFullNameByCodeList(createUsers);
        List<VoucherAllocationBatchResponse> alloctionBatchRespList = BeanCopyUtils.jsonCopyList(alloctionBatchList, VoucherAllocationBatchResponse.class);
        for (VoucherAllocationBatchResponse voucherAllocationBatchResponse : alloctionBatchRespList) {
            String createUser = voucherAllocationBatchResponse.getCreateUser();
            createUser = StringUtil.isEmpty(userMap.get(createUser)) ? createUser : userMap.get(createUser);
            voucherAllocationBatchResponse.setCreateUser(createUser);
            String cpgCode = voucherAllocationBatchResponse.getCpgCode();
            Cpg cpg = cpgMap.get(cpgCode);
            if (cpg != null) {
                voucherAllocationBatchResponse.setCpgName(cpg.getCpgName());
            }
        }
        getCustomerOrderResponse.setAlloctionBatchList(alloctionBatchRespList);

    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> getExtendsParams(String customerOrderCode) {
        GetCustomerOrderRequest getCustomerOrderRequest = new GetCustomerOrderRequest();
        getCustomerOrderRequest.setCustomerOrderCode(customerOrderCode);
        Result<GetCustomerOrderResponse> customerOrderResult = getCustomerOrder(getCustomerOrderRequest);
        GetCustomerOrderResponse customerOrder = customerOrderResult.getData();
        if (StringUtil.isEmpty(customerOrder.getDiscountType())) {
            customerOrder.setDiscountType(null);
        }
        String meansOfPaymentsCode = customerOrder.getMeansOfPaymentCode();
        Map<String, Object> params = new HashMap<>();
        if (!StringUtil.isEmpty(meansOfPaymentsCode)) {
            QueryMeansOfPaymentsByPageResponse meansOfPaymentsByPageResponse = mopService.getMeansOfPayments(meansOfPaymentsCode);
            params.putAll(JSON.parseObject(JSON.toJSONString(meansOfPaymentsByPageResponse), Map.class));
        }
        params.putAll(JSON.parseObject(JSON.toJSONString(customerOrder), Map.class));
        params.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(customerOrder.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        params.put(VoucherBatch.C_UPDATE_TIME, DateUtil.format(customerOrder.getUpdateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        GvOperateLogEntity submitLog = getOperateLog(customerOrderCode, GvcoreConstants.OPERATE_METHOD_ISSUANCE);
        if (submitLog != null) {
            params.put("submitBy", submitLog.getFirstName() + " " + submitLog.getLastName());
        }
        GvOperateLogEntity issuanceLog = getOperateLog(customerOrderCode, GvcoreConstants.OPERATE_METHOD_ISSUANCE);
        if (issuanceLog != null) {
            params.put("issuanceTime", DateUtil.format(issuanceLog.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        }
        String discountType = customerOrder.getDiscountType();
        if (StringUtil.isNotEmpty(discountType) && ProductCategoryDiscountTypeEnum.PERCENTAGE.code().equals(discountType)) {
            params.put("discount", customerOrder.getDiscount().setScale(1) + "%");
        }
        return params;
    }

//	@Transactional(rollbackFor = Exception.class)
    public void receiveExecute(ReceiveRequest request, CustomerOrder customerOrder) {
        log.info("receive开始---------------------------------------receive");
        Integer receiveNum = voucherReceiveService.customerOrderReceive(customerOrder.getCustomerOrderCode(), request.getUpdateUser());
        GetReceiveByCustomerOrderRequest receiveByCustomerOrderRequest = new GetReceiveByCustomerOrderRequest();
        receiveByCustomerOrderRequest.setCustomerOrderCode(customerOrder.getCustomerOrderCode());
        VoucherReceiveResponse voucherReceiveByCustomerOrder = voucherReceiveService.getVoucherReceiveByCustomerOrder(receiveByCustomerOrderRequest);
        Integer status = voucherReceiveByCustomerOrder.getStatus();
        if (!status.equals(VoucherReceiveStatusEnum.COMPLETED.getCode())){
            log.error("订单接收未完成---------------------------------------receive-已接受数量-{}",receiveNum);
            customerOrderService.rollbackOrder(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.valueOfCode(customerOrder.getStatus()));
            return;
        }

        // Check if this order requires delayed self-activation
        if (shouldUseDelayedActivation(customerOrder)) {
            log.info("Order {} requires delayed self-activation, creating self-activation task", customerOrder.getCustomerOrderCode());
            try {
                // Create self-activation task instead of completing the order immediately
                String customerEmail = customerOrder.getContactEmail();
                if (StringUtil.isNotEmpty(customerEmail)) {
                    selfActivationService.createSelfActivationTask(customerOrder.getCustomerOrderCode(), customerEmail);
                    log.info("Self-activation task created successfully for order: {}", customerOrder.getCustomerOrderCode());

                    // Update order status to COMPLETED but vouchers remain in CREATED status
                    int i1 = customerOrderService.updateStatus(customerOrder.getCustomerOrderCode(),
                            CustomerOrderStatusEnum.COMPLETED.getStatus(),
                            CustomerOrderStatusEnum.LOADING.getStatus(),
                            request.getUpdateUser());
                    if (i1 == 0) {
                        log.error("修改订单状态失败---------------------------------------receive");
                    }
                    return;
                } else {
                    log.warn("Customer email not found for order {}, proceeding with normal activation", customerOrder.getCustomerOrderCode());
                }
            } catch (Exception e) {
                log.error("Failed to create self-activation task for order: " + customerOrder.getCustomerOrderCode(), e);
                // Fall through to normal processing if self-activation fails
            }
        }

        // Normal processing for non-delayed activation orders
        List<String> emails = getOperateEmail(customerOrder.getCustomerOrderCode(), GvcoreConstants.OPERATE_METHOD_ISSUANCE, false);
        sendEmail(customerOrder.getCustomerOrderCode(), FlowNodeEnum.RECEIVE.getCode(), emails);
        int i1 = customerOrderService.updateStatus(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.COMPLETED.getStatus(), CustomerOrderStatusEnum.LOADING.getStatus(),
                request.getUpdateUser());
        if (i1 == 0) {
            log.error("修改订单状态失败---------------------------------------receive");
        }
    }

    private void sendEmail(String customerOrderCode, String flowNodeCode, List<String> emails) {
        Map<String, Object> params = getExtendsParams(customerOrderCode);
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
        sendNoticeRequest.setBusinessCode(customerOrderCode);
        sendNoticeRequest.setFlowCode(FlowEnum.CUSTOMER_ORDER_FLOW.getCode());
        sendNoticeRequest.setFlowNodeCode(flowNodeCode);
        sendNoticeRequest.setExtendParams(params);
        if (!CollectionUtils.isEmpty(emails)) {
            sendNoticeRequest.setEmails(emails);
        }
        flowNoticeService.send(sendNoticeRequest);
    }

    //@Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public void updateVoucherByRelease(ReleaseRequest request, CustomerOrder customerOrder, boolean isRelease) {

        String approvalCode = gvCodeHelper.generateApproveCode();
        String invoiceNo = gvCodeHelper.generateInvoiceNumber();


        if (GvcoreConstants.MOP_CODE_VCR.equals(customerOrder.getMopCode())) {
            voucherAllocationService.customerOrderRelease(customerOrder, isRelease, request.getUpdateUser(), approvalCode);
        }

        if (GvcoreConstants.MOP_CODE_VCE.equals(customerOrder.getMopCode())) {
            if (isRelease) {

                voucherService.activatDigitalVoucherByCustomerOrder(customerOrder, request.getUpdateUser(), approvalCode);

            } else {
                int i = voucherService.deleteByVoucherBatch(customerOrder.getVoucherBatchCode());
                if (i == 0) {
                    throw new GTechBaseException(ResultErrorCodeEnum.DELETE_DIGITAL_VOUCHER_FAILED.code(),
                            "{0} delete Digital Voucher failed ", request.getCustomerOrderCode());
                }
            }
        }
    }

    private List<String> getOperateEmail(String customerOrderCode, String method, boolean flag) {
        List<String> emails = new ArrayList<>();
        List<GvOperateLogEntity> list = operateLogService.queryLogByBusiness(customerOrderCode);
        if (CollectionUtils.isEmpty(list)) {
            return emails;
        }
        for (GvOperateLogEntity entity : list) {
            if (method.equals(entity.getMethod())) {
                String email = userAccountService.getUserEmail(entity.getOperateUser());
                emails.add(email);
                if (Boolean.TRUE.equals(flag)) {
                    break;
                }
            }
        }
        return emails;
    }

    private String getUserName(String operateUser) {
        String operateUserName = "";
        if (StringUtils.isNotBlank(operateUser)) {
            GetUserAccountParamDto paramDto = new GetUserAccountParamDto();
            paramDto.setUserCode(operateUser);
            UserAccountResponse userAccount = this.userAccountService.getUserAccount(paramDto);
            if (null != userAccount)
                operateUserName = ConvertUtils.toString(userAccount.getFirstName(), StringUtils.EMPTY)
                        + " "
                        + ConvertUtils.toString(userAccount.getLastName(), StringUtils.EMPTY);
        }
        return operateUserName;
    }

    //@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Result<String> issuanceVce(IssuanceRequest request, CustomerOrder customerOrder, List<CustomerOrderDetails> details) {
        Result<String> result;
        int i;
        List<GetCustomerOrderDetailsResponse> vouchersDetails = new ArrayList<>(details.size());
        for (CustomerOrderDetails customerOrderDetails : details) {
            GetCustomerOrderDetailsResponse vouchersDetail = new GetCustomerOrderDetailsResponse();
            vouchersDetail.setCustomerOrderDetailsCode(customerOrderDetails.getCustomerOrderDetailsCode());
            vouchersDetail.setCpgCode(customerOrderDetails.getCpgCode());
            vouchersDetail.setDenomination(customerOrderDetails.getDenomination());
            vouchersDetail.setVoucherNum(customerOrderDetails.getVoucherNum());
            vouchersDetail.setCustomerOrderCode(customerOrderDetails.getCustomerOrderCode());
            vouchersDetails.add(vouchersDetail);
        }

        GenerateDigitalVouchersRequest vouchersRequest = GenerateDigitalVouchersRequest.builder()
                .customerOrderCode(request.getCustomerOrderCode())
                .purchaseOrderNo(customerOrder.getPurchaseOrderNo())
                .issuerCode(customerOrder.getIssuerCode())
                .mopCode(customerOrder.getMopCode())
                .createUser(request.getUpdateUser())
                .customerOrderDetails(vouchersDetails).build();
        try {
            result = voucherBatchService.generateDigitalVoucher(vouchersRequest);
        } catch (IOException e) {
            throw new GTechBaseException(ResultErrorCodeEnum.GENERATE_DIGITAL_VOUCHER_FAILED.code(),
                    "{0} generate Digital Voucher failed ", customerOrder.getCustomerOrderCode());
        }
        if (!result.isSuccess()) {
            throw new GTechBaseException(ResultErrorCodeEnum.GENERATE_DIGITAL_VOUCHER_FAILED.code(),
                    "{0} generate Digital Voucher failed ", customerOrder.getCustomerOrderCode());
        }
        log.info("CustomerOrderissuance generateDigitalVoucher CustomerOrderCode={}, result={}",
                customerOrder.getCustomerOrderCode(), JSON.toJSONString(result));

        //给订单添加批次
        i = customerOrderService.updateCustomerBatchCode(customerOrder.getCustomerOrderCode(), result.getData());


        if (i == 0) {
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "{0} save voucherBatchCode failed ", customerOrder.getCustomerOrderCode());
        }
        return result;
    }
    /*private int updateStatus(String customerOrderCode, String status, String oldStatus, String updateUser) {

        CustomerOrderDto dto = new CustomerOrderDto();
        dto.setCustomerOrderCode(customerOrderCode);
        dto.setStatus(status);
        dto.setOldStatus(oldStatus);
        dto.setUpdateTime(new Date());
        dto.setUpdateUser(updateUser);
        return customerOrderMapper.updateStatus(dto);
    }
    private int loadingOrder(String customerOrderCode, CustomerOrderStatusEnum statusEnum) {
        Example example = new Example(CustomerOrder.class);
        example.createCriteria()
                .andEqualTo(CustomerOrder.C_CUSTOMER_ORDER_CODE, customerOrderCode)
                .andEqualTo(CustomerOrder.C_STATUS, statusEnum.getStatus());
        CustomerOrder order = new CustomerOrder();
        order.setStatus(CustomerOrderStatusEnum.LOADING.getStatus());
        return customerOrderMapper.updateByConditionSelective(order, example);
    }*/

    /**
     * Check if the order should use delayed self-activation
     * Based on the outlet code (channel) configuration
     */
    private boolean shouldUseDelayedActivation(CustomerOrder customerOrder) {
        try {
            if (selfActivationProperties.getDelayedChannels() == null ||
                selfActivationProperties.getDelayedChannels().isEmpty()) {
                return false;
            }

            String outletCode = customerOrder.getOutletCode();
            if (StringUtil.isEmpty(outletCode)) {
                return false;
            }

            return selfActivationProperties.getDelayedChannels().contains(outletCode);
        } catch (Exception e) {
            log.error("Error checking delayed activation for order: " + customerOrder.getCustomerOrderCode(), e);
            return false;
        }
    }



}
