package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.GvCpgTypeStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.cpg.CreateCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgTypeByPageRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgTypeRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgTypeStatusRequest;
import com.gtech.gvcore.common.response.cpg.CpgTypeListResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgTypeResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgTypeByPageResponse;
import com.gtech.gvcore.dao.mapper.CpgTypeMapper;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CpgTypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class CpgTypeServiceImpl implements CpgTypeService {

    @Autowired
    private CpgTypeMapper cpgTypeMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Override
    public Result<String> createCpgType(CreateCpgTypeRequest createCpgTypeRequest) {

        //cpgTypeName唯一校验
        CpgType cpgType = new CpgType();

        cpgType.setCpgTypeName(createCpgTypeRequest.getCpgTypeName());

        int count = cpgTypeMapper.selectCount(cpgType);
        if (count > 0) {
            return Result.failed(ResultErrorCodeEnum.CPG_TYPE_NAME_ALREADY_EXISTS.code(), ResultErrorCodeEnum.CPG_TYPE_NAME_ALREADY_EXISTS.desc());
        }

        //prefix 唯一校验
        cpgType = new CpgType();
        cpgType.setPrefix(createCpgTypeRequest.getPrefix());

        int count1 = cpgTypeMapper.selectCount(cpgType);
        if (count1 > 0) {
            return Result.failed(ResultErrorCodeEnum.CPG_TYPE_PREFIX_ALREADY_EXISTS.code(), ResultErrorCodeEnum.CPG_TYPE_PREFIX_ALREADY_EXISTS.desc());
        }

        CpgType cpgType1 = BeanCopyUtils.jsonCopyBean(createCpgTypeRequest, CpgType.class);

        cpgType1.setCpgTypeCode(gvCodeHelper.generateCpgTypeCode());
        cpgType1.setStatus(GvCpgTypeStatusEnum.STATUS_ENABLE.getCode());
        cpgType1.setCreateTime(new Date());

        cpgTypeMapper.insertSelective(cpgType1);

        return Result.ok(cpgType1.getCpgTypeCode());
    }

    @Override
    public PageResult<QueryCpgTypeByPageResponse> queryCpgTypeDataByPage(QueryCpgTypeByPageRequest request) {

        Example example = new Example(CpgType.class, true, false);

        example.createCriteria()
                .andLike(CpgType.C_CPG_TYPE_NAME, StringUtil.isEmpty(request.getCpgTypeName()) ? null : "%" + request.getCpgTypeName() + "%")
                .andEqualTo(CpgType.C_STATUS, null == request.getStatus() ? null : request.getStatus());

        //更新时间倒序
        example.orderBy(CpgType.C_CREATE_TIME).desc();

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<CpgType> list = cpgTypeMapper.selectByCondition(example);

        PageInfo<CpgType> pageInfo = new PageInfo<>(list);


        List<QueryCpgTypeByPageResponse> responses = new ArrayList<>(request.getPageSize());

        list.forEach(item -> {
            QueryCpgTypeByPageResponse response = new QueryCpgTypeByPageResponse();
            BeanUtils.copyProperties(item, response);

            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public Result<Object> updateCpgTypeStatus(UpdateCpgTypeStatusRequest request) {

        Result<Object> noDataFound = getResult(request.getCpgTypeCode());

        if (noDataFound != null) {
            return noDataFound;
        }
        Example example = new Example(CpgType.class);
        example.createCriteria().andEqualTo(CpgType.CPG_TYPE_CODE, request.getCpgTypeCode());

        CpgType cpgType = BeanCopyUtils.jsonCopyBean(request, CpgType.class);

        cpgType.setUpdateTime(new Date());
        cpgType.setUpdateUser(request.getUpdateUser());

        int updateCount = cpgTypeMapper.updateByConditionSelective(cpgType, example);
        if (updateCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
    }

    @Override
    public Result<Object> deleteCpgTypeById(String cpgTypeCode) {

        Result<Object> noDataFound = getResult(cpgTypeCode);

        if (noDataFound != null) {
            return noDataFound;
        }
        Example example = new Example(CpgType.class);

        example.createCriteria().andEqualTo(CpgType.CPG_TYPE_CODE, cpgTypeCode);

        int deleteCount = cpgTypeMapper.deleteByCondition(example);
        if (deleteCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
    }

    private Result<Object> getResult(String cpgTypeCode) {

        CpgType cpgType = new CpgType();

        cpgType.setCpgTypeCode(cpgTypeCode);

        int selectCount = cpgTypeMapper.selectCount(cpgType);
        if (selectCount <= 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        return null;
    }

    @Override
    public Result<Object> updateCpgType(UpdateCpgTypeRequest request) {

        Result<Object> checkResult = checkRequestParam(request);

        if (checkResult != null) {
            return checkResult;
        }

        CpgType cpgType = BeanCopyUtils.jsonCopyBean(request, CpgType.class);

        cpgType.setUpdateTime(new Date());

        Example example = new Example(CpgType.class);

        example.createCriteria().andEqualTo(CpgType.CPG_TYPE_CODE, request.getCpgTypeCode());

        int updateCount = cpgTypeMapper.updateByConditionSelective(cpgType, example);
        if (updateCount > 0) {
            return Result.ok(cpgType.getCpgTypeCode());
        }


        return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
    }

    @Override
    public Result<Object> getCpgType(GetCpgTypeRequest getCpgTypeRequest) {

        Result<Object> noDataFound = getResult(getCpgTypeRequest.getCpgTypeCode());

        if (noDataFound != null) {
            return noDataFound;
        }
        CpgType cpgType = new CpgType();
        cpgType.setCpgTypeCode(getCpgTypeRequest.getCpgTypeCode());

        CpgType byPrimaryKey = cpgTypeMapper.selectOne(cpgType);

        GetCpgTypeResponse getCpgTypeResponse = BeanCopyUtils.jsonCopyBean(byPrimaryKey, GetCpgTypeResponse.class);

        return Result.ok(getCpgTypeResponse);
    }

    @Override
    public Result<Object> cpgTypeList() {

        Example example = new Example(CpgType.class, true, false);

        //查询开启的
        example.createCriteria().andEqualTo(CpgType.C_STATUS, GvCpgTypeStatusEnum.STATUS_ENABLE.getCode());

        example.orderBy(CpgType.C_CREATE_TIME).desc();


        List<CpgType> selectList = cpgTypeMapper.selectByCondition(example);

        List<CpgTypeListResponse> cpgTypeListResponses = new ArrayList<>();

        selectList.forEach(singleCpgType -> {
            CpgTypeListResponse listResponse = new CpgTypeListResponse();
            BeanUtils.copyProperties(singleCpgType, listResponse);
            cpgTypeListResponses.add(listResponse);
        });


        return Result.ok(cpgTypeListResponses);
    }

    /**
     * 数据校验
     *
     * @param request
     * @return
     */
    private Result<Object> checkRequestParam(UpdateCpgTypeRequest request) {

        Result<Object> noDataFound = getResult(request.getCpgTypeCode());

        if (noDataFound != null) {
            return noDataFound;
        }

        Example example = new Example(CpgType.class);

        //check prefix，CPG Type Name
        //存在CPG Type Name并且id非本次要修改的数据id，提示数据已存在
        example.createCriteria().andEqualTo(CpgType.C_CPG_TYPE_NAME, request.getCpgTypeName()).andNotEqualTo(CpgType.CPG_TYPE_CODE, request.getCpgTypeCode());

        int selectCountByCondition = cpgTypeMapper.selectCountByCondition(example);
        if (selectCountByCondition > 0) {
            return Result.failed(ResultErrorCodeEnum.CPG_TYPE_NAME_ALREADY_EXISTS.code(), ResultErrorCodeEnum.CPG_TYPE_NAME_ALREADY_EXISTS.desc());
        }

        //存在prefix，并且id非本次要修改的数据id，提示数据已存在
        example = new Example(CpgType.class);
        example.createCriteria().andEqualTo(CpgType.C_PREFIX, request.getPrefix()).andNotEqualTo(CpgType.CPG_TYPE_CODE, request.getCpgTypeCode());

        int countByIdAndPrefix = cpgTypeMapper.selectCountByCondition(example);

        if (countByIdAndPrefix > 0) {
            return Result.failed(ResultErrorCodeEnum.CPG_TYPE_PREFIX_ALREADY_EXISTS.code(), ResultErrorCodeEnum.CPG_TYPE_PREFIX_ALREADY_EXISTS.desc());
        }

        return null;
    }

    @Override
    public Map<String, CpgType> queryCpgTypeCodeNameByCpgTypeCodeList(List<String> cpgTypeCodeList) {

        if (CollectionUtils.isEmpty(cpgTypeCodeList)) {
            return Collections.emptyMap();
        }

        List<CpgType> list = cpgTypeMapper.queryByCpgTypeCodeList(cpgTypeCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(CpgType::getCpgTypeCode, v -> v));
    }

    @Override
    public CpgType queryByCpgTypeCode(String cpgTypeCode) {

        if (StringUtils.isBlank(cpgTypeCode)) {
            return null;
        }

        CpgType cpgType = new CpgType();
        cpgType.setCpgTypeCode(cpgTypeCode);
        return cpgTypeMapper.selectOne(cpgType);
    }

	@Override
	public CpgType getCpgTypeByPrefix(String prefix) {
		if (StringUtil.isEmpty(prefix)) {
			return null;
		}
		CpgType cpgType = new CpgType();
		cpgType.setPrefix(prefix);
		return cpgTypeMapper.selectOne(cpgType);
	}
}
