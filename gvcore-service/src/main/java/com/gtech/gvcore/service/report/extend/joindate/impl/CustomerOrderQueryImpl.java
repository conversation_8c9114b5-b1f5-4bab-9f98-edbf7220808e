package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName CustomerOrderQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:48
 * @Version V1.0
 **/
@Component
public class CustomerOrderQueryImpl implements QuerySupport<CustomerOrder> {

    private static final CustomerOrder EMPTY = new CustomerOrder();

    @Autowired
    private CustomerOrderMapper customerOrderMapper;

    @Override
    public List<CustomerOrder> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(CustomerOrder.class);
        example.createCriteria().andIn(CustomerOrder.C_CUSTOMER_ORDER_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<CustomerOrder> list = customerOrderMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<CustomerOrder, String> codeMapper() {
        return CustomerOrder::getCustomerOrderCode;
    }

    @Override
    public CustomerOrder emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<CustomerOrder> supportType() {
        return CustomerOrder.class;
    }
}
