package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcDeactivatedForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.DeactivatedBo;
import com.gtech.gvcore.service.report.impl.param.GcDeactivatedQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description:
 */
@Service
public class GcDeactivatedForSummaryImpl extends ReportSupport
        implements BusinessReport<GcDeactivatedQueryData, GcDeactivatedForSummaryBean>, SingleReport {

    @Override
    public GcDeactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcDeactivatedQueryData queryData = new GcDeactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setCustomerCodes(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        return queryData;
    }

    @Override
    public List<GcDeactivatedForSummaryBean> getExportData(GcDeactivatedQueryData queryData) {

        List<DeactivatedBo> boList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcDeactivated, queryData, boList::addAll);
        //find
        Collection<DeactivatedSummaryStatisticalBo> list =
                Optional.of(boList)
                        .map(e -> e.stream()
                                .collect(Collectors.toMap(
                                        DeactivatedSummaryStatisticalBo::groupKey,
                                        DeactivatedSummaryStatisticalBo::convert,
                                        DeactivatedSummaryStatisticalBo::merge))
                        ).map(Map::values)
                        .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, DeactivatedSummaryStatisticalBo::getOwnerCustomer, Customer.class);
        //convert result
        List<GcDeactivatedForSummaryBean> collect = list.stream()
                .map(e -> {
                    String customerName;
                    Customer value = customerMap.findValue(e.getCustomerCode());
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    return new GcDeactivatedForSummaryBean()
                            .setVoucherAmount(super.toAmount(e.getTotalAmount()))
                            .setNumberOfVouchers(String.valueOf(e.getNumberOfVouchers()))
                            .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                            .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                            .setCustomerName(customerName)
                            .setDeactivatedReason(e.getDeactivatedReason());
                }).collect(Collectors.toList());
        GcDeactivatedForSummaryBean bean = new GcDeactivatedForSummaryBean();
        bean.setMerchant("Total");
        bean.setNumberOfVouchers(list.stream().map(DeactivatedSummaryStatisticalBo::getNumberOfVouchers).reduce(0, Integer::sum).toString());
        bean.setVoucherAmount(list.stream().map(DeactivatedSummaryStatisticalBo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        collect.add(bean);
        return collect;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_BLOCKED_AND_DEACTIVATED_SUMMARY;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class DeactivatedSummaryStatisticalBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        private String customerCode;

        private String deactivatedReason;

        private String ownerCustomer;

        public static DeactivatedSummaryStatisticalBo convert(DeactivatedBo deactivatedBo) {

            return new DeactivatedSummaryStatisticalBo()
                    .setMerchantCode(deactivatedBo.getMerchantCode())
                    .setOutletCode(deactivatedBo.getOutletCode())
                    .setCpgCode(deactivatedBo.getCpgCode())
                    .setTotalAmount(deactivatedBo.getDenomination())
                    .setCustomerCode(deactivatedBo.getCustomerCode())
                    .setDeactivatedReason(deactivatedBo.getBlockReason())
                    .setOwnerCustomer(deactivatedBo.getCustomerCode());
        }

        public DeactivatedSummaryStatisticalBo merge(DeactivatedSummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey(DeactivatedBo deactivatedBo) {

            return StringUtils.join(",", deactivatedBo.getMerchantCode(), deactivatedBo.getOutletCode(),
                    deactivatedBo.getCpgCode(), deactivatedBo.getCustomerCode(), deactivatedBo.getBlockReason());
        }

    }
}
