package com.gtech.gvcore.service.context;

import com.alibaba.fastjson.JSON;
import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.transaction.CustomerInfo;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.request.voucherbooklet.VoucherActivateUpdateBookletStatusDto;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.common.response.voucher.VerifyVoucherDto;
import com.gtech.gvcore.common.response.voucher.VoucherResponse;
import com.gtech.gvcore.components.TaskProgressManager;
import com.gtech.gvcore.dao.mapper.*;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TransactionApiAction {


    public static final String API_CUSTOMER = "api customer";

    @Autowired
    private CpgService cpgService;


    @Autowired
    private VoucherBookletService voucherBookletService;


    @Lazy
    @Autowired
    private VoucherService voucherService;

    @Autowired
    private TransactionDataMapper transactionDataMapper;



    @Autowired
    private CustomerOrderService customerOrderService;


    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private TransactionDataService transactionDataService;


    @Transactional
    public void updateVoucherStatusAndAddTransactionData(List<VerifyVoucherDto> verifyResponses) {

        List<VerifyVoucherDto> activityCollection = verifyResponses.stream().filter(x -> x.getActionType().equals(VerifyVoucherTypeEnum.VOUCHER_ACTIVATION.getCode())).collect(Collectors.toList());
        List<VerifyVoucherDto> redemptionCollection = verifyResponses.stream().filter(x -> x.getActionType().equals(VerifyVoucherTypeEnum.VOUCHER_REDEMPTION.getCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(activityCollection) && CollectionUtils.isEmpty(redemptionCollection)){
            //两个都为空，认为是校验，直接返回不需要修改
            return;
        }

        Boolean flag = false;

        if (CollectionUtils.isNotEmpty(activityCollection))
            flag = updateStatus(activityCollection, VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());


        if (CollectionUtils.isNotEmpty(redemptionCollection))
            flag = updateStatus(redemptionCollection,VoucherStatusEnum.VOUCHER_USED.getCode());


        if (!flag)
            throw new GTechBaseException();

    }

    public Boolean updateStatus(List<VerifyVoucherDto> vouchers,Integer actionType){
        if (CollectionUtils.isEmpty(vouchers)) return false;

        List<String> redeemFail = vouchers.stream()
                .flatMap(vd -> vd.getFailVerify().stream().map(x -> x.getVoucherInfo().getVoucherCode()).collect(Collectors.toList()).stream())
                .collect(Collectors.toList());

        if (actionType.equals(VoucherStatusEnum.VOUCHER_USED.getCode()) && CollectionUtils.isNotEmpty(redeemFail)){
            throw new GTechBaseException();
        }

        List<String> allVouchers = vouchers.stream()
                .flatMap(vd -> vd.getSuccessVerify().stream().map(x->x.getVoucherInfo().getVoucherCode()).collect(Collectors.toList()).stream())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allVouchers)) throw new GTechBaseException();

        List<Voucher> voucherList = BeanCopyUtils.jsonCopyList(vouchers.stream()
                .filter(x -> CollectionUtils.isNotEmpty(x.getVoucherList()))
                .flatMap(vd -> vd.getVoucherList().stream())
                .filter(x -> allVouchers.contains(x.getVoucherCode()))
                .collect(Collectors.toList()), Voucher.class);

        UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();
        TransactionContext.TransactionInfoContext context = TransactionContext.getContext();
        OutletResponse outlet = context.getOutlet();
        if (actionType.equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())){
            request.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
            request.setSalesTime(new Date());
            request.setSalesOutlet(Optional.ofNullable(outlet.getOutletCode()).orElse(StringUtil.EMPTY));
        }
        if (actionType.equals(VoucherStatusEnum.VOUCHER_USED.getCode())){
            request.setOldStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
            request.setUsedOutlet(Optional.ofNullable(outlet.getOutletCode()).orElse(StringUtil.EMPTY));
            request.setUsedTime(new Date());
        }



        request.setStatus(actionType);
        request.setVoucherCodeList(allVouchers);
        request.setUpdateUser(gvCodeHelper.generateTransactionCode());
        boolean allVoucher = voucherService.updateVoucherStatus(request) == allVouchers.size();

        if (!allVoucher){
            throw new GTechBaseException();
        }

        if (Objects.equals(actionType, VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())) {
            //订单
            createCustomerOrder(voucherList);
        }

        //添加交易记录
        String transactionType = StringUtil.EMPTY;

        if(actionType.equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())){
            transactionType = TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode();
        }else if (actionType.equals(VoucherStatusEnum.VOUCHER_USED.getCode())){
            transactionType = TransactionTypeEnum.GIFT_CARD_REDEEM.getCode();
        }

        Integer i = addTransactionData(BeanCopyUtils.jsonCopyList(voucherList, Voucher.class), transactionType);

        if (!i.equals(allVouchers.size())){
            throw new GTechBaseException();
        }

        return allVoucher;
    }

    public Integer addTransactionData(List<Voucher> vouchers,String transactionType){
        TransactionRequest request = TransactionContext.getRequest();
        TransactionContext.TransactionInfoContext context = TransactionContext.getContext();
        String batchId = context.getBatchId();
        String invoiceNumber = context.getInvoiceNumber();
        OutletResponse outlet = context.getOutlet();
        PosResponse pos = context.getPos();
        String approvalCode = context.getApprovalCode();

        List<CreateTransactionDataRequest> transactionDataRequests = new ArrayList<>();
        for (Voucher voucher : vouchers) {
            CreateTransactionDataRequest logRequest = new CreateTransactionDataRequest();


            logRequest.setTransactionId(String.valueOf(request.getTransactionId())+String.valueOf(batchId)+invoiceNumber);
            logRequest.setTransactionType(String.valueOf(transactionType));
            logRequest.setMerchantCode(outlet.getMerchantCode());
            logRequest.setOutletCode(pos.getOutletCode());
            logRequest.setIssuerCode(voucher.getIssuerCode());
            logRequest.setCpgCode(voucher.getCpgCode());
            logRequest.setMopCode(voucher.getMopCode());
            //.transactionDate(request.getDateAtClient())
            logRequest.setTransactionDate(new Date());
            //根据类型获取范围或者单张
            logRequest.setVoucherCode(voucher.getVoucherCode());
            logRequest.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll("[a-zA-Z]", "")));
            logRequest.setInitiatedBy("");
            logRequest.setPosCode(pos.getPosCode());
            logRequest.setBatchCode(voucher.getVoucherBatchCode());
            logRequest.setLoginSource("");
            logRequest.setDenomination(voucher.getDenomination());
            logRequest.setActualOutlet(voucher.getMopCode());
            logRequest.setForwardingEntityId("");
            logRequest.setTransactionMode(request.getTransactionModeId());
            logRequest.setVoucherEffectiveDate(voucher.getVoucherEffectiveDate());
            CustomerInfo customerInfo = new CustomerInfo();
            if (null != request.getCardholderInfo()) {
                customerInfo = request.getCardholderInfo();
            } else if (null != request.getPurchaserInfo()) {
                customerInfo = request.getPurchaserInfo();
            }

            if (null != customerInfo) {
                logRequest.setCustomerSalutation(customerInfo.getSalutation());
                logRequest.setCustomerFirstName(customerInfo.getFirstName());
                logRequest.setCustomerLastName(customerInfo.getLastName());
                logRequest.setMobile(customerInfo.getMobile());
                logRequest.setCorporateName(customerInfo.getCorporatename());
                logRequest.setEmail(customerInfo.getEmail());
                logRequest.setCustomerType(customerInfo.getCustomerType());
            }

            if (StringUtil.isEmpty(request.getInvoiceNumber())) {
                logRequest.setInvoiceNumber(invoiceNumber);
            } else {
                logRequest.setInvoiceNumber(request.getInvoiceNumber());
            }

//            logRequest.setOtherInputParameter(JSON.toJSONString(request));


            /*logRequest.setSuccessOrFailure(voucherSuccessStatus.getOrDefault(voucher.getVoucherCode(),success));
            logRequest.setResponseMessage(responseMessage);*/
            //这里默认全部成功
            logRequest.setSuccessOrFailure("0");
            logRequest.setResponseMessage("Transaction Successful.");

            logRequest.setCreateUser("");
            logRequest.setCreateTime(new Date());
            logRequest.setReferenceNumber(String.valueOf(request.getTransactionId()));
            logRequest.setTransactionChannel(TransactionChannelEnum.API.getCode());

            //MER-1844
            if (logRequest.getSuccessOrFailure().equals("0")) {
                logRequest.setApproveCode(approvalCode);
            } else {
                logRequest.setApproveCode(" ");
            }
            logRequest.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            logRequest.setBatchId(String.valueOf(batchId));
            logRequest.setCardEntryMode("Swiped");
            if (StringUtil.isNotEmpty(request.getNotes())) {
                logRequest.setNotes(request.getNotes());
            }
            log.info("logRequest:{}", JSON.toJSONString(logRequest));
            transactionDataRequests.add(logRequest);
        }
        Result<Integer> transactionDataList = transactionDataService.createTransactionDataList(transactionDataRequests);


        return transactionDataList.getData();
    }


    private void createCustomerOrder(List<Voucher> vouchers) {

        TransactionRequest request = TransactionContext.getRequest();
        TransactionContext.TransactionInfoContext context = TransactionContext.getContext();
        String batchId = context.getBatchId();
        String invoiceNumber = context.getInvoiceNumber();
        OutletResponse outlet = context.getOutlet();
        PosResponse pos = context.getPos();
        String approvalCode = context.getApprovalCode();

        //查询是否有销售记录 有则不创建
        //查询是否有销售记录 有则不创建
        Example tExample = new Example(TransactionData.class);
        tExample.createCriteria()
                .andIn(TransactionData.C_VOUCHER_CODE, vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()))
                .andEqualTo(TransactionData.C_SUCCESS_OR_FAILURE, "0");
        List<TransactionData> transactionData = transactionDataMapper.selectByCondition(tExample);
        //分别查询transactionData中transactionType为销售的记录和transactionType为取消销售的记录
        List<TransactionData> sellList = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_SELL.getCode())).collect(Collectors.toList());
        List<TransactionData> cancelSell = transactionData.stream().filter(x -> x.getTransactionType().equals(TransactionTypeEnum.GIFT_CARD_CANCEL_SELL.getCode())).collect(Collectors.toList());

        //如果是取消销售再销售的，不会再创建订单
        if (CollectionUtils.isNotEmpty(sellList) && cancelSell.size() == sellList.size()) {
            //插入销售记录
            log.info("执行添加销售记录");
            addTransactionData(vouchers, TransactionTypeEnum.GIFT_CARD_SELL.getCode());
            log.info("执行添加销售记录结束");
            return;
        }

        addTransactionData(vouchers, TransactionTypeEnum.GIFT_CARD_SELL.getCode());
        //插入销售记录
        log.info("执行添加销售记录");
        log.info("执行添加销售记录结束");
        List<GetCpgResponse> cpgByCpgNameList = new ArrayList<>();
        List<CreateCustomerOrderDetailsRequest> detailsRequestList = new ArrayList<>();
        try {
            for (Voucher voucher : vouchers) {
                GetCpgResponse data = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucher.getCpgCode()).build()).getData();
                if (null != data) {
                    cpgByCpgNameList.add(data);
                }
                detailsRequestList.add(CreateCustomerOrderDetailsRequest
                        .builder()
                        .cpgCode(voucher.getCpgCode())
                        .voucherNum(1)
                        .deleteStatus(2)//自动生成
                        .denomination(voucher.getDenomination())
                        .build());

            }

        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.GET_CPG_ERROR.code(), ResultErrorCodeEnum.GET_CPG_ERROR.desc());
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (GetCpgResponse getCpgResponse : cpgByCpgNameList) {
            totalAmount = totalAmount.add(getCpgResponse.getDenomination());
        }

        createCustomerOrderByCreateAndIssue(invoiceNumber, outlet, null,
                Voucher.builder()
                        .mopCode(cpgByCpgNameList.get(0).getMopCode())
                        .issuerCode(cpgByCpgNameList.get(0).getIssuerCode())
                        .voucherBatchCode(vouchers.get(0).getVoucherBatchCode())
                        .build(),
                String.valueOf(request.getTransactionId()) + String.valueOf(batchId) + invoiceNumber, ""
                , Integer.valueOf(vouchers.size()), detailsRequestList, totalAmount);

        //修改booklet状态
        if (CollectionUtils.isNotEmpty(vouchers)) {
            voucherBookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                    .voucherCode(vouchers.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList()))
                    .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                    .type("0")
                    .build());
        }

    }


    public CreateCustomerOrderRequest createCustomerOrderByCreateAndIssue(String invoiceNumber, OutletResponse outlet, CustomerInfo customerInfo
            , Voucher voucher, String transactionId, String notes, Integer numOfCards
            , List<CreateCustomerOrderDetailsRequest> detailsRequestList, BigDecimal voucherAmount) {
        CreateCustomerOrderRequest customerOrderRequest = new CreateCustomerOrderRequest();
        //customerOrderRequest.setCustomerOrderCode(transactionId);
        customerOrderRequest.setIssuerCode(voucher.getIssuerCode());
        customerOrderRequest.setOutletCode(outlet.getOutletCode());
        customerOrderRequest.setInvoiceNo(invoiceNumber);
        customerOrderRequest.setPurchaseOrderNo(outlet.getOutletName() + System.currentTimeMillis());
        customerOrderRequest.setMopCode(voucher.getMopCode());
        customerOrderRequest.setVoucherNum(numOfCards);
        customerOrderRequest.setVoucherAmount(voucherAmount);
        customerOrderRequest.setDiscount(new BigDecimal("0"));
        customerOrderRequest.setAmount(BigDecimal.ZERO);
        if (voucher.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
            customerOrderRequest.setVoucherBatchCode(voucher.getVoucherBatchCode());
        }

        customerOrderRequest.setContactFirstName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getFirstName()) ?
                customerInfo.getFirstName() : API_CUSTOMER);
        customerOrderRequest.setContactLastName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getLastName()) ?
                customerInfo.getLastName() : API_CUSTOMER);
        customerOrderRequest.setContactPhone(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getMobile()) ?
                customerInfo.getMobile() : API_CUSTOMER);
        customerOrderRequest.setCompanyName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCorporatename()) ?
                customerInfo.getCorporatename() : API_CUSTOMER);
        customerOrderRequest.setContactEmail(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getEmail()) ?
                customerInfo.getEmail() : API_CUSTOMER);


        customerOrderRequest.setCurrencyCode(API_CUSTOMER);
        customerOrderRequest.setCustomerCode(API_CUSTOMER);
        customerOrderRequest.setCustomerName(API_CUSTOMER);
        customerOrderRequest.setCustomerType(API_CUSTOMER);
        customerOrderRequest.setProductCategoryCode("");
        customerOrderRequest.setDiscountType("");
        customerOrderRequest.setCreateUser("api");
        customerOrderRequest.setMeansOfPaymentCode("");
        customerOrderRequest.setShippingAddress("");
        customerOrderRequest.setCustomerRemarks(notes);
        customerOrderRequest.setStatus(CustomerOrderStatusEnum.API.getStatus());
        customerOrderRequest.setReleaseTime(new Date());

        //detailsRequestList将相同cpgCode的数据的voucherNum相加
        Map<String, CreateCustomerOrderDetailsRequest> map = new HashMap<>();
        for (CreateCustomerOrderDetailsRequest detailsRequest : detailsRequestList) {
            if (map.containsKey(detailsRequest.getCpgCode())) {
                CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = map.get(detailsRequest.getCpgCode());
                createCustomerOrderDetailsRequest.setVoucherNum(createCustomerOrderDetailsRequest.getVoucherNum() + detailsRequest.getVoucherNum());
            } else {
                map.put(detailsRequest.getCpgCode(), detailsRequest);
            }
        }
        //map转list
        List<CreateCustomerOrderDetailsRequest> list = new ArrayList<>();
        for (Map.Entry<String, CreateCustomerOrderDetailsRequest> entry : map.entrySet()) {
            list.add(entry.getValue());
        }



        customerOrderRequest.setCreateCustomerOrderDetailsRequests(list/*Lists.newArrayList(
                CreateCustomerOrderDetailsRequest
                        .builder()
                        .cpgCode(voucher.getCpgCode())
                        .voucherNum(1)
                        .deleteStatus(2)//自动生成
                        .denomination(voucher.getDenomination())
                        .build()
        )*/);
        log.info("createCustomerOrder:{}", JSON.toJSONString(customerOrderRequest));
        Result<String> customerOrder = customerOrderService.createCustomerOrder(customerOrderRequest);
        if (!customerOrder.isSuccess()) {
            throw new GTechBaseException(customerOrder.getCode(), customerOrder.getMessage());
        }
        log.info("createCustomerOrder Result :{}", JSON.toJSONString(customerOrder));
        return customerOrderRequest;
    }


}
