package com.gtech.gvcore.service.report;

/**
 * gv core report page
 *      实现报表请仔细阅读 业务报表相关接口, extend 的介绍信息
 *      报表相关sql 请实现在 ReportMapper 并划分好新报表内容的区域
 * 顶层接口:
 *      @see com.gtech.gvcore.service.report.ReportRequestService 报表请求服务 处理生成报表的请求
 *      @see com.gtech.gvcore.service.report.ReportTaskRegisterService 报表任务注册服务 处理请求带来的任务消费
 *      @see com.gtech.gvcore.service.report.SchedulerReportService 定时报表服务 储存周期性报表请求的信息
 *      @see com.gtech.gvcore.service.report.SchedulerReportTaskService 定时报表任务服务 负责执行周期性报表请求的发起和强制执行
 * 业务报表相关接口:
 *      @see com.gtech.gvcore.service.report.BusinessReport 业务报表接口 实现业务报表时需要实现该接口
 *      @see com.gtech.gvcore.service.report.BusinessReportConfig 业务报表配置接口 提供基础的业务报表配置
 *      @see com.gtech.gvcore.service.report.ReportQueryParam 业务报表参数接口 所有业务报表构建参数都需要实现该接口 Poll 方式查询时需要继承 PageParam
 *      实现方式接口:
 *          每个业务报表只能任选其一实现 错误的继承结构导致的重复实现则优先 Poll方式 参考
 *          @see com.gtech.gvcore.service.report.base.BuilderReportHelper#builderReportByContext
 *          impl:
 *              @see com.gtech.gvcore.service.report.SingleReport 业务报表将以 Single 的方式进行构建 构建过程中只会进行单次调用获取数据
 *              @see com.gtech.gvcore.service.report.PollReport 业务报表将以 Poll 的方式进行构建 构建过程会反复调用获取数据方法直到获取不到数据 使用 Poll 方式进行调用时 ReportQueryParam的实现类需注意继承 PageParam
 * base 基础包
 *      @see com.gtech.gvcore.service.report.base.BuilderReportHelper 构建报表实现类 该类提供报表个个阶段的默认实现方式与报表基础流程组装
 *      @see com.gtech.gvcore.service.report.base.ReportSupport 报表支持类 该类提供业务报表所需的基础内容 与 组装部分报表通用功能
 * extend 扩展包:
 *      context：上下文包
 *          @see com.gtech.gvcore.service.report.extend.context.ReportContext 报表上下文 负责构建报表的基础零件管理与构建过程中的各个阶段信息的储存
 *          @see com.gtech.gvcore.service.report.extend.context.ReportContextBuilder 报表上下文构建类
 *          @see com.gtech.gvcore.service.report.extend.context.ReportContextHelper 报表上下文帮助类 快速操作与获取报表上报表下文
 *          @see com.gtech.gvcore.service.report.extend.context.ReportContextStatusEnum 报表上下文状态枚举
 *      joindate：关联数据快速查询包
 *          @see com.gtech.gvcore.service.report.extend.joindate.JoinDateFunctionHelper 报表关联数据支持帮助类
 *          @see com.gtech.gvcore.service.report.extend.joindate.GetJoinDateMapSupport 报表关联数据支持接口
 *          @see com.gtech.gvcore.service.report.extend.joindate.JoinDataMap 查询支持接口
 *          @see com.gtech.gvcore.service.report.extend.joindate.QuerySupport 报表关联数据Map 帮助减少 判空代码简化报表逻辑
 *          impl:
 *              查询支持实现包 现有mapbycode无法支持的查询需在该包实现一个
 *              @see com.gtech.gvcore.service.report.extend.joindate.QuerySupport
 *      poll: 轮询支持扩展包
 *          @see com.gtech.gvcore.service.report.extend.poll.ManualPollBusinessReport 业务报表手动轮询
 *          @see com.gtech.gvcore.service.report.extend.ReportFactory 报表实现工厂类
 *          @see com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull 报表客户信息自动填充支持类 根据客户类型自动填充相应的字段
 *          @see com.gtech.gvcore.service.report.extend.ReportOutletTreeParamBuilder 报表outlet tree 数据帮助类 通过 outlet code 获得相关所有子级编码
 *          @see com.gtech.gvcore.service.report.extend.ReportParamConvertHelper 参数转换帮助类 用于快速转换父子级数据关系 如 merchantCode 转换 outletCode
 *          @see com.gtech.gvcore.service.report.extend.ReportProportionDataFunction 百分比数据帮助类 用于统一处理报表内的所有百分比数据
 *          @see com.gtech.gvcore.service.report.extend.voucherstatus.ReportQueryVoucherStatusParam 卡券状态参数帮助类 用于统一化卡券状态sql查询条件
 *          @see com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertSupport 卡券状态转换类 用于获得报表中所需的卡券状态
 * export: 数据导出包
 *      excel: 表格
 *          @see com.gtech.gvcore.service.report.export.file.DefaultFileContext 默认表格上下文
 *          @see com.gtech.gvcore.service.report.export.file.FileContext 表格构建上下文接口
 *          @see com.gtech.gvcore.service.report.export.file.ReportExcelUtils 表格构建帮助类 该类提供快速定位表格相关对象 以及部分表格扩展操作
 *      snapshoot: 数据快照包
 *          @see com.gtech.gvcore.service.report.export.snapshoot.DefaultReportDateContext 默认快照上下文
 *          @see com.gtech.gvcore.service.report.export.snapshoot.ReportDataHelper 报表快照信息帮助类 提供快照信息查询 和 存储
 *          @see com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext 报表快照上下文接口
 *          label: 快照表头信息包
 *              @see com.gtech.gvcore.service.report.export.snapshoot.label.OrderReportDataLabelComponent 报表数据表头帮助类 - 表头基础类
 *              @see com.gtech.gvcore.service.report.export.snapshoot.label.OrderReportLabelDynamicHelper 动态表头支持类
 *              @see com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelContextBean 表头渲染上下文 用于动态表头渲染时提供上下文信息
 *              @see com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelSupport 表头支持接口
 * impl: 业务报表实现包
 *      support:业务报表支持包
 * ...
 * ...
 */