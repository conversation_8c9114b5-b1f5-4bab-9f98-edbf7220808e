package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName BulkOrderDetailedBean
 * @Description BulkOrderDetailedBean
 * <AUTHOR>
 * @Date 2022/7/12 16:54
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcBulkOrderDetailedBean {

    /**
     * 订单创建时间
     */
    @ExcelProperty(value = "Purchase Order Date")
    private String poDate;

    /**
     * Merchant name
     */
    @ExcelProperty(value = "Merchant Name")
    private String merchant;


    /**
     * 公司名称.指用户在 Create Customer 录入的 Corporate(页面为Corporate,落库字段为company_name), ,数据在 gc_customer 表 company_name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * 券类型,其值为 gv_customer_order 表中 mop_code(VCR | VCE),描述信息从 dd_lang 获取,值包括:Physical Vouchers | Digital Vouchers
     */
    @ExcelProperty(value = "Gift Card Type")
    private String voucherType;

    /**
     * 订单号. gv_customer_order 表 purchase_order_no
     */
    @ExcelProperty(value = "PO Number")
    private String poNumber;

    /**
     * 该VPG的全部 voucher amount(面额) 和, 直接取gv_customer_order_details 表的voucher_amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "PO Value", converter = ExportExcelNumberConverter.class)
    private String poValue;

    /**
     * VPG Name
     */
    @ExcelProperty(value = "Order Item")
    private String orderItem;


    /**
     * 客户邮箱 gv_customer_order 表 contact_email
     */
    @ExcelProperty(value = "Email Recipient")
    private String emailRecipient;
    /**
     * 折扣百分比, gv_customer_order 表 discount.非百分比折扣固定为0 仅当 discountType 为 percentage 时表示折扣百分比.
     */
    @ExcelProperty(value = "Discount Percentage")
    private String discountPercentage;

    /**
     * 订单折扣总比例金额,若固定金额优惠,显示固定金额.若比例金额优惠,为订单总面额*比例
     */
    @ReportAmountValue
    @ExcelProperty(value = "Discount Amount", converter = ExportExcelNumberConverter.class)
    private String discountAmount;

    /**
     * 折扣类型,描述值取dd_lang,DD_CODE = 'product_category_discount_type'
     */
    @ExcelProperty(value = "Discount Type")
    private String discountType;
    /**
     * 创建人名称
     */
    @ExcelProperty(value = "User Login")
    private String activatingUserLogin;

    /**
     * Mop 名, 通过 gv_customer_order 表 means_of_payment_code 从 gv_means_of_payment 表 获取 Mop name
     */
    @ExcelProperty(value = "Payment Mode")
    private String paymentMode;

    /**
     * 发票号码.gv_customer_order 表 invoice_no
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * 该VPG总数量,直接取gv_customer_order_details 表的 voucher_num
     */
    @ExcelProperty(value = "Total Count", converter = ExportExcelNumberConverter.class)
    private String totalQuantity;

    /**
     * 该VPG激活过的总面额.使用vpg_code,batch,transaction_type 在 gv_transaction_data 表中定位,从 gv_transaction_data 获取 [激活过的] 券总面额
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;

    /**
     * Total Net Amount = PO Amount - Discount Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Net Amount", converter = ExportExcelNumberConverter.class)
    private String totalNetAmount;

    /**
     * 订单提交时的note,gv_customer_order_receiver 表中 customer_remarks,使用customer_order_code 关联
     */
    @ExcelProperty(value = "Notes")
    private String notes;

}
