package com.gtech.gvcore.service.report.impl;

import com.gtech.basic.idm.service.UserAccountService;
import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.basic.idm.service.dto.UserAccountDto;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.GcReportTempLiabilitySStructure;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelContextBean;
import com.gtech.gvcore.service.report.extend.ReportAmountSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcLiabilitySummaryBean;
import com.gtech.gvcore.service.report.impl.bean.LiabilitySummaryBean;
import com.gtech.gvcore.service.report.impl.bo.LiabilitySummaryHeadBo;
import com.gtech.gvcore.service.report.impl.param.LiabilitySummaryQueryData;
import com.gtech.gvcore.service.report.impl.support.liability.GcLiabilityDataScript;
import com.gtech.gvcore.service.report.impl.support.liability.LiabilityDataScript;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:10
 * @Description:
 */
@Service
public class GcLiabilitySummaryImpl extends ReportSupport
        implements BusinessReport<LiabilitySummaryQueryData, GcLiabilitySummaryBean>, SingleReport {

    @Autowired
    private UserAccountService userService;
    @Autowired
    private GcLiabilityDataScript script;

    @Override
    public LiabilitySummaryQueryData builderQueryParam(CreateReportRequest reportParam) {

        LiabilitySummaryQueryData liabilitySummaryQueryData = new LiabilitySummaryQueryData();

        // issuer merchant outlet
        liabilitySummaryQueryData.setIssuerCode(reportParam.getIssuerCode());
        liabilitySummaryQueryData.setMerchantCodeList(reportParam.getMerchantCodes());
        liabilitySummaryQueryData.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        liabilitySummaryQueryData.setCpgCodeList(reportParam.getCpgCodes());

        // table code - simplified backward compatibility logic
        Date transactionDateStart = reportParam.getTransactionDateStart();
        Date targetDate = GvConvertUtils.toObject(transactionDateStart, new Date());
        
        // 1. 首先尝试旧格式表
        String legacyTableCode = DateUtil.format(targetDate, GcLiabilityDataScript.TABLE_CODE_DATE_FORMAT);
        String legacyTableName = String.format("gc_report_temp_liability_%s_%s", 
            GcLiabilityDataScript.LIABILITY_SUMMARY_TABLE_TYPE, legacyTableCode);
        
        if (script.isTableExist(legacyTableName)) {
            // 旧表存在，使用旧格式
            liabilitySummaryQueryData.setTableCode(legacyTableCode);
        } else {
            // 旧表不存在，使用新格式
            String newTableName = script.generateNewTableName(GcLiabilityDataScript.LIABILITY_SUMMARY_TABLE_TYPE, targetDate);
            
            if (!script.isTableExist(newTableName)) {
                ReportContextHelper.noData();
            }
            
            // 🔧 修复：从完整表名提取正确的tableCode
            // 新格式表名：gc_report_temp_liability_s_1_08
            // 需要提取：1_08 (年份模数_月份)
            String[] parts = newTableName.split("_");
            String yearMod = parts[parts.length - 2];  // 获取 "1"
            String month = parts[parts.length - 1];    // 获取 "08"
            String newTableCode = yearMod + "_" + month; // 拼接为 "1_08"
            
            liabilitySummaryQueryData.setTableCode(newTableCode);
        }

        return liabilitySummaryQueryData;
    }

    @Override
    public List<GcLiabilitySummaryBean> getExportData(LiabilitySummaryQueryData queryData) {

        //find
        List<GcReportTempLiabilitySStructure> list = gcReportBusinessMapper.liabilitySummaryReport(queryData);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //find
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcReportTempLiabilitySStructure::getMerchantCode, Merchant.class);
        final JoinDataMap<Issuer> issuerMap = super.getMapByCode(list, GcReportTempLiabilitySStructure::getIssuerCode, Issuer.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcReportTempLiabilitySStructure::getCpgCode, GcCpg.class);

        final IssuerTotal issuerTotal = new IssuerTotal();

        List<GcLiabilitySummaryBean> result = new ArrayList<>(list.stream()
                // group by issuer merchant cpg
                .collect(Collectors.toMap(
                        // key => issuer merchant cpg
                        e -> StringUtils.join("_", e.getIssuerCode(), e.getMerchantCode(), e.getExpiryDate(),e.getCpgCode())
                        // value => identity
                        , Function.identity()
                        // merge => sum
                        , (e1, e2) -> {
                            e1.setActivatedAmount(e1.getActivatedAmount().add(e2.getActivatedAmount()));
                            e1.setPurchasedAmount(e1.getPurchasedAmount().add(e2.getPurchasedAmount()));
                            e1.setDeactivatedAmount(e1.getDeactivatedAmount().add(e2.getDeactivatedAmount()));
                            e1.setExpiredAmount(e1.getExpiredAmount().add(e2.getExpiredAmount()));
                            e1.setTotalAmount(e1.getTotalAmount().add(e2.getTotalAmount()));
                            return e1;
                        })).values() // to Result List
                .stream() // stream
                .map(issuerTotal::add)
                // convert to bean
                .map(e -> new GcLiabilitySummaryBean()
                        .setIssuer(issuerMap.findValue(e.getIssuerCode()).getIssuerName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setActivated(this.toAmount(e.getActivatedAmount()))
                        .setPurchased(this.toAmount(e.getPurchasedAmount()))
                        .setDeactivated(this.toAmount(e.getDeactivatedAmount()))
                        .setExpired(this.toAmount(e.getExpiredAmount()))
                        .setTotal(this.toAmount(e.getTotalAmount()))
                        .setExpiryDate(DateUtil.format(e.getExpiryDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                        .setAsOnDate(this.formatAsOnDate(queryData.getTableCode()))
                ).collect(Collectors.toList()));

        result.add(issuerTotal.convertBean());

        return result; //to List
    }

    @Getter
    @Setter
    public static class IssuerTotal implements ReportAmountSupport {

        private BigDecimal activatedAmount = BigDecimal.ZERO;

        private BigDecimal purchasedAmount = BigDecimal.ZERO;

        private BigDecimal deactivatedAmount = BigDecimal.ZERO;

        private BigDecimal expiredAmount = BigDecimal.ZERO;


        private BigDecimal totalAmount = BigDecimal.ZERO;

        public GcReportTempLiabilitySStructure add(GcReportTempLiabilitySStructure e) {
            this.activatedAmount = this.activatedAmount.add(e.getActivatedAmount());
            this.purchasedAmount = this.purchasedAmount.add(e.getPurchasedAmount());
            this.deactivatedAmount = this.deactivatedAmount.add(e.getDeactivatedAmount());
            this.expiredAmount = this.expiredAmount.add(e.getExpiredAmount());
            this.totalAmount = this.totalAmount.add(e.getActivatedAmount().add(e.getPurchasedAmount()).add(e.getDeactivatedAmount()));
            return e;
        }

        public GcLiabilitySummaryBean convertBean() {
            return new GcLiabilitySummaryBean()
                    .setIssuer("Total")
                    .setActivated(this.toAmount(this.activatedAmount))
                    .setPurchased(this.toAmount(this.purchasedAmount))
                    .setDeactivated(this.toAmount(this.deactivatedAmount))
                    .setExpired(this.toAmount(this.expiredAmount))
                    .setTotal(this.toAmount(this.totalAmount));
        }
    }

    /**
     * @see LiabilitySummaryBean expiredIn @ReportLabel
     * @param labelContextBean labelContextBean
     * @return expiredIn label value
     */
    @SuppressWarnings("unused")
    public String expiredIn(ReportLabelContextBean labelContextBean) {

        final CreateReportRequest reportParam = labelContextBean.getReportParam();
        final Date transactionDateStart = reportParam.getTransactionDateStart();

        return this.expiredIn(transactionDateStart);
    }

    private String expiredIn(Date transactionDateStart) {

        final Date selectDate = GvConvertUtils.toObject(transactionDateStart, new Date());

        return DateUtil.format(DateUtil.addMonth(selectDate, -1), GvDateUtil.FORMAT_MM_YY_SLASH);
    }

    /**
     * Format AsOnDate with backward compatibility for new and legacy table code formats
     * @param tableCode table code (could be "06" new format or "2506" legacy format)
     * @return formatted date string in "yyyy-MM" format
     */
    private String formatAsOnDate(String tableCode) {
        try {
            Date date;
            if (tableCode.length() == 2) {
                // New format: "06" -> parse with current year
                int month = Integer.parseInt(tableCode);
                Calendar cal = Calendar.getInstance();
                cal.set(Calendar.MONTH, month - 1);
                date = cal.getTime();
            } else {
                // Legacy format: "2506" -> parse with yyMM format
                date = DateUtil.parseDate(tableCode, GcLiabilityDataScript.TABLE_CODE_DATE_FORMAT);
            }
            return DateUtil.format(date, "yyyy-MM");
        } catch (Exception e) {
            return DateUtil.format(new Date(), "yyyy-MM");
        }
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.GC_LIABILITY_SUMMARY_REPORT;
    }

    @Override
    public Object getHeadObject(ReportContext context) {

        final CreateReportRequest reportParam = context.getReportParam();
        final LiabilitySummaryQueryData queryData = (LiabilitySummaryQueryData) context.getQueryParam();

        String merchant;
        if (CollectionUtils.isEmpty(queryData.getMerchantCodeList())) {
            merchant = "all";
        } else {
            Map<String, Merchant> merchantMap = super.getMapByCode(queryData.getMerchantCodeList(), Merchant.class);
            merchant = MapUtils.isEmpty(merchantMap) ? StringUtils.EMPTY : merchantMap.values().stream().map(Merchant::getMerchantName).collect(Collectors.joining(","));
        }

        String createUser = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(reportParam.getCreateUser())) {

            GetUserAccountParamDto paramDto = new GetUserAccountParamDto();
            paramDto.setUserCode(reportParam.getCreateUser());
            UserAccountDto userAccount = this.userService.getUserAccount(paramDto);

            createUser = null != userAccount
                    ? ConvertUtils.toString(userAccount.getFirstName()) + ConvertUtils.toString(userAccount.getLastName())
                    : StringUtils.EMPTY;
        }

        String cpg;
        if (CollectionUtils.isEmpty(queryData.getCpgCodeList())) {
            cpg = "all";
        } else {
            Map<String, Cpg> cpgMap = super.getMapByCode(queryData.getCpgCodeList(), Cpg.class);
            cpg = MapUtils.isEmpty(cpgMap) ? StringUtils.EMPTY : cpgMap.values().stream().map(Cpg::getCpgName).collect(Collectors.joining(","));
        }

        return new LiabilitySummaryHeadBo()
                .setAsOnDate(DateUtil.format(reportParam.getTransactionDateStart(), GvDateUtil.FORMAT_YYYY_MM_BAR))
                .setGeneratedOn(DateUtil.format(ReportContextHelper.reportBuilderTime(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                .setGeneratedBy(createUser)
                .setMerchant(merchant)
                .setIssuer(StringUtils.isEmpty(queryData.getIssuerCode()) ? "all" : super.getByCode(queryData.getIssuerCode(), Issuer.class).getIssuerName())
                .setCardProgramGroup(cpg)
                .setExpiredIn(this.expiredIn(reportParam.getTransactionDateStart()));

    }

}
