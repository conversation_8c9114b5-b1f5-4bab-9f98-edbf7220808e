package com.gtech.gvcore.service.impl.issuehandle;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.gtech.basic.filecloud.api.model.Resp;
import com.gtech.basic.filecloud.commons.PagedData;
import com.gtech.basic.filecloud.commons.PagedDatas;
import com.gtech.basic.filecloud.exports.excel.spec.ExcelExportSpec;
import com.gtech.basic.filecloud.exports.management.FileExport;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.basic.filecloud.exports.management.FileExportResult;
import com.gtech.commons.page.PageData;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.response.issuehandling.GcRegenerateActivationCodeToEmailResponse;
import com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.domain.service.ActivationCodeGenerator;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.helper.FileCompressionHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.impl.MessageComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GcIssueHandlerRegenerateActivationCodeService extends GcIssueHandlerValidateService implements IssueHandlerBaseService {

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;

    @Autowired
    private IssueHandlingDetailsMapper issueHandlingDetailsMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private FileExportManager fileExportManager;

    @Autowired
    private MessageComponent messageComponent;

    @Autowired
    private OssHelper ossHelper;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_RESET_ACTIVATION;
    }

    @Override
    public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
        checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkEmail(details);
        return details;
    }

    private static void checkEmail(List<IssueHandlingDetails> details) {
        for (IssueHandlingDetails detail : details) {
            if (StringUtil.isEmpty(detail.getReceiverEmail())) {
                String result = "Email can't be empty!";
                if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
                    detail.setResult(detail.getResult() + result);
                } else {
                    detail.setResult(result);
                }
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            }
            //校验email格式
            if (!isValidEmail(detail.getReceiverEmail())) {
                String result = "Email format is incorrect!";
                if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
                    detail.setResult(detail.getResult() + result);
                } else {
                    detail.setResult(result);
                }
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            }
        }
    }

    public static boolean isValidEmail(String email) {
        String emailRegex = "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        return email.matches(emailRegex);
    }


    @Override
    public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
        validate(details, issuerCode);
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        CountDownLatch downlatch = new CountDownLatch(details.size());
        List<IssueHandlingDetails> returnList = new CopyOnWriteArrayList<>();
        for (IssueHandlingDetails issueHandlingDetails : details) {
            EXECUTOR.execute(() -> {
                try {
                    returnList.add(issueHandlingDetails);
                    makeIssueHandling(issueHandlingDetails);
                } catch (Exception e) {
                    String msg = e.getMessage();
                    log.error(e.getMessage(), e);
                    issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                    issueHandlingDetails.setResult(msg.length() > 500 ? msg.substring(0, 499) : msg);
                } finally {
                    downlatch.countDown();
                }
            });
        }
        try {
            downlatch.await();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            Thread.currentThread().interrupt();
        }

        return returnList;
    }

    public void makeIssueHandling(IssueHandlingDetails issueHandlingDetails) {
        if (IssueHandlingProcessStatusEnum.FAILED.code().equals(issueHandlingDetails.getProcessStatus())) {
            return;
        }
        String voucherCode = issueHandlingDetails.getVoucherCode();
        WeekendSqls<GiftCardEntity> weekendSqls = WeekendSqls.custom();
        weekendSqls.andEqualTo(GiftCardEntity::getCardNumber, voucherCode);
        Example example = Example.builder(GiftCardEntity.class).where(weekendSqls).build();
        GiftCardEntity giftCard = new GiftCardEntity();
        giftCard.setActivationCode(ActivationCodeGenerator.generate());
        int count = giftCardMapper.updateByConditionSelective(giftCard, example);
        if (count == 0) {
            issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            issueHandlingDetails.setResult(ResultErrorCodeEnum.DATA_MISS.code() + "【" + voucherCode + "】");
        } else {
            issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
        }
    }


    @Override
    public void afterExecute(IssueHandling issueHandling) {
        if (issueHandling == null) {
            return;
        }
        IssueHandlingDetailsDto dto = new IssueHandlingDetailsDto();
        dto.setIssueHandlingCode(issueHandling.getIssueHandlingCode());
        List<IssueHandlingDetails> details = issueHandlingDetailsMapper.queryDigitalByIssueHandlingCode(dto);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        Map<String, List<IssueHandlingDetails>> map = details.stream().collect(Collectors.groupingBy(IssueHandlingDetails::getReceiverEmail));
        map.forEach((email, v) -> {
            try {
                sendToPic(issueHandling.getIssueHandlingCode(), details.get(0).getUpdateUser(), email, v.stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }


    private void sendToPic(String issueHandlingCode, String updateUser, String email, List<String> voucherCodeList) {
        String fileName = gvCodeHelper.generateFileNameCode();
        PageData<GcRegenerateActivationCodeToEmailResponse> pageData = new PageData<>();

        List<GiftCardEntity> giftCardEntities = giftCardService.queryByCardNumberList(null, voucherCodeList);
        if (CollectionUtils.isEmpty(giftCardEntities)) {
            return;
        }

        List<GcRegenerateActivationCodeToEmailResponse> list = new ArrayList<>();
        for (GiftCardEntity cardEntity : giftCardEntities) {
            GcRegenerateActivationCodeToEmailResponse response = new GcRegenerateActivationCodeToEmailResponse();
            response.setVoucherCode(cardEntity.getCardNumber());
            response.setNewActivationCode(cardEntity.getActivationCode());
            list.add(response);
        }
        pageData.setList(list);
        // 电子券excel
        String secretCode = voucherNumberHelper.randomPassword(10);
        PagedData<GcRegenerateActivationCodeToEmailResponse> voucherData = PagedDatas
                .<GcRegenerateActivationCodeToEmailResponse, PageData<GcRegenerateActivationCodeToEmailResponse>>builder()
                .query(() -> pageData)
                .queryResultConverter(PageData::getList)
                .hasNextPage(pageResult -> false)
                .afterQuery(() -> log.info("do nothing"))
                .build();

        ExcelExportSpec.SheetBuilder<GcRegenerateActivationCodeToEmailResponse> sheet = ExcelExportSpec.builder()
                .sheet(GcRegenerateActivationCodeToEmailResponse.class, "Gift Card")
                .dataSource(voucherData);


        FileExport fileExport = FileExport.builder()
                .domainCode(GvcoreConstants.SYSTEM_DEFAULT)
                .tenantCode(GvcoreConstants.SYSTEM_DEFAULT)
                .userId(updateUser)
                .name(fileName + ".xlsx")
                .category("voucher.digital")
                .spec(sheet.build())
                .build();

        String accessUrl = "";
        Resp<FileExportResult> result = FileExportResult.from(fileExportManager.export(fileExport));
        accessUrl = result.getData().getAccessUrl();
        final FileCompressionHelper.EncryptCompressionParameter parameter = FileCompressionHelper.EncryptCompressionParameter.builder()
                .fileUrl(accessUrl)
                .fileName(fileName + ".xlsx")
                .password(secretCode)
                .build();
        accessUrl = ossHelper.compressionUploadToOss(parameter, fileName);

        List<String> cpgCodeList = new ArrayList<>();
        List<String> issuerCodeList = new ArrayList<>();
        BigDecimal voucherAmount = BigDecimal.ZERO;
        for (GiftCardEntity voucher : giftCardEntities) {
            cpgCodeList.add(voucher.getCpgCode());
            issuerCodeList.add(voucher.getIssuerCode());
            voucherAmount = voucherAmount.add(voucher.getDenomination());
        }

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put("fileName", fileName + ".zip");
        extendParams.put("businessCode", issueHandlingCode);
        extendParams.put("email", email);
        //发送EXCEL邮件 TEST
        sendExcelToEmail(fileName, ossHelper.grantAccessUrl(accessUrl), extendParams);

        sendPwdToEmail(secretCode, extendParams, fileName);

    }

    private void sendPwdToEmail(String pwd, Map<String, Object> extendParams, String fileName) {
        JSONObject messageRequest = new JSONObject();
        messageRequest.put("eventCode", MessageEnventEnum.REGENERATE_ACTIVATION_CODE_PWD.getCode());
        JSONObject param = new JSONObject();
        param.putAll(extendParams);
        param.put("password", pwd);
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
    }

    private void sendExcelToEmail(String fileName, String fileUrl, Map<String, Object> extendParams) {
        JSONObject messageRequest = new JSONObject();
        messageRequest.put("eventCode", MessageEnventEnum.REGENERATE_ACTIVATION_CODE.getCode());
        JSONObject param = new JSONObject();
        param.putAll(extendParams);
        JSONArray attachments = new JSONArray();
        JSONObject files = new JSONObject();
        files.put("filename", fileName + ".zip");
        files.put("url", fileUrl);
        attachments.add(files);
        param.put("attachments", attachments);
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
    }

}
