package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description: Gift Card Deactivated (Block) Report Detail Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcDeactivatedForDetailBean {

    /**
     * Transaction Date
     */
    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    /**
     * Gift Card Number
     */
    @ExcelProperty(value = "Gift Card Number")
    private String voucherNumber;

    /**
     * Merchant Name
     */
    @ExcelProperty(value = "Merchant Name")
    private String merchant;

    /**
     * Outlet
     */
    @ExcelProperty(value = "Outlet")
    private String merchantOut;

    /**
     * Gift Card Program Group
     */
    @ExcelProperty(value = "Gift Card Program Group")
    private String vpg;

    /**
     * Total Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    /**
     * Customer Name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * Invoice Number
     */
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    /**
     * Deactivated Reason
     */
    @ExcelProperty(value = "Deactivated Reason")
    private String deactivatedReason;
}
