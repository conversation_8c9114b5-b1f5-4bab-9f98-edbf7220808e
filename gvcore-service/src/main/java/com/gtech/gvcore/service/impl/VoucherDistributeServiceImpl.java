package com.gtech.gvcore.service.impl;


import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.PrinterFtpAuthorizationTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.voucherdistribute.CreateVoucherDistributeRequest;
import com.gtech.gvcore.common.response.productcategory.printer.SftpInformationDto;
import com.gtech.gvcore.common.utils.InputStreamUtils;
import com.gtech.gvcore.common.utils.SftpUtil;
import com.gtech.gvcore.dao.mapper.PrinterMapper;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.service.VoucherDistributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import java.io.InputStream;
import java.net.URL;

/**
 * <AUTHOR>
 * @Date 2022/3/2 14:47
 */
@Service
@Slf4j
public class VoucherDistributeServiceImpl implements VoucherDistributeService {

    @Autowired
    private PrinterMapper printerMapper;

    @Override
    public Result<Object> voucherDistribute(String excelFileUrl, SftpInformationDto informationDto, String targetFileName) {

        try {

            URL url = new URL(informationDto.getFtpUrl());
            SftpUtil sftpUtil;
            sftpUtil = getSftpConnect(informationDto, url);

            //创建连接
            sftpUtil.connect();

            InputStream inputStream = InputStreamUtils.getInputStreamByUrl(excelFileUrl);

            if (null == inputStream) {
                return Result.failed(ResultErrorCodeEnum.VOUCHER_DISTRIBUTE_UNABLE_TO_GET_FILE.code(), ResultErrorCodeEnum.VOUCHER_DISTRIBUTE_UNABLE_TO_GET_FILE.desc());
            }

            sftpUtil.upload(url.getPath(), targetFileName, inputStream);
            sftpUtil.disconnect();

        } catch (Exception e) {

            log.error("voucherFTpDistribute Exception:{}", e.getMessage());

            return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
        }

        return Result.ok();
    }

    private SftpUtil getSftpConnect(SftpInformationDto informationDto, URL url) {

        SftpUtil sftpUtil;

        if (PrinterFtpAuthorizationTypeEnum.AUTHORIZATION_TYPE_KEY_FILE.getCode().equals(informationDto.getFtpAuthorizationType())) {
            //解密
            byte[] decode = Base64Utils.decode(informationDto.getFtpKeyFile());
            sftpUtil = new SftpUtil(informationDto.getFtpUsername(), url.getHost(), url.getPort(), decode, informationDto.getPassphrase(), SftpUtil.AUTH_TYPE_PRIVATE_KEY_BYTE);
        }
        //密码
        else {
            sftpUtil = new SftpUtil(informationDto.getFtpUsername(), informationDto.getFtpPassword(), url.getHost(), url.getPort(), informationDto.getPassphrase(), SftpUtil.AUTH_TYPE_PAD);
        }

        return sftpUtil;
    }


    @Override
    public Result<Object> voucherDistributeByPrint(CreateVoucherDistributeRequest distributeRequest) {

        Printer printer = new Printer();
        printer.setPrinterCode(distributeRequest.getPrinterCode());

        int selectCount = printerMapper.selectCount(printer);

        if (selectCount <= 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        Printer selectOne = printerMapper.selectOne(printer);

        SftpInformationDto sftpInformationDto = new SftpInformationDto();

        BeanUtils.copyProperties(selectOne, sftpInformationDto);


        return voucherDistribute(distributeRequest.getExcelFileUrl(), sftpInformationDto, distributeRequest.getTargetFileName());
    }

}
