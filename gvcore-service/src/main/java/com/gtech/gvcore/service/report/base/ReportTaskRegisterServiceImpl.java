package com.gtech.gvcore.service.report.base;

import cn.hutool.core.io.unit.DataUnit;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.config.GTechGitProperties;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportStatusEnum;
import com.gtech.gvcore.dao.mapper.ReportRequestMapper;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.helper.GvRedisTemplate;
import com.gtech.gvcore.helper.RedisLockHelper;
import com.gtech.gvcore.service.report.ReportTaskRegisterService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.PreDestroy;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * @ClassName ReportPressureShuntHelper
 * <AUTHOR>
 * @Date 2023/2/2 15:17
 * @Version V1.0
 **/
@Slf4j
@Order(1)
@Component
public class ReportTaskRegisterServiceImpl implements ApplicationRunner, ReportTaskRegisterService {

    //应用编码自增key
    public static final String APPLICATION_NO_KEY = "REPORT:APPLICATION_NO:INCREASE";
    //报表队列
    public static final String REPORT_QUEUE_KEY = "REPORT:QUEUE";
    //读取睡眠时间
    public static final long POP_SLEEP_TIME = 3 * 1000L;
    //(用户 + 报表类型) 唯一 redis lock key
    public static final String USER_REPORT_TYPE_LOCK_KEY = "REPORT:TYPE:USER";
    //触发拒绝后等待时间 : (用户 + 报表类型) 唯一 新报表尝试生成时重新进入生成逻辑的等待时间
    public static final long REFUSED_WAIT_TIME = 5 * 1000L;

    public static final String LOCK_SET_REDIS_KEY = "REPORT:LOCK:SET";

    //应用编码
    private String applicationNo;

    @Autowired private GvRedisTemplate redisTemplate;
    @Autowired private BuilderReportHelper builderReportHelper;
    @Autowired private ReportRequestMapper reportRequestMapper;
    @Autowired private TaskAsync taskAsync;
    @Autowired private LettuceConnectionFactory lettuceConnectionFactory;

    //最大线程 与核心线程数
    @Value("${report.consumption.thread:10}")
    private Integer consumptionThreadNumber;

    @Autowired
    @Qualifier("RTRSTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Value("${report.consumption.enable:true}")
    private boolean enable;

    @Value("${report.version.check.enable:true}")
    private boolean versionCheckEnable;

    @Autowired
    private GTechGitProperties gitProperties;

    @PreDestroy
    public void close() {

        taskAsync.setClosed(true);

        executor.shutdown();

        lettuceConnectionFactory.destroy();
    }

    //用于处理一直在进行中的报表任务
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void performTask() {

        log.info("报表定时任务执行----定时清除正在进行中的任务");
        // example
        final Example queryReRegister = new Example(ReportRequest.class);
        queryReRegister.createCriteria()
                .andEqualTo(ReportRequest.C_REPORT_STATUS, ReportStatusEnum.PROCESSING.getCode())
                .andLessThanOrEqualTo(ReportRequest.C_ALLOT_TIME, DateUtils.addDays(new Date(),-1));

        ReportRequest reportRequest = new ReportRequest();
        reportRequest.setReportStatus(ReportStatusEnum.FAILED.getCode());

        int i = reportRequestMapper.updateByConditionSelective(reportRequest, queryReRegister);

        log.info("报表定时任务执行----定时清除完成清除—{}条记录",i);

    }

    public String getReportQueueKey() {
        if (versionCheckEnable) {
            return REPORT_QUEUE_KEY + ":" + gitProperties.getCommitIdAbbrev();
        } else {
            return REPORT_QUEUE_KEY;
        }
    }

    @Override
    public void run(ApplicationArguments args) {

        if (!enable) return;

        // applicationNo
        this.applicationNo = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHH + "_"
                + redisTemplate.opsValueIncrement(GvcoreConstants.APP_CODE, APPLICATION_NO_KEY, 1));

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 初始化 [START] => application no : {}, queue = {}, version check enabled: {}",
                applicationNo, getReportQueueKey(), versionCheckEnable);

        //unlock
        this.unlock();

        // re register report
        this.reRegisterReport();

        // running thread
        this.run();

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 初始化 [SUCCESS] => application no : {}", applicationNo);

    }

    /**
     * unlock redis key
     */
    private void unlock() {

        // remove lock begin logger
        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - UNLOCK [RUNNER]");

        //get lock set and foreach set key
        redisTemplate.opsSetMembers(GvcoreConstants.APP_CODE, LOCK_SET_REDIS_KEY, String.class)
                .forEach(e -> {

                    // logger
                    log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - UNLOCK => unlock key = {}", e);

                    // unlock and remove set key
                    redisTemplate.opsSetRemove(GvcoreConstants.APP_CODE, LOCK_SET_REDIS_KEY, e);
                    RedisLockHelper.unLockRedisKey(e);
                });

        // remove lock end logger
        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - UNLOCK [END]");
    }

    /**
     * restart application times
     * re register report
     */
    private void reRegisterReport() {

        // example
        final Example queryReRegister = new Example(ReportRequest.class);
        queryReRegister.createCriteria()
                .andIsNull(ReportRequest.C_APPLICATION_NO)
                .andIsNull(ReportRequest.C_ALLOT_TIME)
                .andIsNull(ReportRequest.C_EXECUTE_TIME_MS);

        // select
        List<ReportRequest> reportRequests = reportRequestMapper.selectByCondition(queryReRegister);
        if (CollectionUtils.isEmpty(reportRequests)) return;

        // re register
        reportRequests.stream()
                .map(ReportRequest::getReportCode)
                .filter(e -> redisTemplate.opsSetAdd(GvcoreConstants.APP_CODE, getReportQueueKey(), e) > 0)
                .forEach(e -> log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 注册任务[过期] reportCode => {}", e));
    }

    /**
     *  register report task
     */
    public void register(String reportCode, boolean local) {

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 注册任务 reportCode => {}", reportCode);

        // local running
        if (local) this.executeBuilder(reportCode);
            // add queue
        else redisTemplate.opsSetAdd(GvcoreConstants.APP_CODE, getReportQueueKey(), reportCode);

    }
    public void register(String reportCode) {

        // add queue
        this.register(reportCode, false);
    }

    /**
     * get queue report code
     *  WAIT
     *      @see ReportTaskRegisterServiceImpl#POP_SLEEP_TIME
     *  SYNCHRONIZED:
     *      block work thread
     * @return report code
     */
    private synchronized String popReportCode() {

        // redis read report code
        String reportCode = redisTemplate.opsSetPop(GvcoreConstants.APP_CODE, getReportQueueKey(), String.class);

        // not found sleep
        if (null == reportCode) sleep(POP_SLEEP_TIME);

        // return
        return reportCode;
    }

    /**
     * get redis lock key
     * @param reportCode report code
     * @return user_code + report_type
     */
    private String getLockReportValue(@NotNull String reportCode) {

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }
        // find report request
        ReportRequest reportRequest = this.reportRequestMapper.selectOne(new ReportRequest().setReportCode(reportCode));
        HintManager.clear();
        // user_code + report_type
        return reportRequest.getCreateUser() + "_" + reportRequest.getReportType();
    }

    /**
     * lock report database
     * @param reportCode report code
     * @param allotTime allot time
     * @return 是否成功锁定该数据
     */
    private boolean dbLock(@NotBlank String reportCode, long allotTime) {

        // example
        final Example updateExample = new Example(ReportRequest.class);
        updateExample.createCriteria()
                .andEqualTo(ReportRequest.C_REPORT_CODE, reportCode)
                .andIsNull(ReportRequest.C_APPLICATION_NO)
                .andIsNull(ReportRequest.C_ALLOT_TIME)
                .andIsNull(ReportRequest.C_EXECUTE_TIME_MS);

        // update lock report
        return this.reportRequestMapper.updateByConditionSelective(new ReportRequest()
                .setApplicationNo(applicationNo)
                .setAllotTime(new Date(allotTime)), updateExample) > 0;
    }

    /**
     * work thread execute
     * builder work thread
     * 通过 pop report code 方法 同步调用来阻塞与睡眠
     */
    private void run() {

        // for generate thread
        for (int i = 0; i < consumptionThreadNumber; i++)
            taskAsync.runConsumer(() -> {
                try {
                    // pop report code
                    String reportCode = popReportCode();
                    if (null == reportCode) return;

                    log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - [工作线程接收到任务] reportCode => {}", reportCode);

                    // execute
                    execute(reportCode);

                    log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - [工作线程任务处理结束] reportCode => {}", reportCode);

                } catch (Exception e) {

                    if (e instanceof RedisSystemException || e instanceof RedisConnectionFailureException) {
                        //ignore
                        return;
                    }

                    log.error("ReportTaskRegisterServiceImpl 报表任务注册服务 - [工作线程错误警告] 调度器执行发生异常", e);
                }
            });
    }

    /**
     * execute builder report task
     * @param reportCode
     */
    private void execute(@NotNull String reportCode) {

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 尝试构建 [TRY - BUILDER] reportCode => {}", reportCode);


        log.debug("ReportTaskRegisterServiceImpl 报表任务注册服务 - 获取业务锁(REDIS USER_REPORT) [REDIS LOCK] reportCode => {}", reportCode);
        // get redis lock key
        final String lockReportValue = getLockReportValue(reportCode);
        // get redis lock
        if (!this.tryLock(lockReportValue)) {
            refused(reportCode);
            return;
        }

        try {
            // execute builder
            executeBuilder(reportCode);
        } finally {
            // unlock
            RedisLockHelper.unLock(USER_REPORT_TYPE_LOCK_KEY, lockReportValue);
        }

    }

    /**
     * try lock
     * @param lockReportValue
     * @return true: lock success
     */
    private boolean tryLock(String lockReportValue) {

        // try lock
        String redisKey = RedisLockHelper.tryLockAndGetRedisKey(USER_REPORT_TYPE_LOCK_KEY, lockReportValue, 3 * 1000L, 30 * 60 * 1000L);

        // lock error
        if (StringUtils.isBlank(redisKey)) return false;

        // add lock set
        redisTemplate.opsSetAdd(GvcoreConstants.APP_CODE, LOCK_SET_REDIS_KEY, redisKey);

        // lock success
        return true;
    }

    /**
     * execute builder
     * @param reportCode
     */
    private void executeBuilder(String reportCode) {

        log.debug("ReportTaskRegisterServiceImpl 报表任务注册服务 - 获取业务锁(DB) [DB LOCK] reportCode => {}", reportCode);
        // allot time
        long allotTime = System.currentTimeMillis();
        // lock report
        if (!dbLock(reportCode, allotTime)) {
            log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 任务拒绝 报表被锁定 [丢弃报表] reportCode => {}", reportCode);
            return;
        }

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 构建报表 [RUNNING] reportCode => {}", reportCode);

        // execute builder
        builderReportHelper.builder(reportCode);

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 构建报表 [SUCCESS] reportCode => {}", reportCode);

        // execute finish
        this.finish(reportCode, allotTime);

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 构建结束 [STOP] reportCode => {}", reportCode);
    }

    /**
     * 结束方法
     * 完成时更新执行时间
     * @param reportCode
     * @param allotTime
     */
    private void finish(@NotBlank String reportCode, @NotNull Long allotTime) {

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        // example
        final Example updateExample = new Example(ReportRequest.class);
        updateExample.createCriteria()
                .andEqualTo(ReportRequest.C_REPORT_CODE, reportCode);

        // update
        this.reportRequestMapper.updateByConditionSelective(new ReportRequest().setExecuteTimeMs(String.valueOf(System.currentTimeMillis() - allotTime))
                , updateExample);

        HintManager.clear();

    }

    /**
     * 任务拒绝
     * 任务拒绝后扣留任务在本地延时时间达到后重新注册
     * @param reportCode
     */
    private void refused(String reportCode) {

        log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - 任务拒绝 用户该报表队列阻塞 [等待后重试] reportCode => {}", reportCode);

        // wait time
        taskAsync.runRefused(() -> this.register(reportCode));
    }

    /**
     * sleep
     * @param time
     */
    private static void sleep(long time) {
        try {
            Thread.sleep(time); // sleep
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Component("ReportTaskRegisterServiceTaskAsync")
    public static class TaskAsync {

        //队列禁用
        @Value("${report.queue.disable:false}")
        private boolean queueDisable;

        @Setter
        private boolean closed = false;

        @Async("RTRSTaskExecutor")
        public void runConsumer(Runnable runnable) {

            log.info("ReportTaskRegisterServiceImpl 报表任务注册服务 - [Thread init] Thread => {}", Thread.currentThread().getName());

            while (!queueDisable && !closed) runnable.run();
        }

        @Async("reportReturnRefusedTaskExecutor")
        public void runRefused (Runnable runnable) {

            ReportTaskRegisterServiceImpl.sleep(REFUSED_WAIT_TIME);
            runnable.run();
        }

    }

    @Configuration
    public static class ReportTaskRegisterServiceConfiguration {

        //最大线程 与核心线程数
        @Value("${report.consumption.thread:10}")
        private Integer consumptionThreadNumber;

        @Bean("RTRSTaskExecutor")
        public ThreadPoolTaskExecutor reportTaskRegisterServiceTaskExecutor() {
            ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

            threadPoolTaskExecutor.setCorePoolSize(consumptionThreadNumber);
            threadPoolTaskExecutor.setMaxPoolSize(consumptionThreadNumber);

            return threadPoolTaskExecutor;
        }

        @Bean("reportReturnRefusedTaskExecutor")
        public Executor reportReturnRefusedTaskExecutor() {
            return new ThreadPoolTaskExecutor();
        }
    }

}