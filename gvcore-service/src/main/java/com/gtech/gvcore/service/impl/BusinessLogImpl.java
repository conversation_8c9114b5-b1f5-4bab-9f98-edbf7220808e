package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.BusinessLogStatusEnum;
import com.gtech.gvcore.common.request.businesslog.CreateBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslog.QueryBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.request.businesslogdetail.QueryBusinessLogDetailRequest;
import com.gtech.gvcore.common.response.businesslog.BusinessLogResponse;
import com.gtech.gvcore.common.response.businesslogdetail.BusinessLogDetailResponse;
import com.gtech.gvcore.dao.mapper.BusinessLogMapper;
import com.gtech.gvcore.dao.model.BusinessLog;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.BusinessLogDetailService;
import com.gtech.gvcore.service.BusinessLogService;
import com.gtech.gvcore.service.GvUserAccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/11 17:11
 */
@Service
public class BusinessLogImpl implements BusinessLogService {


    @Autowired
    private BusinessLogMapper businessLogMapper;

    @Autowired
    private BusinessLogDetailService businessLogDetailService;

    @Autowired
    private GvCodeHelper codeHelper;
    
    @Autowired
    private GvUserAccountService userAccountService;


    @Override
    public Result<Void> createBusinessLog(CreateBusinessLogRequest request) {
        request.setCreateTime(new Date());
        BusinessLog businessLog = BeanCopyUtils.jsonCopyBean(request, BusinessLog.class);
        businessLog.setBusinessCode(codeHelper.generateBusinessLogCode());
        if (businessLog.getStatus() == null) {
        	businessLog.setStatus(BusinessLogStatusEnum.SUCCESS.code());
        }
        businessLogMapper.insertSelective(businessLog);

        if (CollectionUtils.isNotEmpty(request.getBusinessLogDetailList())){
            for (CreateBusinessLogDetailRequest detailRequest : request.getBusinessLogDetailList()) {
                detailRequest.setBusinessCode(businessLog.getBusinessCode());
                detailRequest.setCreateUser(request.getCreateUser());
                detailRequest.setCreateTime(request.getCreateTime());
            }
            businessLogDetailService.createBusinessLogDetailList(request.getBusinessLogDetailList());
        }

        return Result.ok();
    }

    @Override
    public PageResult<BusinessLogResponse> queryBusinessLog(QueryBusinessLogRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        Example example = new Example(BusinessLog.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtil.isNotEmpty(request.getBusinessLogCode())){
            criteria.andEqualTo(BusinessLog.C_BUSINESS_CODE, request.getBusinessLogCode());
        }
        if (StringUtil.isNotEmpty(request.getContentCode())){
            criteria.andEqualTo(BusinessLog.C_CONTENT_CODE, request.getContentCode());
        }
        example.orderBy("id").desc();
        List<BusinessLog> businessLogs = businessLogMapper.selectByCondition(example);
        PageInfo<BusinessLog> pageInfo = PageInfo.of(businessLogs);

        List<BusinessLogResponse> businessLogResponses = BeanCopyUtils.jsonCopyList(pageInfo.getList(), BusinessLogResponse.class);
        List<String> userCodeList = businessLogResponses.stream().map(BusinessLogResponse::getCreateUser).collect(Collectors.toList());
        Map<String, String> userMap = userAccountService.queryFullNameByCodeList(userCodeList);
        for (BusinessLogResponse businessLog : businessLogResponses) {
            QueryBusinessLogDetailRequest queryBusiness = new QueryBusinessLogDetailRequest();
            queryBusiness.setBusinessCode(businessLog.getBusinessCode());
            List<BusinessLogDetailResponse> businessLogDetailResponses = businessLogDetailService.queryBusinessLogDetail(queryBusiness);
            if (CollectionUtils.isNotEmpty(businessLogDetailResponses)){

                businessLogDetailResponses.forEach(businessLogDetailResponse -> {
                    String reason = businessLogDetailResponse.getReason();
                    businessLogDetailResponse.setReason(JSONObject.parseObject(reason).getString("desc"));
                });

                businessLog.setBusinessLogDetailList(businessLogDetailResponses);
            }
            String createUser = businessLog.getCreateUser();
            businessLog.setCreateUser(StringUtil.isEmpty(userMap.get(createUser)) ? createUser : userMap.get(createUser));
        }

        return new PageResult<>(businessLogResponses,pageInfo.getTotal());
    }
}
