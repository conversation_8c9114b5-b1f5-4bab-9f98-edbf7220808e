package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RegenerateActivationCodeQueryDate extends PageParam implements ReportQueryParam {

    /**
     * Request Date
     * Start Voucher Number
     * End Voucher Number
     * Customer email
     * Upload CSV file
     */


    String issueHandlingCode;
    Long startVoucherCode;
    Long endVoucherCode;
    String customerEmail;
    Date startDate;
    Date endDate;
    List<String> voucherCodes;

}
