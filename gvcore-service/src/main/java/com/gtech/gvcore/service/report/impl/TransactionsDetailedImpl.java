package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.ManualPollBusinessReport;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertUtils;
import com.gtech.gvcore.service.report.impl.bean.TransactionsDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.TransactionDetailedBo;
import com.gtech.gvcore.service.report.impl.param.TransactionDetailQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:01
 * @Description:
 */
@Service
public class TransactionsDetailedImpl extends ManualPollBusinessReport<TransactionDetailQueryData, TransactionsDetailedBean, TransactionDetailedBo>
        implements BusinessReport<TransactionDetailQueryData, TransactionsDetailedBean>, PollReport {

    @Autowired
    private VoucherMapper voucherMapper;
    @Autowired
    private CustomerOrderMapper customerOrderMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.TRANSACTIONS_DETAILED_SUMMARY_REPORT;
    }

    @Override
    public TransactionDetailQueryData builderQueryParam(CreateReportRequest reportParam) {

        TransactionDetailQueryData transactionDetailReportRequest = new TransactionDetailQueryData();

        transactionDetailReportRequest.setTransactionDateStart(reportParam.getTransactionDateStart());
        transactionDetailReportRequest.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        transactionDetailReportRequest.setIssuerCodes(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        transactionDetailReportRequest.setMerchantCodes(reportParam.getMerchantCodes());
        transactionDetailReportRequest.setOutletCodes(reportParam.getOutletCodes());
        transactionDetailReportRequest.setCpgCodes(reportParam.getCpgCodes());
        transactionDetailReportRequest.setTransactionType(reportParam.getTransactionTypes());
        transactionDetailReportRequest.setTransactionStatus(reportParam.getTransactionStatus());

        transactionDetailReportRequest.setInvoiceNumber(reportParam.getInvoiceNo());
        transactionDetailReportRequest.setCustomerType(reportParam.getCustomerType());
        transactionDetailReportRequest.setCustomerCodes(reportParam.getCustomerCodes());

        transactionDetailReportRequest.setBulkOrderStatus(reportParam.getOrderStatuses());
        transactionDetailReportRequest.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        transactionDetailReportRequest.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());
        transactionDetailReportRequest.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        transactionDetailReportRequest.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        // set voucher code list
        transactionDetailReportRequest.setPurchaseOrderVoucherCodeList(selectVoucherCodeByPurchaseOrderNo(reportParam.getPurchaseOrderNo()));

        // set customer order no list
        transactionDetailReportRequest.setBulkOrderStatusTransactionIdList(selectCustomerOrderNoByStatus(reportParam.getOrderStatuses()));

        return transactionDetailReportRequest;
    }

    /**
     * select customer order no by status
     *
     * @param customerOrderStatusList customer order status list
     * @return customer order no list
     */
    private List<String> selectCustomerOrderNoByStatus(List<String> customerOrderStatusList) {

        // if customer order status list is empty, return empty list
        if (CollectionUtils.isEmpty(customerOrderStatusList)) return Collections.emptyList();

        // select customer order no by status
        final Example example = new Example(CustomerOrder.class);
        example.createCriteria()
                .andIn(CustomerOrder.C_STATUS, customerOrderStatusList);

        // select customer order no
        example.selectProperties(CustomerOrder.C_CUSTOMER_ORDER_CODE);

        // select customer order no list
        final List<CustomerOrder> customerOrders = customerOrderMapper.selectByCondition(example);

        // if customer order no list is empty, return empty list
        if (CollectionUtils.isEmpty(customerOrders)) return Collections.emptyList();

        // return customer order no list
        return customerOrders.stream().map(CustomerOrder::getCustomerOrderCode).collect(Collectors.toList());
    }

    /**
     * select voucher code by purchase order no
     *
     * @param purchaseOrderNo purchase order no
     * @return voucher code list
     */
    private List<String> selectVoucherCodeByPurchaseOrderNo(final String purchaseOrderNo) {

        // if purchase order no is empty, return empty list
        if (StringUtils.isBlank(purchaseOrderNo)) return Collections.emptyList();

        // select voucher code by purchase order no
        final Example example = new Example(Voucher.class);
        example.createCriteria()
                .andEqualTo(Voucher.C_VOUCHER_BATCH_CODE, purchaseOrderNo);

        // select voucher code
        example.selectProperties(Voucher.C_VOUCHER_CODE);

        // select voucher code list
        final List<Voucher> vouchers = voucherMapper.selectByCondition(example);

        // if voucher code list is empty, return empty list
        if (CollectionUtils.isEmpty(vouchers)) return Collections.emptyList();

        // return voucher code list
        return vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList());
    }


    @Override
    protected List<TransactionDetailedBo> findDate(TransactionDetailQueryData param, RowBounds rowBounds) {

        // select transaction detail report
        return reportBusinessMapper.transactionDetailReport(param, rowBounds);
    }

    @Override
    public List<TransactionDetailedBo> filter(TransactionDetailQueryData param, List<TransactionDetailedBo> boList) {

        // filter voucher status
        this.filterVoucherStatus(boList, param.getVoucherStatusList());

        // return bo list
        return boList;
    }

    @Override
    protected List<TransactionsDetailedBean> getExportData(TransactionDetailQueryData param, List<TransactionDetailedBo> boList) {

        final JoinDataMap<UserAccount> userAccountMap = super.getMapByCode(boList, TransactionDetailedBo::getCreateUser, UserAccount.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, TransactionDetailedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, TransactionDetailedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(boList, TransactionDetailedBo::getPosCode, Pos.class);

        // if bo list is empty, return empty list
        if (CollectionUtils.isEmpty(boList)) return Collections.emptyList();

        // convert to export data
        return boList.stream()
                .map(e -> new TransactionsDetailedBean()
                        .setTransactionType(TransactionTypeEnum.getTypeDesc(e.getTransactionType()))
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setOutlet(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setTransactionDate(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_DEFAULT))
                        .setVoucherNumber(e.getVoucherCode())
                        .setInitiatedBy(e.getCreateUser(userAccountMap))
                        .setPosName(null != posMap.findValue(e.getPosCode()) && StringUtil.isNotEmpty(posMap.findValue(e.getPosCode()).getPosName()) ? posMap.findValue(e.getPosCode()).getPosName() : "GV POS")
                        .setTerminal(posMap.findValue(e.getPosCode()).getMachineId())
                        .setOrganizationName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setBatchNumber(e.getBatchCode())
                        .setLoginSource(e.getLoginSource())
                        .setDenomination(toAmount(e.getDenomination()))
                        .setActualOutlet(e.getActualOutlet())
                        .setForwardingEntityId(e.getForwardingEntityId())
                        .setResponseMessage(e.getResponseMessage())
                        .setTransactionMode(e.getTransactionMode())
                        .setCustomerSalutation(e.getCustomerSalutation())
                        .setCustomerFirstName(e.getCustomerFirstName())
                        .setCustomerLastName(e.getCustomerLastName())
                        .setMobile(e.getMobile())
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setOtherInputParameter("")
                ).collect(Collectors.toList());
    }

    /**
     * filter voucher status
     * @param boList bo list
     * @param voucherStatusList voucher status list
     */
    private void filterVoucherStatus(List<TransactionDetailedBo> boList, List<String> voucherStatusList) {

        // if voucher status list is empty, return
        if (CollectionUtils.isEmpty(voucherStatusList)) return;

        // get voucher map
        JoinDataMap<Voucher> voucherMap = super.getMapByCode(boList, TransactionDetailedBo::getVoucherCode, Voucher.class,
                ReportVoucherStatusConvertUtils.SELECT_VOUCHER_STATUS_FIELDS);

        // filter voucher status
        boList.removeIf(transaction -> {
            Voucher voucher = voucherMap.get(transaction.getVoucherCode());
            ReportVoucherStatusEnum voucherStatus = this.getVoucherStatus(voucher);
            return voucherStatus != null && !voucherStatusList.contains(String.valueOf(voucherStatus.getCode()));
        });
    }
}
