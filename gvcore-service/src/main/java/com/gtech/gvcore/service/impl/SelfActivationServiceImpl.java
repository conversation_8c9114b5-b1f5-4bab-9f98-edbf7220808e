package com.gtech.gvcore.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.selfactivation.ActivateVouchersRequest;
import com.gtech.gvcore.common.request.selfactivation.GetActivationInfoRequest;
import com.gtech.gvcore.common.request.selfactivation.RequestOtpRequest;
import com.gtech.gvcore.common.request.selfactivation.ResendActivationEmailRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.response.selfactivation.ActivateVouchersResponse;
import com.gtech.gvcore.common.response.selfactivation.GetActivationInfoResponse;
import com.gtech.gvcore.common.response.selfactivation.RequestOtpResponse;
import com.gtech.gvcore.dao.mapper.SelfActivationLogMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.SelfActivationLog;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.SelfActivationService;
import com.gtech.gvcore.service.VoucherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Self-activation service implementation
 */
@Slf4j
@Service
public class SelfActivationServiceImpl implements SelfActivationService {
    
    private static final String REDIS_TOKEN_PREFIX = "activation:token:";
    private static final String REDIS_OTP_PREFIX = "activation:otp:";
    private static final long TOKEN_EXPIRY_DAYS = 30;
    private static final long OTP_EXPIRY_MINUTES = 5;
    private static final String TOKEN_STATUS_PENDING = "PENDING";
    private static final String TOKEN_STATUS_ACTIVATED = "ACTIVATED";
    private static final String TOKEN_STATUS_EXPIRED = "EXPIRED";
    
    @Autowired
    private SelfActivationLogMapper selfActivationLogMapper;
    
    @Autowired
    private VoucherMapper voucherMapper;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private MessageComponent messageComponent;
    
    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private VoucherService voucherService;

    @Autowired
    private CustomerOrderService customerOrderService;
    
    @Override
    public Result<GetActivationInfoResponse> getActivationInfo(GetActivationInfoRequest request) {
        try {
            log.info("Getting activation info for token: {}", request.getToken());
            
            // Check token in Redis
            String customerOrderCode = stringRedisTemplate.opsForValue().get(REDIS_TOKEN_PREFIX + request.getToken());
            if (StringUtil.isEmpty(customerOrderCode)) {
                return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "Activation link does not exist or has expired");
            }
            
            // Get activation log from database
            SelfActivationLog selfActivationLog = new SelfActivationLog();
            selfActivationLog.setActivationToken(request.getToken());
            selfActivationLog.setTokenStatus(TOKEN_STATUS_PENDING);
            
            SelfActivationLog activationLog = selfActivationLogMapper.selectOne(selfActivationLog);
            if (activationLog == null) {
                return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "Activation link does not exist or has expired");
            }


            // Count vouchers for this order using the correct method
            List<Voucher> vouchers = customerOrderService.queryPhysicalVouchersByCustomerOrderCode(customerOrderCode);
            int voucherCount = vouchers != null ? vouchers.size() : 0;
            
            GetActivationInfoResponse response = GetActivationInfoResponse.builder()
                    .customerOrderCode(customerOrderCode)
                    .email(activationLog.getCustomerEmail())
                    .voucherCount(voucherCount)
                    .build();
            
            return Result.ok(response);
            
        } catch (Exception e) {
            log.error("Error getting activation info", e);
            return Result.failed(ResultErrorCodeEnum.FAILED.code(), "Failed to get activation information");
        }
    }


    public List<Voucher> getVouchersByCustomerOrderCode(String customerOrderCode) {
        // 1. 先查询订单信息，获取 voucherBatchCode
        CustomerOrder customerOrder = customerOrderService.getCustomerOrder(customerOrderCode);

        if (customerOrder == null || StringUtil.isEmpty(customerOrder.getVoucherBatchCode())) {
            return Collections.emptyList();
        }

        // 2. 根据 voucherBatchCode 查询所有券
        List<Voucher> vouchers = voucherService.queryVoucherListByVoucherBatchCode(customerOrder.getVoucherBatchCode());

        return vouchers;
    }
    
    @Override
    public Result<RequestOtpResponse> requestOtp(RequestOtpRequest request) {
        try {
            log.info("Requesting OTP for token: {}", request.getToken());
            
            // Verify token exists in Redis
            String customerOrderCode = stringRedisTemplate.opsForValue().get(REDIS_TOKEN_PREFIX + request.getToken());
            if (StringUtil.isEmpty(customerOrderCode)) {
                return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "Activation link does not exist or has expired");
            }
            
            // Generate 8-digit OTP
            String otp = generateOtp();
            
            // Store OTP in Redis with 5-minute expiry
            stringRedisTemplate.opsForValue().set(REDIS_OTP_PREFIX + request.getToken(), otp, OTP_EXPIRY_MINUTES, TimeUnit.MINUTES);
            
            // Get customer email from activation log
            SelfActivationLog selfActivationLog = new SelfActivationLog();
            selfActivationLog.setActivationToken(request.getToken());
            SelfActivationLog activationLog = selfActivationLogMapper.selectOne(selfActivationLog);
            
            if (activationLog != null) {
                // Send OTP email asynchronously
                sendOtpEmailAsync(activationLog.getCustomerEmail(), otp);
            }
            
            RequestOtpResponse response = RequestOtpResponse.builder()
                    .success(true)
                    .message("OTP has been sent to your email.")
                    .build();
            
            return Result.ok(response);
            
        } catch (Exception e) {
            log.error("Error requesting OTP", e);
            return Result.failed(ResultErrorCodeEnum.FAILED.code(), "Failed to send OTP");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ActivateVouchersResponse> activateVouchers(ActivateVouchersRequest request) {
        try {
            log.info("Activating vouchers for token: {}", request.getToken());
            
            // Get customer order code from Redis
            String customerOrderCode = stringRedisTemplate.opsForValue().get(REDIS_TOKEN_PREFIX + request.getToken());
            if (StringUtil.isEmpty(customerOrderCode)) {
                return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "Activation link does not exist or has expired");
            }
            
            // Verify OTP
            String storedOtp = stringRedisTemplate.opsForValue().get(REDIS_OTP_PREFIX + request.getToken());
            if (StringUtil.isEmpty(storedOtp) || !storedOtp.equals(request.getOtp())) {
                return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "OTP is incorrect or has expired");
            }
            
            // Update vouchers status to ACTIVATED using the correct method
            List<Voucher> vouchers = customerOrderService.queryPhysicalVouchersByCustomerOrderCode(customerOrderCode);
            int updatedCount = 0;

            if (vouchers != null && !vouchers.isEmpty()) {
                // Filter vouchers that are in CREATED status and update them to ACTIVATED
                List<String> voucherCodes = vouchers.stream()
                        .filter(v -> VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(v.getStatus()))
                        .map(Voucher::getVoucherCode)
                        .collect(Collectors.toList());

                if (!voucherCodes.isEmpty()) {
                    UpdateVoucherStatusRequest updateRequest = new UpdateVoucherStatusRequest();
                    updateRequest.setOldStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
                    updateRequest.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
                    updateRequest.setVoucherCodeList(voucherCodes);
                    updateRequest.setUpdateUser(gvCodeHelper.generateCustomerCode());

                    updatedCount = voucherService.updateVoucherStatus(updateRequest);
                }
            }

            log.info("Updated {} vouchers to ACTIVATED status for order: {}", updatedCount, customerOrderCode);
            
            // Update activation log status
            Example logExample = new Example(SelfActivationLog.class);
            logExample.createCriteria().andEqualTo(SelfActivationLog.C_ACTIVATION_TOKEN, request.getToken());
            
            SelfActivationLog updateLog = new SelfActivationLog();
            updateLog.setTokenStatus(TOKEN_STATUS_ACTIVATED);
            updateLog.setUpdatedAt(new Date());
            
            selfActivationLogMapper.updateByConditionSelective(updateLog, logExample);
            
            // Clean up Redis
            stringRedisTemplate.delete(REDIS_TOKEN_PREFIX + request.getToken());
            stringRedisTemplate.delete(REDIS_OTP_PREFIX + request.getToken());
            
            // Send activation success email asynchronously
            SelfActivationLog selfActivationLog = new SelfActivationLog();
            selfActivationLog.setActivationToken(request.getToken());
            SelfActivationLog activationLog = selfActivationLogMapper.selectOne(selfActivationLog);
            if (activationLog != null) {
                sendActivationSuccessEmailAsync(activationLog.getCustomerEmail(), customerOrderCode);
            }
            
            ActivateVouchersResponse response = ActivateVouchersResponse.builder()
                    .success(true)
                    .message("Vouchers activated successfully.")
                    .build();
            
            return Result.ok(response);
            
        } catch (Exception e) {
            log.error("Error activating vouchers", e);
            return Result.failed(ResultErrorCodeEnum.FAILED.code(), "Failed to activate vouchers");
        }
    }
    
    @Override
    public Result<String> resendActivationEmail(ResendActivationEmailRequest request) {
        try {
            log.info("Resending activation email for order: {}", request.getCustomerOrderCode());
            
            // Find pending activation log
            SelfActivationLog selfActivationLog = new SelfActivationLog();
            selfActivationLog.setCustomerOrderCode(request.getCustomerOrderCode());
            selfActivationLog.setActivationToken(TOKEN_STATUS_PENDING);

            
            SelfActivationLog activationLog = selfActivationLogMapper.selectOne(selfActivationLog);
            if (activationLog == null) {
                return Result.failed(ResultErrorCodeEnum.PARAMTER_ERROR.code(), "No pending activation found for this order");
            }

            Example example = new Example(SelfActivationLog.class);
            example.createCriteria()
                    .andEqualTo(SelfActivationLog.C_CUSTOMER_ORDER_CODE, request.getCustomerOrderCode());

            // Update email if provided
            String emailToUse = activationLog.getCustomerEmail();
            if (!StringUtil.isEmpty(request.getNewEmail())) {
                SelfActivationLog updateLog = new SelfActivationLog();
                updateLog.setCustomerEmail(request.getNewEmail());
                updateLog.setUpdatedAt(new Date());
                selfActivationLogMapper.updateByConditionSelective(updateLog, example);
                emailToUse = request.getNewEmail();
            }
            
            // Send activation email asynchronously
            sendActivationEmailAsync(emailToUse, activationLog.getActivationToken());
            
            return Result.ok("Activation email has been resent successfully");
            
        } catch (Exception e) {
            log.error("Error resending activation email", e);
            return Result.failed(ResultErrorCodeEnum.FAILED.code(), "Failed to resend activation email");
        }
    }
    
    @Override
    public Result<String> createSelfActivationTask(String customerOrderCode, String customerEmail) {
        try {
            log.info("Creating self-activation task for order: {}", customerOrderCode);
            
            // Generate unique activation token
            String activationToken = UUID.randomUUID().toString();
            
            // Calculate expiry date (30 days from now)
            Date expiryDate = Date.from(LocalDateTime.now().plusDays(TOKEN_EXPIRY_DAYS).atZone(ZoneId.systemDefault()).toInstant());
            
            // Create activation log
            SelfActivationLog activationLog = SelfActivationLog.builder()
                    .customerOrderCode(customerOrderCode)
                    .activationToken(activationToken)
                    .tokenStatus(TOKEN_STATUS_PENDING)
                    .tokenExpiryAt(expiryDate)
                    .customerEmail(customerEmail)
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();
            
            selfActivationLogMapper.insertSelective(activationLog);
            
            // Store token in Redis with 30-day expiry
            stringRedisTemplate.opsForValue().set(
                    REDIS_TOKEN_PREFIX + activationToken, 
                    customerOrderCode, 
                    TOKEN_EXPIRY_DAYS, 
                    TimeUnit.DAYS
            );
            
            // Send activation email asynchronously
            sendActivationEmailAsync(customerEmail, activationToken);
            
            return Result.ok("Self-activation task created successfully");
            
        } catch (Exception e) {
            log.error("Error creating self-activation task", e);
            return Result.failed(ResultErrorCodeEnum.FAILED.code(), "Failed to create self-activation task");
        }
    }
    
    private String generateOtp() {
        Random random = new Random();
        return String.format("%08d", random.nextInt(100000000));
    }
    
    @Async
    public void sendActivationEmailAsync(String email, String token) {
        try {
            JSONObject emailRequest = new JSONObject();
            emailRequest.put("to", email);
            emailRequest.put("subject", "Activate Your Gift Cards");
            emailRequest.put("templateCode", "SELF_ACTIVATION");
            
            JSONObject templateData = new JSONObject();
            templateData.put("activationUrl", "https://your-frontend.com/activate?token=" + token);
            emailRequest.put("templateData", templateData);
            
            messageComponent.sendEmail(emailRequest);
            log.info("Activation email sent to: {}", email);
        } catch (Exception e) {
            log.error("Failed to send activation email to: " + email, e);
        }
    }
    
    @Async
    public void sendOtpEmailAsync(String email, String otp) {
        try {
            JSONObject emailRequest = new JSONObject();
            emailRequest.put("to", email);
            emailRequest.put("subject", "Your Activation Code");
            emailRequest.put("templateCode", "ACTIVATION_OTP");
            
            JSONObject templateData = new JSONObject();
            templateData.put("otp", otp);
            emailRequest.put("templateData", templateData);
            
            messageComponent.sendEmail(emailRequest);
            log.info("OTP email sent to: {}", email);
        } catch (Exception e) {
            log.error("Failed to send OTP email to: " + email, e);
        }
    }
    
    @Async
    public void sendActivationSuccessEmailAsync(String email, String customerOrderCode) {
        try {
            JSONObject emailRequest = new JSONObject();
            emailRequest.put("to", email);
            emailRequest.put("subject", "Gift Cards Activated Successfully");
            emailRequest.put("templateCode", "ACTIVATION_SUCCESS");
            
            JSONObject templateData = new JSONObject();
            templateData.put("customerOrderCode", customerOrderCode);
            emailRequest.put("templateData", templateData);
            
            messageComponent.sendEmail(emailRequest);
            log.info("Activation success email sent to: {}", email);
        } catch (Exception e) {
            log.error("Failed to send activation success email to: " + email, e);
        }
    }
}
