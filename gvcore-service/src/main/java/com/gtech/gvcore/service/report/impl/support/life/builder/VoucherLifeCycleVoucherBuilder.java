package com.gtech.gvcore.service.report.impl.support.life.builder;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.TableHelper;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertSupport;
import com.gtech.gvcore.service.report.impl.bean.CardLifeCycleBean;
import com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.life.VoucherLifeCycleAbstract;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName CardLifeCycleVoucherBuilder
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 18:42
 * @Version V1.0
 **/
@Service
public class VoucherLifeCycleVoucherBuilder extends VoucherLifeCycleAbstract<CardLifeCycleBean> implements ReportVoucherStatusConvertSupport {

    private static final TransactionData DEFAULT_TRANSACTION_DATA = new TransactionData();

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.CARD_LIFE_CYCLE_REPORT;
    }

    @Override
    public Class<?> getExportDataClass() {
        return CardLifeCycleBean.class;
    }

    @Override
    public String getFillKey() {
        return "cycle";
    }

    @Override
    public List<CardLifeCycleBean> builder(VoucherLifeCycleQueryData queryData) {

        Voucher voucher = voucherService.getByVoucherCodeNumRangeReturnByPage(queryData.getVoucherCode(), null);

        if (null == voucher) return Collections.emptyList();

        List<TransactionData> transactionDataList = super.reportBusinessMapper.selectVoucherLifeCycleTransactionDataByVoucherCodeNum(voucher.getVoucherCodeNum(), TableHelper.getTableIndex(voucher.getVoucherCode()));

        //sell transaction
        final TransactionData sellingTransaction = transactionDataService.selectLastTransactionDataByVoucherAndType(voucher.getVoucherCode(), TransactionTypeEnum.GIFT_CARD_SELL, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL);
        // setting selling date
        final Date sellingDate = GvConvertUtils.toObject(sellingTransaction, new TransactionData()).getTransactionDate();

        Cpg cpg = super.nonNullGetByCode(voucher.getCpgCode(), Cpg.class);
        Outlet outlet = this.getVoucherOutlet(voucher, transactionDataList);
        Merchant merchant = super.nonNullGetByCode(outlet.getMerchantCode(), Merchant.class);

        Date now = new Date();

        CardLifeCycleBean cycleBean = new CardLifeCycleBean();
        cycleBean.setVoucherNumber(voucher.getVoucherCode());
        cycleBean.setBookletNumber(voucher.getBookletCode());
        cycleBean.setTimeZone(GvDateUtil.TIME_ZONE_DISPLAY_NAME);
        cycleBean.setCreationDate(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));
        cycleBean.setVoucherStatus(this.getVoucherStatusDesc(voucher, now));
        cycleBean.setDenomination(super.toAmount(voucher.getDenomination()));
        cycleBean.setExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS));
        cycleBean.setExpiryTimeZone(GvDateUtil.TIME_ZONE_DISPLAY_NAME);
        settingCustomerInfo(voucher, cycleBean);
        cycleBean.setVoucherProgramGroup(cpg.getCpgName());
        cycleBean.setBaseCurrencyOfTheCard(cpg.getCurrencyCode());
        cycleBean.setMerchantName(merchant.getMerchantName());
        cycleBean.setSellingDate(DateUtil.format(sellingDate, DateUtil.FORMAT_YYYYMMDDHHMISS));

        return Collections.singletonList(cycleBean);
    }

    private Outlet getVoucherOutlet(Voucher voucher, List<TransactionData> transactionDataList) {

        String outletCode = VoucherOwnerTypeEnum.CUSTOMER.equalsCode(voucher.getVoucherOwnerType()) ?
                transactionDataList.stream()
                        .filter(e -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(e.getTransactionType()))
                        .findFirst().orElse(DEFAULT_TRANSACTION_DATA).getOutletCode()
                : voucher.getVoucherOwnerCode();

        return super.nonNullGetByCode(outletCode, Outlet.class);
    }

    private void settingCustomerInfo(Voucher voucher, CardLifeCycleBean cycleBean) {

        TransactionData transactionData = transactionDataService.selectLastTransactionDataByVoucherAndType(voucher.getVoucherCode(), TransactionTypeEnum.GIFT_CARD_SELL);

        if (Objects.nonNull(transactionData)) {

            Customer customerSummary = super.getByCode(transactionData.getCustomerCode(), Customer.class);

            if (Objects.nonNull(customerSummary)) {

                cycleBean.autoFull(customerSummary, CardLifeCycleBean.class);

            } else {

                cycleBean.setCustomerFullName(transactionData.getCustomerFirstName() + " " + transactionData.getCustomerLastName());
                cycleBean.setCustomerMobile(transactionData.getMobile());
                cycleBean.setCustomerName(transactionData.getCorporateName());
            }

        }


    }

}
