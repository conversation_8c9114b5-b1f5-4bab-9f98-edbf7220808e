package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.dashboard.DataRecordRequest;
import com.gtech.gvcore.common.request.dashboard.HistogramRequest;
import com.gtech.gvcore.common.request.dashboard.PieChartRequest;
import com.gtech.gvcore.common.request.dashboard.RedemptionOtherSumRequest;
import com.gtech.gvcore.common.request.dashboard.RedemptionProportionRequest;
import com.gtech.gvcore.common.request.dashboard.SalesBarChartRequest;
import com.gtech.gvcore.common.request.dashboard.TopTenRequest;
import com.gtech.gvcore.common.request.dashboard.TotalRedemptionRequest;
import com.gtech.gvcore.common.request.dashboard.TotalSalesRequest;
import com.gtech.gvcore.common.request.dashboard.VcrInStockRequest;
import com.gtech.gvcore.common.response.dashboard.HistogramResponse;
import com.gtech.gvcore.common.response.dashboard.PieChartResponse;
import com.gtech.gvcore.common.response.dashboard.RedemptionOtherSumResponse;
import com.gtech.gvcore.common.response.dashboard.RedemptionProportionResponse;
import com.gtech.gvcore.common.response.dashboard.SalesBarChartResponse;
import com.gtech.gvcore.common.response.dashboard.TopTenResponse;
import com.gtech.gvcore.common.response.dashboard.TotalSalesResponse;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/8/29 13:46
 */
public interface DashboardService {


    void dataRecord(DataRecordRequest date);




    /**
     * 柱状图  每个月的数据----查询条件 年
     *
     *
     */
    List<HistogramResponse> histogram(HistogramRequest request);

    String lastUpdateTime();



    List<SalesBarChartResponse> salesBarChart(SalesBarChartRequest request);


    List<PieChartResponse> pieChart(PieChartRequest request);


    List<TopTenResponse> topTen(TopTenRequest request);


    List<TotalSalesResponse> totalSales(TotalSalesRequest request);


    BigDecimal totalRedemption(TotalRedemptionRequest request);


    List<RedemptionProportionResponse>redemptionProportion(RedemptionProportionRequest request);


    List<Map<String, String>> vcrInStock(VcrInStockRequest request);


    RedemptionOtherSumResponse redemptionOtherSum(RedemptionOtherSumRequest request);






}
