package com.gtech.gvcore.pool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Slf4j
@Component
public class ObjectPoolManager {
    
    private final Map<Class<?>, DynamicObjectPool<?>> pools = new ConcurrentHashMap<>();
    
    /**
     * 获取或创建对象池
     */
    @SuppressWarnings("unchecked")
    public <T> DynamicObjectPool<T> getPool(Class<T> clazz, Supplier<T> factory, Consumer<T> reset) {
        return (DynamicObjectPool<T>) pools.computeIfAbsent(clazz, 
            k -> createPool(factory, reset));
    }
    
    /**
     * 创建新的对象池
     */
    private <T> DynamicObjectPool<T> createPool(Supplier<T> factory, Consumer<T> reset) {
        return new DynamicObjectPool<>(factory, reset,
            50,     // 初始大小
            200,    // 最大大小
            20);    // 每次增长大小
    }
    
    /**
     * 关闭所有对象池
     */
    @PreDestroy
    public void shutdown() {
        pools.values().forEach(DynamicObjectPool::close);
        pools.clear();
    }
    
    /**
     * 获取对象池统计信息
     */
    public Map<String, PoolStats> getPoolStats() {
        Map<String, PoolStats> stats = new ConcurrentHashMap<>();
        pools.forEach((clazz, pool) -> 
            stats.put(clazz.getSimpleName(), pool.getStats()));
        return stats;
    }
} 