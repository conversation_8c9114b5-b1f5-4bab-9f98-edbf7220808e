package com.gtech.gvcore.helper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Calendar;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/27 16:11
 * @Description:用于生成文件名
 */
@Slf4j
@Component
public class FileHelper {

    /**
     * @param exportName
     * @param suffix     后缀
     * @return
     */
    public String generateOssFileName(String exportName, String suffix) {
        return exportName + "_" + Calendar.getInstance().getTimeInMillis() + suffix;
    }


    public void fileCleanup(String localFilePath) {
        if (StringUtils.isBlank(localFilePath)) {
            return;
        }
        File localFile = new File(localFilePath);
        if (localFile.exists()) {
            try {
                Files.delete(Paths.get(localFilePath));
            } catch (IOException e) {
                log.error("[File] delete local file cause an error.", e);
            }
        }
    }

    public String generateOssZipFileName(final String fileName, final String suffix) {
        return System.currentTimeMillis() + "/" + fileName + suffix;
    }
}
