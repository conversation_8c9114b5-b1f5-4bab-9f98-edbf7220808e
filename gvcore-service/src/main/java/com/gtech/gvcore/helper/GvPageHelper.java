package com.gtech.gvcore.helper;

import com.gtech.commons.page.PageParam;
import org.apache.ibatis.session.RowBounds;

/**
 * @ClassName GvPageHelper
 * @Description gv 分页插件
 * <AUTHOR>
 * @Date 2022/12/9 15:38
 * @Version V1.0
 **/
public class GvPageHelper {

    private GvPageHelper() {
    }

    /**
     * 查询存在
     */
    public static final RowBounds LIMIT_1 = new RowBounds(0, 1);

    /**
     * 根据分页参数返回分页对象
     */
    public static RowBounds getRowBounds (int pageSize, int pageNumber) {

        if (pageSize < 1) pageSize = PageParam.DEFAULT_PAGE_SIZE;
        if (pageNumber < 1) pageNumber = PageParam.DEFAULT_PAGE_NUM;

        int offset = (pageNumber - 1) * pageSize;
        int limit = pageSize;

        return new RowBounds(offset, limit);
    }

    /**
     * 根据分页参数获得分页对象
     */
    public static RowBounds getRowBounds (PageParam pageParam) {

        return getRowBounds(pageParam.getPageSize(), pageParam.getPageNum());
    }



}
