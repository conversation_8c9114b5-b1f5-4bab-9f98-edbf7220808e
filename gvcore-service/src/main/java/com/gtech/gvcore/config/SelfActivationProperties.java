package com.gtech.gvcore.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Configuration properties for self-activation functionality
 * Reads delayed-channels configuration from application.yml
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.activation")
public class SelfActivationProperties {
    
    /**
     * List of channels that require delayed activation
     * Example: MAPCLUB_COM, MAPGV_COM
     */
    private List<String> delayedChannels;
}
