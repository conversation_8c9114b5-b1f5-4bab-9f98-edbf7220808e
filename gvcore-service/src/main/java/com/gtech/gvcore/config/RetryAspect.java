package com.gtech.gvcore.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gtech.gvcore.common.annotation.RetryableApi;
import com.gtech.gvcore.common.request.base.BaseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
@Order(1) // Ensure this aspect runs before LoggerAspect
public class RetryAspect {

    private static final String RETRY_KEY_PREFIX = "GV:RETRY_KEY:";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Around("@annotation(com.gtech.gvcore.common.annotation.RetryableApi)")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = signature.getMethod();
        RetryableApi retryableApi = method.getAnnotation(RetryableApi.class);

        if (!retryableApi.enable()) {
            return proceedingJoinPoint.proceed();
        }

        Object[] args = proceedingJoinPoint.getArgs();
        BaseRequest requestBody = null;
        for (Object arg : args) {
            if (arg instanceof BaseRequest) {
                requestBody = (BaseRequest) arg;
                break;
            }
        }

        if (requestBody == null || StringUtils.isBlank(requestBody.getRetryKey())) {
            return proceedingJoinPoint.proceed();
        }

        String retryKey = requestBody.getRetryKey();
        String cacheKey = RETRY_KEY_PREFIX + retryKey;

        // Check cache
        String cachedResponse = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(cachedResponse)) {
            log.info("Retry key hit. Key: {}. Returning cached response.", retryKey);
            //httpstatus
            JSONObject parse = JSON.parseObject(cachedResponse);
            Integer statusCodeValue = (Integer) parse.get("statusCodeValue");
            Object responseBody = parse.get("body");
            HttpStatus httpStatus = HttpStatus.valueOf(statusCodeValue);

            // Assuming the cached response is a JSON representation of ResponseEntity
            return new ResponseEntity<>(responseBody, httpStatus);
        }

        // Proceed with method execution
        Object result = proceedingJoinPoint.proceed();

        ObjectMapper objectMapper = new ObjectMapper();

        // Cache the response
        if (result instanceof ResponseEntity) {
            redisTemplate.opsForValue().set(cacheKey, objectMapper.writeValueAsString(result), retryableApi.expireSeconds(), TimeUnit.SECONDS);
            log.info("Response cached for retry key: {}", retryKey);
        }

        return result;
    }
} 