package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @ClassName CancelRedeemSummaryDto
 * @Description CancelRedeemSummaryDto
 * <AUTHOR>
 * @Date 2022/7/7 16:07
 * @Version V1.0
 **/
@Getter
@Setter
public class CancelRedeemSummaryDto {

    /**
     * Merchant Code
     */
    private String merchantCode;

    /**
     * Outlet Code
     */
    private String outletCode;

    /**
     * Cpg Code / VPG Code
     */
    private String cpgCode;

    /**
     * 券数量
     */
    private Integer numberOfVouchers;

    /**
     * 该CPG券的面额
     */
    private BigDecimal denomination;

}
