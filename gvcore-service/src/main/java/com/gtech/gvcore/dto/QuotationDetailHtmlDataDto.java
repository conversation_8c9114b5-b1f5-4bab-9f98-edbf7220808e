package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @ClassName QuotationDetailHtmlDataDto
 * @Description QuotationDetailHtmlDataDto
 * <AUTHOR>
 * @Date 2022/7/25 14:08
 * @Version V1.0
 **/
@Getter
@Setter
public class QuotationDetailHtmlDataDto {

    private Integer line;

    private String articleCodeName;

    private String cpgName;

    private String voucherNum;

    private String denomination;

    private String amount;

    /**
     * amount的数值表示
     */
    private BigDecimal amountNumerical;
}
