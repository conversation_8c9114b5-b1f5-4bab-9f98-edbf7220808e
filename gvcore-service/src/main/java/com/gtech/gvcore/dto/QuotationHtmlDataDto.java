package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName QuotationHtmlDataDto
 * @Description 订单Quotation的html数据
 * <AUTHOR>
 * @Date 2022/7/25 13:55
 * @Version V1.0
 **/
@Getter
@Setter
public class QuotationHtmlDataDto {

    /**
     * 订单号
     */
    private String purchaseOrderNo;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 总金额
     */
    private String totalAmount;

    /**
     * 折扣百分比
     */
    private String discount;

    /**
     * 折扣金额
     */
    private String discountAmount;

    /**
     * 累计金额
     */
    private String grandTotal;

    /**
     * 详情
     */
    private List<QuotationDetailHtmlDataDto> detailList;

    /**
     * 付款条件
     */
    private String termsOfPayment;

}
