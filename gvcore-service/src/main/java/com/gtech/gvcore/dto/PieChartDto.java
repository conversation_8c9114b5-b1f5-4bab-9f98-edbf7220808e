package com.gtech.gvcore.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/8/31 14:00
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PieChartDto {


    private String outletCode;

    private String outletName;
    private String outletType;

    private Integer voucherCount;

    private BigDecimal discount;

    private BigDecimal amount;




}
