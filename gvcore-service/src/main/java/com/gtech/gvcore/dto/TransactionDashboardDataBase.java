package com.gtech.gvcore.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/8/29 15:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDashboardDataBase {


    private String voucherCode;

    private String outletCode;

    private BigDecimal denomination;

    private String outletType;

    private String issuerCode;

    private String transactionId;

    private String mopCode;

}
