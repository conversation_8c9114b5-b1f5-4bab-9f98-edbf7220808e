package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName InvoiceHtmlDataDto
 * @Description InvoiceHtmlDataDto
 * <AUTHOR>
 * @Date 2022/7/25 14:56
 * @Version V1.0
 **/
@Getter
@Setter
public class InvoiceHtmlDataDto {

    /**
     * 订单号
     */
    private String purchaseOrderNo;

    /**
     * 生成时间
     */
    private String generationTime;

    private String contactFirstName;

    private String customerName;

    private String shippingAddress;

    /**
     * 总金额
     */
    private String totalAmount;

    /**
     * 折扣百分比
     */
    private String discount;

    /**
     * 折扣金额
     */
    private String discountAmount;

    /**
     * 累计金额
     */
    private String grandTotal;

    private List<InvoiceDetailHtmlDataDto> detailList;

    /**
     * 付款条件
     */
    private String termsOfPayment;


}
