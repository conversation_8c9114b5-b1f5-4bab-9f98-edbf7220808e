package com.gtech.gvcore.components;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/3/13 10:24
 */
@Component
@Slf4j
public class SnowflakeRedis {


    private RedisTemplate redisTemplate;

    public SnowflakeRedis(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    private static final String SNOWFLAKE_KEY = "snowflake:%s";
    private static final long REDIS_TIMEOUT = 5000L;

    private final String prefix = "voucher";
    private final long datacenterId = 1L;
    private static final long workerId;

    private long sequence = 0L;
    private long lastTimestamp = -1L;

    static long MAX_WORKER_ID = 31L;

    // 默认调用的方法
    static{
        InetAddress addr = null;
        try {
            addr = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            log.error("获取本机IP异常", e);
            throw new RuntimeException(e);
        }
        String ip = addr.getHostAddress();
        int hash = Math.abs(ip.hashCode());
        workerId = hash % MAX_WORKER_ID;
    }



    public synchronized List<Long> nextIds(String batchId, long start, long end) {
        List<Long> ids = new ArrayList<>();
        long timestamp = timeGen();

        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & 0xFFF;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        if (timestamp < lastTimestamp) {
            throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }

        lastTimestamp = timestamp;

        for (long i = start; i <= end; i++) {
            long id = ((timestamp - twepoch) << timestampLeftShift)
                    | (datacenterId << datacenterIdShift)
                    | (workerId << workerIdShift)
                    | sequence;
            ids.add(id);
            sequence = (sequence + 1) & 0xFFF;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
                lastTimestamp = timestamp;
            }
        }

        String redisKey = String.format(SNOWFLAKE_KEY, prefix + batchId);
        redisTemplate.opsForList().rightPushAll(redisKey, ids);
        redisTemplate.expire(redisKey, REDIS_TIMEOUT, TimeUnit.MILLISECONDS);

        return ids;
    }

    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    private long timeGen() {
        return System.currentTimeMillis();
    }

    private static final long twepoch = 1622476800000L;
    private static final long workerIdBits = 5L;
    private static final long datacenterIdBits = 5L;
    private static final long maxWorkerId = -1L ^ (-1L << workerIdBits);
    private static final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);
    private static final long sequenceBits = 12L;
    private static final long workerIdShift = sequenceBits;
    private static final long datacenterIdShift = sequenceBits + workerIdBits;
    private static final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
}

