package com.gtech.gvcore.components.atomic.request;

import com.gtech.gvcore.common.request.voucher.VerifyVoucherInfo;
import com.gtech.gvcore.common.response.voucher.VoucherResponse;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AtomicRequest {

    AtomicCustomerOrderRequest customerOrderRequest;
    AtomicTransactionDataRequest transactionDataRequest;
    AtomicVoucherStatusRequest voucherStatusRequest;


    //TransactionApi param
    Map<String,List<VoucherResponse>> voucherList;

}
