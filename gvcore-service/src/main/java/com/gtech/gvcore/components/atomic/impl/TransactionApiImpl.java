package com.gtech.gvcore.components.atomic.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VerifyVoucherTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.transaction.LineItem;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.request.voucher.VerifyVoucherInfo;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.common.response.voucher.VoucherResponse;
import com.gtech.gvcore.components.atomic.VoucherStatusStrategy;
import com.gtech.gvcore.components.atomic.request.AtomicRequest;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.VoucherService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TransactionApiImpl implements VoucherStatusStrategy {


    /**
     * pos api 券相关实现类
     *
     */



    //1.修改券状态

    //2.添加交易记录

    //3.创建订单或者合并订单


    @Autowired
    private VoucherService voucherService;


    @Override
    public int execute(AtomicRequest request) {

        //TODO 获取api结果
        Map<String, List<VoucherResponse>> voucherList = request.getVoucherList();

        //TODO 修改券状态

        voucherList.forEach((k,v)->{
            action(k,v);
        });




        //TODO 添加交易记录

        //TODO 创建订单或者合并订单

        return 0;
    }


    private void action(String actionType,List<VoucherResponse> vouchers){

        if (actionType.equals(VerifyVoucherTypeEnum.VOUCHER_ACTIVATION.getCode())) {
            activationVoucher(vouchers);
        } else if (actionType.equals(VerifyVoucherTypeEnum.VOUCHER_REDEMPTION.getCode())) {
            redemptionVoucher(vouchers);
        } else if (actionType.equals(VerifyVoucherTypeEnum.VOUCHER_VERIFY_ACTIVATION.getCode())) {
            verifyActivationVoucher(vouchers);
        } else if (actionType.equals(VerifyVoucherTypeEnum.VOUCHER_VERIFY_REDEMPTION.getCode())) {
            verifyRedemptionVoucher(vouchers);
        }

    }


    private void activationVoucher(List<VoucherResponse> vouchers){
        List<String> voucherCodes = vouchers.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList());
        UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();
        request.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
        request.setVoucherCodeList(voucherCodes);
        voucherService.updateVoucherStatus(request);
    }

    private void redemptionVoucher(List<VoucherResponse> vouchers){
        List<String> voucherCodes = vouchers.stream().map(x -> x.getVoucherCode()).collect(Collectors.toList());
        UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();
        request.setStatus(VoucherStatusEnum.VOUCHER_USED.getCode());
        request.setVoucherCodeList(voucherCodes);
        voucherService.updateVoucherStatus(request);
    }

    private void verifyActivationVoucher(List<VoucherResponse> vouchers){

    }

    private void verifyRedemptionVoucher(List<VoucherResponse> vouchers){

    }


}
