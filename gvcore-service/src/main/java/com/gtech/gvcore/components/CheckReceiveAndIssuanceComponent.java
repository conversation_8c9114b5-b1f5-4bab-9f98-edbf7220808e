package com.gtech.gvcore.components;

import com.google.common.collect.Lists;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.request.customerorder.GetCustomerOrderRequest;
import com.gtech.gvcore.common.request.voucher.GetStartAndEndVoucherRequest;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderResponse;
import com.gtech.gvcore.common.response.voucher.GetStartAndEndVoucherResponse;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveMapper;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dao.model.VoucherReceiveBatch;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.VoucherReceiveBatchService;
import com.gtech.gvcore.service.VoucherReceiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/1 14:41
 */
@Component
public class CheckReceiveAndIssuanceComponent {


    @Autowired
    private VoucherReceiveService receiveService;

    @Autowired
    private VoucherReceiveMapper receiveMapper;
    @Autowired
    private VoucherReceiveBatchMapper voucherReceiveBatchMapper;

    @Autowired
    private VoucherReceiveBatchService voucherReceiveBatchService;

    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private CustomerOrderService customerOrderService;


    public static final String ALL_CORRECT_COUPONS = "All vouchers can be received.";
    public static final String EXISTENCE_IS_NOT_WITHIN_THE_SCOPE_OF_THE_RECEIPT = "Error: {START_TO_END}  do not belong to the scope of receipt.";
    public static final String THERE_ARE_ALREADY_SIGNED_COUPONS = "Error: {START_TO_END} have been received.";


    /*当券数量超过可发行数量：This issuance failed because the voucher quantity of the assignment was higher than the voucher quantity of the order.
            当券数量低于可发行数量：This issuance failed because the voucher quantity of the assignment was lower than the voucher quantity of the order.
            当券的VPG不正确时：This issuance failed because the voucher group program of the vouchers of the assignment was incorrect.*/

    public static final String CPG_ERROR = "This issuance failed because the voucher group program of the vouchers of the assignment was incorrect.";
    public static final String CPG_MORE_THAN_THE = "This issuance failed because the voucher quantity of the assignment was higher than the voucher quantity of the order.";
    public static final String CPG_LESS_THAN = "This issuance failed because the voucher quantity of the assignment was lower than the voucher quantity of the order.";


    /**
     * 全部正确
     *  1.符合订单待签收数据
     *  2.提交券的信息正确 （所属人订单的outlet，状态是待签收-2，不能被禁用，券是新建状态）
     * 存在不在签收券范围内的：
     *  1.除了状态是待签收
     * 存在已签收券的：
     *  1.状态已经签收
     * 先判断是否符合订单
     * 不符合就是不在签收券范围
     * 判断是否已签收
     * 不符合就是已签收
     * 其他失败参照以前错误逻辑
     *
     */
    public List<GetStartAndEndVoucherResponse> checkIfReceive(List<GetStartAndEndVoucherResponse> responses, GetStartAndEndVoucherRequest request){

        //1.判断是否符合订单 是否是订单的起始券和结束券内
        String receiveCode = request.getVoucherReceiveCode();
        VoucherReceive voucherReceive = receiveMapper.selectOne(VoucherReceive.builder().voucherReceiveCode(receiveCode).build());
        //根据voucherReceive的voucherReceiveCode查询voucherReceiveBatch
        Example voucherReceiveCode = new Example(VoucherReceiveBatch.class);
        voucherReceiveCode.createCriteria()
                .andEqualTo("voucherReceiveCode", receiveCode);
        List<VoucherReceiveBatch> voucherReceiveBatches = voucherReceiveBatchMapper.selectByCondition(voucherReceiveCode);

        //遍历voucherReceiveBatches和responses 判断是否符合订单,如果都不符合就返回错误信息

        for (VoucherReceiveBatch voucherReceiveBatch : voucherReceiveBatches) {
            for (GetStartAndEndVoucherResponse respons : responses) {
                if (Long.parseLong(respons.getVoucherStartNo()) >= Long.parseLong(voucherReceiveBatch.getVoucherStartNo())
                        && Long.parseLong(respons.getVoucherEndNo()) <= Long.parseLong(voucherReceiveBatch.getVoucherEndNo())) {
                    //符合订单
                    respons.setResponseMessage(ALL_CORRECT_COUPONS);
                }
            }
        }
        responses.stream()
                .filter(response -> !(ALL_CORRECT_COUPONS).equals(response.getResponseMessage()))
                .forEach(response -> {
                    response.setResponseMessage(EXISTENCE_IS_NOT_WITHIN_THE_SCOPE_OF_THE_RECEIPT.replace("{START_TO_END}", response.getVoucherStartNo() + "-" + response.getVoucherEndNo()));
                });



        //2.判断是否已签收
        responses.stream().filter(response -> response.getResponseMessage().equals(ALL_CORRECT_COUPONS)).forEach(response -> {

            //根据起始值和结束值查询券
            Example example = new Example(Voucher.class);
            example.createCriteria()
                    .andBetween("voucherCode", response.getVoucherStartNo(), response.getVoucherEndNo())
                    .andNotEqualTo("circulationStatus", 2);
            List<Voucher> vouchers = voucherMapper.selectByCondition(example);

            if (vouchers.size() > 0) {
                List<String> segment = segmentStrings(vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()));
                StringBuilder strings = new StringBuilder();
                for (String string : segment) {
                    strings.append(string).append(",");
                }
                response.setResponseMessage(THERE_ARE_ALREADY_SIGNED_COUPONS.replace("{START_TO_END}",strings));
            }
        });
        return responses;

    }



    public static List<String> segmentStrings(List<String> list) {
        //排序
        Collections.sort(list);

        List<String> segmentedStrings = new ArrayList<String>();
        int start = 0;
        int end = 0;
        for (int i = 1; i < list.size(); i++) {
            if (Long.parseLong(list.get(i)) == Long.parseLong(list.get(i - 1)) + 1) {
                end = i;
            } else {
                segmentedStrings.add("{"+list.get(start) + " - " + list.get(end)+"} ");
                start = i;
                end = i;
            }
        }
        segmentedStrings.add("{"+list.get(start) + " - " + list.get(end)+"} ");
        return segmentedStrings;
    }




    public List<GetStartAndEndVoucherResponse> checkIfIssuance(List<GetStartAndEndVoucherResponse> responses, GetStartAndEndVoucherRequest request) {

        /**
         * 当券数量超过可发行数量：This issuance failed because the voucher quantity of the assignment was higher than the voucher quantity of the order.
         * 当券数量低于可发行数量：This issuance failed because the voucher quantity of the assignment was lower than the voucher quantity of the order.
         * 当券的VPG不正确时：This issuance failed because the voucher group program of the vouchers of the assignment was incorrect.
         */


        //1.查询订单找到所有需要的cpg
        Result<GetCustomerOrderResponse> customerOrder =
                customerOrderService.getCustomerOrder(GetCustomerOrderRequest.builder().customerOrderCode(request.getCustomerOrderCode()).build());
        List<GetCustomerOrderDetailsResponse> getCustomerOrderDetailsResponses = customerOrder.getData().getGetCustomerOrderDetailsResponses();
        Map<String, GetCustomerOrderDetailsResponse> cpgMap = getCustomerOrderDetailsResponses.stream().collect(Collectors.toMap(GetCustomerOrderDetailsResponse::getCpgCode, v -> v));

        //根据response的起始和结束,查询所有券号的cpg

        ArrayList<Voucher> voucherList = new ArrayList<>();

        for (GetStartAndEndVoucherResponse respons : responses) {

            List<Voucher> vouchers = new ArrayList<>();
            if (StringUtil.isNotBlank(respons.getVoucherStartNo()) && StringUtil.isNotBlank( respons.getVoucherEndNo())){
                Example vocuherExample = new Example(Voucher.class);
                vocuherExample.createCriteria()
                        .andBetween("voucherCode", respons.getVoucherStartNo(), respons.getVoucherEndNo());
                vouchers = voucherMapper.selectByCondition(vocuherExample);
            }

            Map<String, List<Voucher>> cpgVoucherMap = vouchers.stream().collect(Collectors.groupingBy(Voucher::getCpgCode));
            //2.判断cpg是否在订单中
            for (Map.Entry<String, List<Voucher>> entry : cpgVoucherMap.entrySet()) {
                if (!cpgMap.containsKey(entry.getKey())) {
                    //不在订单中
                    respons.setResponseMessage(CPG_ERROR);
                } else if (cpgMap.containsKey(entry.getKey()) && entry.getValue().size() > cpgMap.get(entry.getKey()).getVoucherNum()) {
                    //多余
                    respons.setResponseMessage(CPG_MORE_THAN_THE);
                } else if (cpgMap.containsKey(entry.getKey()) && entry.getValue().size() < cpgMap.get(entry.getKey()).getVoucherNum()) {
                    //少于
                    respons.setResponseMessage(CPG_LESS_THAN);
                }
            }

        }
        return responses;
    }

}
