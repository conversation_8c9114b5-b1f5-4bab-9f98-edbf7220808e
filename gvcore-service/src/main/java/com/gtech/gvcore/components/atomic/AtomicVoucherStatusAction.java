package com.gtech.gvcore.components.atomic;

import com.gtech.gvcore.components.atomic.impl.CustomerOrderImpl;
import com.gtech.gvcore.components.atomic.impl.TransactionDataImpl;
import com.gtech.gvcore.components.atomic.impl.VoucherStatusImpl;
import com.gtech.gvcore.components.atomic.request.AtomicCustomerOrderRequest;
import com.gtech.gvcore.components.atomic.request.AtomicRequest;
import com.gtech.gvcore.components.atomic.request.AtomicTransactionDataRequest;
import com.gtech.gvcore.components.atomic.request.AtomicVoucherStatusRequest;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
public class AtomicVoucherStatusAction {


    static final List<VoucherStatusStrategy> sales = new ArrayList<>();
    static final List<VoucherStatusStrategy> activity = new ArrayList<>();
    static final List<VoucherStatusStrategy> redeem = new ArrayList<>();

    public static final String SALES = "SALES";
    public static final String ACTIVITY = "ACTIVITY";
    public static final String REDEEM = "REDEEM";


    static {

        sales.add(new VoucherStatusImpl());
        sales.add(new TransactionDataImpl());
        sales.add(new CustomerOrderImpl());

        activity.add(new VoucherStatusImpl());
        activity.add(new TransactionDataImpl());


        redeem.add(new VoucherStatusImpl());
        redeem.add(new TransactionDataImpl());

    }



    @Transactional
    public void action(AtomicVoucherStatusRequest voucherStatus,
                       AtomicTransactionDataRequest transactionData,
                       AtomicCustomerOrderRequest customerOrder,
                       String actionType
                       ){

        AtomicRequest atomicRequest = new AtomicRequest();
        switch (actionType) {
            case SALES:
                atomicRequest.setVoucherStatusRequest(voucherStatus);
                atomicRequest.setTransactionDataRequest(transactionData);
                atomicRequest.setCustomerOrderRequest(customerOrder);
                sales.forEach(x->x.execute(atomicRequest));
                break;
            case ACTIVITY:
                atomicRequest.setVoucherStatusRequest(voucherStatus);
                atomicRequest.setTransactionDataRequest(transactionData);
                activity.forEach(x->x.execute(atomicRequest));
                break;
            case REDEEM:
                atomicRequest.setVoucherStatusRequest(voucherStatus);
                atomicRequest.setTransactionDataRequest(transactionData);
                redeem.forEach(x->x.execute(atomicRequest));
                break;
            default:
        }

        List<VoucherStatusStrategy> build = new AtomicBuilder().builder()
                .addCustomerOrderAction(atomicRequest)
                .addVoucherStatusAction(atomicRequest)
                .addVoucherStatusAction(atomicRequest)
                .addTransactionDataAction(atomicRequest)
                .build();


    }



    @Transactional
    public void customizeAction(List<VoucherStatusStrategy> actionList){
        actionList.forEach(x->x.execute(null));
    }
















}
