package com.gtech.gvcore.components.atomic.impl;

import com.ctc.wstx.util.StringUtil;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.gvcore.components.atomic.VoucherStatusStrategy;
import com.gtech.gvcore.components.atomic.request.AtomicRequest;
import com.gtech.gvcore.components.atomic.request.AtomicVoucherStatusRequest;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.Voucher;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

@Component
public class VoucherStatusImpl implements VoucherStatusStrategy {


    @Autowired
    private VoucherMapper voucherMapper;


    private AtomicRequest request;


    public VoucherStatusImpl() {
    }

    public VoucherStatusImpl(AtomicRequest request) {
        this.request = request;
    }

    @Override
    public int execute(AtomicRequest request) {
        if (null == request){
            request = this.request;
        }

        if (null == request.getVoucherStatusRequest()){
            throw new GTechBaseException();
        }

        AtomicVoucherStatusRequest voucherStatusRequest = request.getVoucherStatusRequest();


        Weekend<Voucher> voucherWeekend = Weekend.of(Voucher.class);
        WeekendCriteria<Voucher, Object> weekendCriteria = voucherWeekend.weekendCriteria();

        weekendCriteria.andIn(Voucher::getVoucherCode,voucherStatusRequest.getVoucherCodeList());

        if (null != voucherStatusRequest.getOldStatus())
            weekendCriteria.andEqualTo(Voucher::getStatus,voucherStatusRequest.getOldStatus());

        if (StringUtils.isNotBlank(voucherStatusRequest.getOldOwnerCode()))
            weekendCriteria.andEqualTo(Voucher::getVoucherOwnerCode,voucherStatusRequest.getOldOwnerCode());

        Voucher voucher = new Voucher();
        voucher.setStatus(voucherStatusRequest.getNewStatus());

        if (StringUtils.isNotBlank(voucherStatusRequest.getNewOwnerCode()))
            voucher.setVoucherOwnerCode(voucherStatusRequest.getNewOwnerCode());

        if (StringUtils.isNotBlank(voucherStatusRequest.getNewOwnerType()))
            voucher.setVoucherOwnerCode(voucherStatusRequest.getNewOwnerType());

        return voucherMapper.updateByConditionSelective(voucher,voucherWeekend);
    }



}
