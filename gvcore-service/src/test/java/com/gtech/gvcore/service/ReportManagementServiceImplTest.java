package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.reportmanagement.CreateReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.DelReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.QueryReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.UpdateReportManagementRequest;
import com.gtech.gvcore.common.response.reportmanagement.QueryReportManagementResponse;
import com.gtech.gvcore.dao.mapper.ReportManagementMapper;
import com.gtech.gvcore.dao.model.ReportManagement;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.ReportManagementServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class ReportManagementServiceImplTest {

    @InjectMocks
    private ReportManagementServiceImpl reportManagementService;

    @Mock
    private ReportManagementMapper reportManagementMapper;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(ReportManagement.class, new MapperHelper().getConfig());
    }

    @Test
    public void updatePermissions() {
        Mockito.when(reportManagementMapper.selectByCode(Mockito.any())).thenReturn(new ReportManagement());
        Assert.assertNotNull(reportManagementService.updatePermissions(UpdateReportManagementRequest.builder().reportManagementCode("RM102203081916000004").build()));
    }

    @Test
    public void testUpdatePermissions() {
        Mockito.when(reportManagementMapper.selectByCode(Mockito.any())).thenReturn(null);
        Assert.assertNotNull(reportManagementService.updatePermissions(UpdateReportManagementRequest.builder().reportManagementCode("RM102203081916000004").build()));
    }

    @Test
    public void delReportManagement() {
        Assert.assertNotNull(reportManagementService.delReportManagement(new DelReportManagementRequest()));
    }


    @Test
    public void queryReportManagements() {
        Mockito.when(reportManagementMapper.queryReportManagements(Mockito.any())).thenReturn(new ArrayList<>(Arrays.asList(new QueryReportManagementResponse(), new QueryReportManagementResponse())));
        Assert.assertNotNull(reportManagementService.queryReportManagements(new QueryReportManagementRequest()));
    }

    @Test
    public void createReportManagements() {
        Mockito.when(gvCodeHelper.generateReportManagementCode()).thenReturn("RM102203081916000004");
        Assert.assertNotNull(reportManagementService.createReportManagements(new ArrayList<>(Arrays.asList(new CreateReportManagementRequest(), new CreateReportManagementRequest()))));
    }
}