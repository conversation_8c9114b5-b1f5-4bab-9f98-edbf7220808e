package com.gtech.gvcore.service;

import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.web.dao.MasterDataDistrictMapper;
import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.gvcore.common.request.outlet.*;
import com.gtech.gvcore.common.response.outlet.OutletCpgVo;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.OutletServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class OutletServiceTest {

    @InjectMocks
    private OutletServiceImpl outletService;

    @Mock
    private OutletMapper outletMapper;

    @Mock
    private OutletCpgService outletCpgService;

    @Mock
    private OutletProductCategoryService outletProductCategoryService;

    @Mock
    private GvCodeHelper codeHelper;

    @Mock
    private MasterDataDistrictMapper masterDataDistrictMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Outlet.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(MasterDataDistrictEntity.class, new MapperHelper().getConfig());

    }


    @Test
    public void createOutlet() {
        CreateOutletRequest request = new CreateOutletRequest();
        request.setOutletName("name");
        request.setCreateUser("user");
        request.setCpgCode(Lists.newArrayList("123123"));
        request.setCreateUser("123");
        outletService.createOutlet(request);
    }

    @Test
    public void updateOutlet() {
        UpdateOutletRequest request = new UpdateOutletRequest();
        request.setOutletCode("code");
        request.setOutletName("123");
        request.setStatus(0);
        request.setUpdateUser("123");
        request.setCpgCode(Lists.newArrayList("12312"));
        outletService.updateOutlet(request);
    }


    @Test
    public void updateOutletStatus() {
        UpdateOutletStatusRequest request = new UpdateOutletStatusRequest();
        request.setOutletCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");

        outletService.updateOutletStatus(request);
    }


    @Test
    public void deleteOutlet() {
        DeleteOutletRequest request = new DeleteOutletRequest();
        request.setOutletCode("123");

        outletService.deleteOutlet(request);
    }


    @Test
    public void queryOutletList() {

        QueryOutletRequest request = new QueryOutletRequest();
        ArrayList<Outlet> objects = new ArrayList<>();
        List<Outlet> gvOutletEntities = new ArrayList<>();
        Outlet Outlet = new Outlet();
        Outlet.setId(0L);
        Outlet.setOutletCode("123");
        Outlet.setOutletName("123");
        Outlet.setStatus(0);
        Outlet.setCreateUser("123");
        Outlet.setCreateTime(new Date());
        Outlet.setUpdateUser("123");
        Outlet.setUpdateTime(new Date());
        gvOutletEntities.add(Outlet);


        request.setOutletType("Xxxx");
        List<OutletResponse> outlet = new ArrayList<>();
        outlet.add(OutletResponse.builder().outletCode("123").cpg(Lists.newArrayList(OutletCpgVo.builder().cpgCode("123").build())).build());

        Mockito.when(outletMapper.queryOutletList(Mockito.any())).thenReturn(outlet);


        List<OutletCpgResponse> response = new ArrayList<>();

        response.add(OutletCpgResponse.builder().cpgCode("123").build());


        Mockito.when(outletCpgService.queryOutletCpgListByOutlet(Mockito.any())).thenReturn(response);


        outletService.queryOutletList(request);

    }


    @Test
    public void getOutlet() {

        GetOutletRequest request = new GetOutletRequest();
        request.setOutletCode("123");

        OutletResponse outletResponse = new OutletResponse();
        outletResponse.setOutletCode("1");
        outletResponse.setOutletName("1");
        outletResponse.setMerchantCode("1");
        outletResponse.setIssuerCode("");
        outletResponse.setMerchantName("");
        outletResponse.setBusinessOutletCode("");
        outletResponse.setOutletType("1");
        outletResponse.setStateCode("1");
        outletResponse.setCityCode("1");
        outletResponse.setStateName("1");
        outletResponse.setCityName("1");
        outletResponse.setDistrictCode("1");
        outletResponse.setDistrictName("");
        outletResponse.setAddress1("");
        outletResponse.setAddress2("");
        outletResponse.setPinCode("");
        outletResponse.setFirstName("");
        outletResponse.setLastName("");
        outletResponse.setEmail("");
        outletResponse.setPhone("");
        outletResponse.setMobile("");
        outletResponse.setAlertnateEmail("");
        outletResponse.setAlertnatePhone("");
        outletResponse.setDescriptive("");
        outletResponse.setStatus(0);
        outletResponse.setCpg(Lists.newArrayList());
        outletResponse.setCreateUser("");
        outletResponse.setCreateTime(new Date());
        outletResponse.setUpdateUser("");
        outletResponse.setUpdateTime(new Date());


        Mockito.when( outletMapper.getOutlet(Mockito.any())).thenReturn(outletResponse);
        outletService.getOutlet(request);

    }

    @Test
    public void queryOutletByMerchantCodes() {
        QueryOutletByMerchantCodesRequest queryCompanyByIssuerCodesRequest = new QueryOutletByMerchantCodesRequest();
        queryCompanyByIssuerCodesRequest.setMerchantCodes(Lists.newArrayList("123123"));
        outletService.queryOutletByMerchantCodes(queryCompanyByIssuerCodesRequest);
    }

    @Test
    public void queryOutletByCodes() {
        outletService.queryOutletByCodes(Arrays.asList("123"));
    }

    @Test
    public void queryOutletByBusinessType() {
        QueryOutletByBusinessRequest queryOutletByBusinessRequest = new QueryOutletByBusinessRequest();
        queryOutletByBusinessRequest.setBusinessType("request");
        queryOutletByBusinessRequest.setIssuerCode("IS102204161424001275");
        queryOutletByBusinessRequest.setFromOrTo("from");
        Assert.assertNotNull(outletService.queryOutletByBusinessType(queryOutletByBusinessRequest));
    }
}
