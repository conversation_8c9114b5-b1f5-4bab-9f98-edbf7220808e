package com.gtech.gvcore.service;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.request.customerorder.ApproveCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.CancelReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.DelPaymentVoucherRequest;
import com.gtech.gvcore.common.request.customerorder.DeliverCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.NonSystemCreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.QueryCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.ReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.ReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.SendCustomerOrderEmailRequest;
import com.gtech.gvcore.common.request.customerorder.SubmitCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UploadPaymentVoucherRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.response.customerorder.QueryCustomerOrderResponse;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.ArticleMopMapper;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderEmailMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderReceiverMapper;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.CustomerOrderReceiver;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.helper.CustomerOrderPdfHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.GvHtmlHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.impl.CustomerOrderServiceImpl;
import com.gtech.gvcore.service.impl.MessageComponent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CustomerOrderServiceImplTest {

    @InjectMocks
    private CustomerOrderServiceImpl customerOrderServiceImpl;
    @Mock
    private GvCodeHelper codeHelper;
    @Mock
    private CustomerOrderMapper customerOrderMapper;
    @Mock
    private CustomerOrderDetailsMapper customerOrderDetailsMapper;
    @Mock
    private CustomerOrderReceiverMapper customerOrderReceiverMapper;
    @Mock
    private ReleaseApproveService releaseApproveService;
    @Mock
    private ApproveNodeRecordMapper approveNodeRecordMapper;
    @Mock
    private VoucherReceiveService voucherReceiveService;
    @Mock
    private VoucherAllocationService voucherAllocationService;
    @Mock
    private GvOperateLogService operateLogService;
    @Mock
    private VoucherBatchService voucherBatchService;

    @Mock
    private MessageComponent messageComponent;

    @Mock
    private VoucherService voucherService;

    @Mock
    private GvHtmlHelper gvHtmlHelper;

    @Mock
    private CustomerOrderPdfHelper customerOrderPdfHelper;


    @Mock
    private GvUserAccountService userAccountService;

    @Mock
    private FlowNoticeService flowNoticeService;

    @Mock
    private OssHelper ossHelper;

    @Mock
    private ArticleMopMapper articleMopMapper;

    @Mock
    private CpgMapper cpgMapper;

    @Mock
    private OutletMapper outletMapper;

    @Mock
    private VoucherMapper voucherMapper;

    @Mock
    private CustomerOrderService customerOrderService;

    @Mock
    private VoucherAllocationBatchService voucherAllocationBatchService;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Mock
    private PermissionHelper permissionHelper;

    @Mock
    private CustomerOrderEmailMapper customerOrderEmailMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(CustomerOrder.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(CustomerOrderDetails.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(ApproveNodeRecord.class, new MapperHelper().getConfig());
    }

    @Test
    public void createCustomerOrder() {

        CreateCustomerOrderRequest createCustomerOrderRequest = new CreateCustomerOrderRequest();
        createCustomerOrderRequest.setVoucherAmount(BigDecimal.TEN);
        createCustomerOrderRequest.setDiscount(new BigDecimal(2));
        createCustomerOrderRequest.setOutletCode("xxx");
        createCustomerOrderRequest.setVoucherNum(1);

        List<CreateCustomerOrderDetailsRequest> createCustomerOrderDetailsRequests = new ArrayList<>();
        CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = new CreateCustomerOrderDetailsRequest();
        createCustomerOrderDetailsRequest.setDenomination(BigDecimal.TEN);
        createCustomerOrderDetailsRequest.setVoucherNum(1);

        createCustomerOrderDetailsRequests.add(createCustomerOrderDetailsRequest);
        createCustomerOrderRequest.setCreateCustomerOrderDetailsRequests(createCustomerOrderDetailsRequests);
        Mockito.when(codeHelper.generateCustomerOrderCode()).thenReturn("xxx");

        ArticleMop articleMop = new ArticleMop();
        articleMop.setArticleCodeName("Xxx");
        Mockito.when(articleMopMapper.getArticleNameByCpgCode(Mockito.any())).thenReturn(articleMop);

        Cpg cpg = new Cpg();
        cpg.setDenomination(BigDecimal.TEN);
        cpg.setCpgName("xxx");
        Mockito.when(cpgMapper.selectOne(Mockito.any())).thenReturn(cpg);
        Assert.assertNotNull(customerOrderServiceImpl.createCustomerOrder(createCustomerOrderRequest));
    }

    @Test
    public void queryCustomerOrder() {
        QueryCustomerOrderRequest queryCustomerOrderRequest = new QueryCustomerOrderRequest();
        List<QueryCustomerOrderResponse> queryCustomerOrderResponseList = new ArrayList<>();
        QueryCustomerOrderResponse queryCustomerOrderResponse = new QueryCustomerOrderResponse();
        queryCustomerOrderResponse.setCustomerOrderCode("xxx");
        queryCustomerOrderResponse.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        queryCustomerOrderResponse.setStatus(CustomerOrderStatusEnum.RELEASE.getStatus());

        queryCustomerOrderResponseList.add(queryCustomerOrderResponse);

        List<CustomerOrderDetails> customerOrderDetails = new ArrayList<>();
        CustomerOrderDetails customerOrderDetails1 = new CustomerOrderDetails();
        customerOrderDetails1.setDenomination(BigDecimal.TEN);
        customerOrderDetails.add(customerOrderDetails1);

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode("xxx");
        Mockito.when(voucherAllocationService.getAllocationBySourceDataCode(Mockito.any(), Mockito.any())).thenReturn(voucherAllocation);

        Mockito.when(customerOrderMapper.queryOrder(Mockito.any())).thenReturn(queryCustomerOrderResponseList);
        Mockito.when(customerOrderDetailsMapper.selectByCondition(Mockito.any())).thenReturn(customerOrderDetails);

        VoucherAllocation voucherAllocation1 = new VoucherAllocation();
        voucherAllocation1.setCreateTime(new Date());
        Mockito.when(voucherAllocationService.getAllocationBySourceDataCode(Mockito.any(), Mockito.any())).thenReturn(voucherAllocation1);
        Assert.assertNotNull(customerOrderServiceImpl.queryCustomerOrder(queryCustomerOrderRequest));
    }

    /*@Test
    public void getCustomerOrder() {
        GetCustomerOrderRequest getCustomerOrderRequest = new GetCustomerOrderRequest();
        getCustomerOrderRequest.setCustomerOrderCode("CO102203211147000007");
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(new CustomerOrder());
        Mockito.when(customerOrderDetailsMapper.select(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.when(customerOrderReceiverMapper.selectOne(Mockito.any())).thenReturn(new CustomerOrderReceiver());
        Result<List<ApproveNodeRecordResponse>> listResult = new Result<>();
        listResult.setData(Arrays.asList(new ApproveNodeRecordResponse()));
        Mockito.when(releaseApproveService.queryLogByBusinessCode(Mockito.any(), Mockito.any())).thenReturn(listResult);

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode("xxx");
        Mockito.when(voucherAllocationService.getAllocationBySourceDataCode(Mockito.any(), Mockito.any())).thenReturn(voucherAllocation);

        Assert.assertNotNull(customerOrderServiceImpl.getCustomerOrder(getCustomerOrderRequest));
    }*/

    @Test
    public void updateCustomerOrder() {
        UpdateCustomerOrderRequest updateCustomerOrderRequest = new UpdateCustomerOrderRequest();
        updateCustomerOrderRequest.setVoucherAmount(BigDecimal.TEN);
        updateCustomerOrderRequest.setVoucherNum(1);
        updateCustomerOrderRequest.setPurchaseOrderNo("1122");
        updateCustomerOrderRequest.setUpdateUser("xxx");
        updateCustomerOrderRequest.setCustomerName("xxx");

        List<UpdateCustomerOrderDetailsRequest> updateCustomerOrderDetailsRequests = new ArrayList<>();
        UpdateCustomerOrderDetailsRequest updateCustomerOrderDetailsRequest = new UpdateCustomerOrderDetailsRequest();
        updateCustomerOrderDetailsRequest.setDenomination(BigDecimal.TEN);
        updateCustomerOrderDetailsRequest.setVoucherNum(1);
        updateCustomerOrderDetailsRequests.add(updateCustomerOrderDetailsRequest);

        updateCustomerOrderRequest.setUpdateCustomerOrderDetailsRequests(updateCustomerOrderDetailsRequests);

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setPurchaseOrderNo("1122");
        customerOrder.setCreateTime(new Date());
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);

        CustomerOrderReceiver customerOrderReceiver = new CustomerOrderReceiver();
        Mockito.when(customerOrderReceiverMapper.selectOne(Mockito.any())).thenReturn(customerOrderReceiver);
        Assert.assertNotNull(customerOrderServiceImpl.updateCustomerOrder(updateCustomerOrderRequest));
    }

    @Test
    public void generatePurchaseOrderNumber() {
        Assert.assertNotNull(customerOrderServiceImpl.generatePurchaseOrderNumber("123"));
    }

    @Test
    public void submitCustomerOrder() {
        SubmitCustomerOrderRequest submitCustomerOrderRequest = new SubmitCustomerOrderRequest();
        CustomerOrder order = new CustomerOrder();
        order.setMopCode("123");
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(order);
        Assert.assertNotNull(customerOrderServiceImpl.submitCustomerOrder(submitCustomerOrderRequest));
    }

    @Test
    public void deliverCustomerOrder() {
        DeliverCustomerOrderRequest deliverCustomerOrderRequest = new DeliverCustomerOrderRequest();
        deliverCustomerOrderRequest.setDeliveType(1);
        deliverCustomerOrderRequest.setTrackNo("12");
        deliverCustomerOrderRequest.setLogisticsName("123");
        CustomerOrder customerOrder = new CustomerOrder();
		customerOrder.setStatus(CustomerOrderStatusEnum.RELEASE.getStatus());
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);
        customerOrderServiceImpl.deliverCustomerOrder(deliverCustomerOrderRequest);
        Assert.assertTrue(true);
    }

    @Test
    public void receiveCustomerOrder() {
        ReceiveRequest request = new ReceiveRequest();
        customerOrderServiceImpl.receive(request);
        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setStatus(CustomerOrderStatusEnum.RELEASE.getStatus());
        customerOrder.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);
        Mockito.when(customerOrderMapper.updateStatus(Mockito.any())).thenReturn(1);
        customerOrderServiceImpl.receive(request);
		customerOrder.setStatus(CustomerOrderStatusEnum.DELIVER.getStatus());
		customerOrderServiceImpl.receive(request);
        Assert.assertTrue(true);
    }


    @Test
    public void approveCustomerOrder() {
        ApproveCustomerOrderRequest approveCustomerOrderRequest = new ApproveCustomerOrderRequest();
        approveCustomerOrderRequest.setCustomerOrderCode("xxxx");
        approveCustomerOrderRequest.setStatus(true);

        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setNextRoleCode("xxx");

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setStatus(CustomerOrderStatusEnum.SUBMIT.getStatus());
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);
        Assert.assertNotNull(customerOrderServiceImpl.approveCustomerOrder(approveCustomerOrderRequest));

        customerOrder.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);
        Assert.assertNotNull(customerOrderServiceImpl.approveCustomerOrder(approveCustomerOrderRequest));
    }


    @Test
    public void approveCustomerOrderAble() {
        ReleaseApproveAbleRequest releaseApproveAbleRequest = new ReleaseApproveAbleRequest();
        releaseApproveAbleRequest.setBusinessCode("xxx");

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setVoucherAmount(BigDecimal.TEN);
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);
        Mockito.when(releaseApproveService.approveAble(Mockito.any())).thenReturn(new Result<>(1));
        Assert.assertNotNull(customerOrderServiceImpl.approveCustomerOrderAble(releaseApproveAbleRequest));

        Mockito.when(releaseApproveService.approveAble(Mockito.any())).thenReturn(new Result<>(-1));
        Assert.assertNotNull(customerOrderServiceImpl.approveCustomerOrderAble(releaseApproveAbleRequest));
    }

    @Test
    public void nonSystemCreateCustomerOrder() {

        List<CreateCustomerOrderDetailsRequest> createCustomerOrderDetailsRequests = new ArrayList<>();
        CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = new CreateCustomerOrderDetailsRequest();
        createCustomerOrderDetailsRequest.setDenomination(BigDecimal.TEN);
        createCustomerOrderDetailsRequest.setVoucherNum(1);

        createCustomerOrderDetailsRequests.add(createCustomerOrderDetailsRequest);
        Mockito.when(codeHelper.generateCustomerOrderCode()).thenReturn("xxx");

        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(new CustomerOrder());

        NonSystemCreateCustomerOrderRequest nonSystemCreateCustomerOrderRequest = new NonSystemCreateCustomerOrderRequest();
        nonSystemCreateCustomerOrderRequest.setCustomerName("xxx");
        nonSystemCreateCustomerOrderRequest.setVoucherAmount(BigDecimal.TEN);
        nonSystemCreateCustomerOrderRequest.setVoucherNum(1);
        nonSystemCreateCustomerOrderRequest.setCreateCustomerOrderDetailsRequests(createCustomerOrderDetailsRequests);

        CustomerOrder order = new CustomerOrder();
        order.setId(0L);
        order.setCustomerOrderCode("");
        order.setIssuerCode("");
        order.setOutletCode("");
        order.setPurchaseOrderNo("");
        order.setMopCode("");
        order.setMeansOfPaymentCode("");
        order.setVoucherNum(0);
        order.setVoucherAmount(new BigDecimal("0"));
        order.setDiscount(new BigDecimal("0"));
        order.setAmount(new BigDecimal("0"));
        order.setCurrencyCode("");
        order.setCustomerCode("");
        order.setCustomerName("");
        order.setCustomerType("");
        order.setCompanyName("");
        order.setContactFirstName("");
        order.setContactLastName("");
        order.setContactPhone("");
        order.setContactEmail("");
        order.setProductCategoryCode("");
        order.setDiscountType("");
        order.setInvoiceNo("");
        order.setDeliveType(0);
        order.setLogisticsName("");
        order.setTrackNo("");
        order.setStatus("");
        order.setVoucherBatchCode("");
        order.setReleaseTime(new Date());
        order.setCreateUser("");
        order.setCreateTime(new Date());
        order.setUpdateUser("");
        order.setUpdateTime(new Date());


        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(order);

        Assert.assertNotNull(
                customerOrderServiceImpl.nonSystemCreateCustomerOrder(nonSystemCreateCustomerOrderRequest));
    }

    /*@Test
    public void issuance() {

        IssuanceRequest request = new IssuanceRequest();
        request.setCustomerOrderCode("customerOrderCode");
        request.setUpdateUser("updateUser");
        request.setVoucherBatchList(new ArrayList<>());
        customerOrderServiceImpl.issuance(request);

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setIssuerCode("issuerCode");
        customerOrder.setCustomerOrderCode(request.getCustomerOrderCode());
        customerOrder.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        customerOrder.setStatus(CustomerOrderStatusEnum.SUBMIT.getStatus());
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        customerOrderServiceImpl.issuance(request);

        customerOrder.setStatus(CustomerOrderStatusEnum.APPROVAL.getStatus());
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        customerOrderServiceImpl.issuance(request);

        List<IssuanceVoucherBatch> voucherBatchList = new ArrayList<>();
        IssuanceVoucherBatch batch = new IssuanceVoucherBatch();
        batch.setVoucherStartNo("11");
        batch.setVoucherEndNo("100");
        voucherBatchList.add(batch);
        request.setVoucherBatchList(voucherBatchList);
        customerOrderServiceImpl.issuance(request);

        batch.setDenomination(new BigDecimal("100"));
        customerOrderServiceImpl.issuance(request);

        when(customerOrderMapper.updateStatus(any(CustomerOrderDto.class))).thenReturn(1);

        when(voucherAllocationService.allocateByCustomerOrder(any(IssuanceRequest.class), any(CustomerOrder.class),
                any(List.class))).thenReturn(Result.ok());
        customerOrderServiceImpl.issuance(request);

//        // TODO
//        List<CustomerOrderDetails> details = null;
//        when(customerOrderDetailsMapper.select(any(CustomerOrderDetails.class))).thenReturn(details);

        customerOrder = new CustomerOrder();
        customerOrder.setIssuerCode("issuerCode");
        customerOrder.setCustomerOrderCode(request.getCustomerOrderCode());
        customerOrder.setMopCode("VCE");
        customerOrder.setStatus(CustomerOrderStatusEnum.APPROVAL.getStatus());
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        try {
            when(voucherBatchService.generateDigitalVoucher(any(GenerateDigitalVouchersRequest.class))).thenReturn(Result.failed(""));
        } catch (IOException e) {
            // TODO Auto-generated catch block
        }

        List<CustomerOrderDetails> details = new ArrayList<>();
        CustomerOrderDetails detail = new CustomerOrderDetails();
        detail.setCustomerOrderDetailsCode("customerOrderDetailsCode");
        detail.setCpgCode("cpgCode");
        detail.setDenomination(new BigDecimal("10"));
        detail.setVoucherNum(100);
        details.add(detail);
        when(customerOrderDetailsMapper.select(any(CustomerOrderDetails.class))).thenReturn(details);
        try {
            //customerOrderServiceImpl.issuance(request);
        } catch (GTechBaseException e) {

        }

        try {
            when(voucherBatchService.generateDigitalVoucher(any(GenerateDigitalVouchersRequest.class))).thenReturn(Result.ok("12313"));
        } catch (IOException e) {
            // TODO Auto-generated catch block
        }
        try {
            customerOrderServiceImpl.issuance(request);
        } catch (GTechBaseException e) {

        }

        when(customerOrderMapper.updateByPrimaryKeySelective(any(CustomerOrder.class))).thenReturn(1);
        customerOrderServiceImpl.issuance(request);

    }*/

    @Test
    public void release() {

        ReleaseRequest request = new ReleaseRequest();
        request.setCustomerOrderCode("customerOrderCode");
        request.setStatus(false);
        request.setUpdateUser("updateUser");
        customerOrderServiceImpl.release(request);

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setIssuerCode("issuerCode");
        customerOrder.setCustomerOrderCode(request.getCustomerOrderCode());
        customerOrder.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        customerOrder.setStatus(CustomerOrderStatusEnum.SUBMIT.getStatus());
        customerOrder.setVoucherBatchCode("voucherBatchCode");
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        customerOrderServiceImpl.release(request);

        customerOrder.setStatus(CustomerOrderStatusEnum.ISSUANCE.getStatus());
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        customerOrderServiceImpl.release(request);

        when(customerOrderMapper.updateStatus(any(CustomerOrderDto.class))).thenReturn(1);
        customerOrderServiceImpl.release(request);

        customerOrder = new CustomerOrder();
        customerOrder.setIssuerCode("issuerCode");
        customerOrder.setCustomerOrderCode(request.getCustomerOrderCode());
        customerOrder.setMopCode("VCE");
        customerOrder.setStatus(CustomerOrderStatusEnum.ISSUANCE.getStatus());
        customerOrder.setVoucherBatchCode("voucherBatchCode");
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        try {
            customerOrderServiceImpl.release(request);
        } catch (GTechBaseException e) {

        }

        when(voucherService.deleteByVoucherBatch(any(String.class))).thenReturn(100);
        customerOrderServiceImpl.release(request);

        request.setStatus(true);
        customerOrderServiceImpl.release(request);

        request.setNotes("notes");
        customerOrderServiceImpl.release(request);

    }

    /*@Test
    public void approveCustomerOrderRelease() {

        ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
        approveNodeRecordRequest.setApproveUser("approveUser");
        approveNodeRecordRequest.setBusinessCode("businessCode");
        approveNodeRecordRequest.setNote("note");
        approveNodeRecordRequest.setReleaseApproveAmountType("releaseApproveAmountType");
        approveNodeRecordRequest.setReleaseType("releaseType");
        approveNodeRecordRequest.setRoleCode("roleCode");
        approveNodeRecordRequest.setStatus(true);
        approveNodeRecordRequest.setVoucherAmount(new BigDecimal("10000"));
        Result<String> result = customerOrderServiceImpl.approveCustomerOrderRelease(approveNodeRecordRequest);

        CustomerOrder customerOrder = new CustomerOrder();
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        Result<ApproveNodeRecord> approve = Result.ok();
        when(releaseApproveService.approve(any(ApproveNodeRecordRequest.class))).thenReturn(approve);
        result = customerOrderServiceImpl.approveCustomerOrderRelease(approveNodeRecordRequest);

        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setNextRoleCode("nextRoleCode");
        approve = Result.ok(approveNodeRecord);
        when(releaseApproveService.approve(any(ApproveNodeRecordRequest.class))).thenReturn(approve);
        result = customerOrderServiceImpl.approveCustomerOrderRelease(approveNodeRecordRequest);

        approveNodeRecord = new ApproveNodeRecord();
        approve = Result.ok(approveNodeRecord);
        when(releaseApproveService.approve(any(ApproveNodeRecordRequest.class))).thenReturn(approve);

        customerOrder = new CustomerOrder();
        customerOrder.setIssuerCode("issuerCode");
        customerOrder.setCustomerOrderCode(approveNodeRecordRequest.getBusinessCode());
        customerOrder.setMopCode("VCE");
        customerOrder.setStatus(CustomerOrderStatusEnum.ISSUANCE.getStatus());
        customerOrder.setVoucherBatchCode("voucherBatchCode");
        when(customerOrderMapper.selectOne(any(CustomerOrder.class))).thenReturn(customerOrder);
        when(customerOrderMapper.updateByRelease(any(CustomerOrderDto.class))).thenReturn(1);
        result = customerOrderServiceImpl.approveCustomerOrderRelease(approveNodeRecordRequest);

        //assertTrue(result.isSuccess());
    }*/

    @Test
    public void cancelRelease() {
        CancelReleaseRequest cancelReleaseRequest = new CancelReleaseRequest();

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setStatus(CustomerOrderStatusEnum.RELEASE.getStatus());
        customerOrder.setMopCode(GvcoreConstants.MOP_CODE_VCR);
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);

        Outlet outlet = new Outlet();
        outlet.setOutletType("xxx");
        outlet.setOutletCode("xxx");
        Mockito.when(outletMapper.selectOne(Mockito.any())).thenReturn(outlet);

        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setCreateTime(new Date());
        Mockito.when(approveNodeRecordMapper.selectNewNote(Mockito.any(), Mockito.any())).thenReturn(approveNodeRecord);

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherOwnerCode("xxx");
        voucherAllocation.setVoucherAllocationCode("xxx");
        Mockito.when(voucherAllocationService.getAllocationBySourceDataCode(Mockito.any(), Mockito.any())).thenReturn(voucherAllocation);

        List<Voucher> vouchers = new ArrayList<>();
        Mockito.when(voucherMapper.queryVoucherListByAllocateCode(Mockito.any(),Mockito.any())).thenReturn(vouchers);
        assertNotNull(customerOrderServiceImpl.cancelRelease(cancelReleaseRequest));
    }

    @Test
    public void uploadPaymentVoucher() {
        UploadPaymentVoucherRequest uploadPaymentVoucherRequest = new UploadPaymentVoucherRequest();
        uploadPaymentVoucherRequest.setPaymentVoucherUrl("xxx");
        uploadPaymentVoucherRequest.setCustomerOrderCode("xxx");
        uploadPaymentVoucherRequest.setUserCode("Xxx");

        Mockito.when(customerOrderReceiverMapper.selectOne(Mockito.any())).thenReturn(new CustomerOrderReceiver());
        assertNotNull(customerOrderServiceImpl.uploadPaymentVoucher(uploadPaymentVoucherRequest));
    }

    @Test
    public void delPaymentVoucher() {
        DelPaymentVoucherRequest delPaymentVoucherRequest = new DelPaymentVoucherRequest();

        delPaymentVoucherRequest.setUserCode("xxx");
        Mockito.when(customerOrderReceiverMapper.selectOne(Mockito.any())).thenReturn(new CustomerOrderReceiver());
        assertNotNull(customerOrderServiceImpl.delPaymentVoucher(delPaymentVoucherRequest));
    }

    @Test
    public void sendEmail() {
        SendCustomerOrderEmailRequest sendCustomerOrderEmailRequest = new SendCustomerOrderEmailRequest();
        sendCustomerOrderEmailRequest.setCustomerOrderCode("xxx");
        sendCustomerOrderEmailRequest.setFileName("Quotation");
        sendCustomerOrderEmailRequest.setFileUrl("https://gtech-biz.oss-cn-shanghai.aliyuncs.com/dev/GV/20220427/export/syNcEiX5bpVqIiymwaj3uzRGp7Hiw34I/C%3A%5CUsers%5Czhanglong%5CDesktop66497488422200Quotation.pdf");

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setContactEmail("<EMAIL>");
        Mockito.when(customerOrderMapper.selectOne(Mockito.any())).thenReturn(customerOrder);
        assertNotNull(customerOrderServiceImpl.sendEmail(sendCustomerOrderEmailRequest));
        sendCustomerOrderEmailRequest.setFileName("Quotation.pdf");
        assertNotNull(customerOrderServiceImpl.sendEmail(sendCustomerOrderEmailRequest));
        sendCustomerOrderEmailRequest.setFileName("Invoice.pdf");
        assertNotNull(customerOrderServiceImpl.sendEmail(sendCustomerOrderEmailRequest));
    }
}