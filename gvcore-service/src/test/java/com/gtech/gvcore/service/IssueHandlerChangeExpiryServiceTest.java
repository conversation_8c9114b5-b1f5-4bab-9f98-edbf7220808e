
package com.gtech.gvcore.service;

import java.util.Arrays;
import java.util.Date;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.impl.issuehandle.IssueHandlerChangeExpiryService;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class IssueHandlerChangeExpiryServiceTest {

	@InjectMocks
	IssueHandlerChangeExpiryService changeExpiryService;

	@Mock
	VoucherService voucherService;
	@Mock
	VoucherMapper voucherMapper;
	
	@Mock
	TransactionDataService transactionDataService;
	
	@Before
	public void before() {
		EntityHelper.initEntityNameMap(Voucher.class, new MapperHelper().getConfig());
	}
	
	@Test
	public void getIssueHandlingTypeTest() {
		changeExpiryService.getIssueHandlingType();
	}
	

	@Test
	public void validTest() {
		changeExpiryService.validate(null, "1");
	}
	
	@Test
	public void executeTest() {
		changeExpiryService.execute(null, "1");
		IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
		changeExpiryService.execute(Arrays.asList(issueHandlingDetails), "1");
		issueHandlingDetails.setVoucherCode("123");
		changeExpiryService.execute(Arrays.asList(issueHandlingDetails), "1");
		issueHandlingDetails.setVoucherEffectiveDate(new Date());
		changeExpiryService.execute(Arrays.asList(issueHandlingDetails), "1");
		Voucher voucher = new Voucher();
		voucher.setVoucherCode("123");
		Mockito.when(voucherService.queryByVoucherCodeList(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(voucher));
		changeExpiryService.execute(Arrays.asList(issueHandlingDetails), "1");
	}
	
	@Test
	public void makeIssueHandlingTest() {
		IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		issueHandlingDetails.setVoucherCode("123");
		changeExpiryService.makeIssueHandling(issueHandlingDetails);
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		issueHandlingDetails.setVoucherEffectiveDate(new Date());
		changeExpiryService.makeIssueHandling(issueHandlingDetails);
		Mockito.when(voucherMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		changeExpiryService.makeIssueHandling(issueHandlingDetails);
	}
}
