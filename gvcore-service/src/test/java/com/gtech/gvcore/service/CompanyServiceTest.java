package com.gtech.gvcore.service;

import com.google.common.collect.Lists;
import com.gtech.gvcore.common.request.company.CreateCompanyRequest;
import com.gtech.gvcore.common.request.company.DeleteCompanyRequest;
import com.gtech.gvcore.common.request.company.GetCompanyRequest;
import com.gtech.gvcore.common.request.company.QueryCompanyByIssuerCodesRequest;
import com.gtech.gvcore.common.request.company.QueryCompanyRequest;
import com.gtech.gvcore.common.request.company.UpdateCompanyRequest;
import com.gtech.gvcore.common.request.company.UpdateCompanyStatusRequest;
import com.gtech.gvcore.dao.mapper.CompanyMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.CompanyServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class CompanyServiceTest {

    @InjectMocks
    private CompanyServiceImpl companyService;
    
    @Mock
    private CompanyMapper companyMapper;
    
    @Mock
    private GvCodeHelper codeHelper;

    @Mock
    private IssuerService issuerService;
    
    
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(Company.class, new MapperHelper().getConfig());
    }



    @Test
    public void createCompany(){
        CreateCompanyRequest request = new CreateCompanyRequest();
        request.setCompanyName("name");
        request.setCreateUser("user");

        request.setCreateUser("123");
        companyService.createCompany(request);
    }

    @Test
    public void updateCompany(){
        UpdateCompanyRequest request = new UpdateCompanyRequest();
        request.setCompanyCode("code");
        request.setCompanyName("123");
        request.setStatus(0);
        request.setUpdateUser("123");

        companyService.updateCompany(request);
    }

    @Test
    public void updateCompanyStatus(){
        UpdateCompanyStatusRequest request = new UpdateCompanyStatusRequest();
        request.setCompanyCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");
        companyService.updateCompanyStatus(request);
    }


    @Test
    public void deleteCompany(){
        DeleteCompanyRequest request = new DeleteCompanyRequest();
        request.setCompanyCode("123");

        companyService.deleteCompany(request);
    }


    @Test
    public void queryCompanyList(){

        QueryCompanyRequest request = new QueryCompanyRequest();
        ArrayList<Company> objects = new ArrayList<>();
        List<Company> gvCompanyEntities = new ArrayList<>();
        Company Company = new Company();
        Company.setId(0L);
        Company.setCompanyCode("123");
        Company.setCompanyName("123");
        Company.setStatus(0);
        Company.setCreateUser("123");
        Company.setCreateTime(new Date());
        Company.setUpdateUser("123");
        Company.setUpdateTime(new Date());
        gvCompanyEntities.add(Company);

        List<Company> gvCompany = new ArrayList<>();
        Company company = new Company();
        company.setCompanyName("123");
        company.setCompanyCode("123");
        //company.setIssuerCode("123");
        gvCompany.add(company);

        Mockito.when(companyMapper.selectByCondition(Mockito.any())).thenReturn(gvCompany);


        companyService.queryCompanyList(request);

    }



    @Test
    public void getCompany(){

        GetCompanyRequest request = new GetCompanyRequest();
        request.setCompanyCode("123");
        Company company = new Company();
        company.setCompanyName("123");
        company.setCompanyCode("123");
        //company.setIssuerCode("123");
        Mockito.when(companyMapper.selectOne(Mockito.any())).thenReturn(company);
        companyService.getCompany(request);

    }


    @Test
    public void queryCompanyByIssuerCodes(){
        QueryCompanyByIssuerCodesRequest queryCompanyByIssuerCodesRequest = new QueryCompanyByIssuerCodesRequest();
        queryCompanyByIssuerCodesRequest.setIssuerCodes(Lists.newArrayList("123123"));
        companyService.queryCompanyByIssuerCodes(queryCompanyByIssuerCodesRequest);
    }
    
	@Test
	public void queryCompanyByCodes() {
		companyService.queryCompanyByCodes(Arrays.asList("123"));
	}
    
}
