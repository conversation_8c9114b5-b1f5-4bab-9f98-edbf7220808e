package com.gtech.gvcore.service;


import com.google.common.collect.Lists;
import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.request.businesslogdetail.QueryBusinessLogDetailRequest;
import com.gtech.gvcore.dao.mapper.BusinessLogDetailMapper;
import com.gtech.gvcore.dao.model.BusinessLogDetails;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.BusinessLogDetailImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class BusinessLogDetailTest {

    @InjectMocks
    private BusinessLogDetailImpl businessLogDetail;


    @Mock
    private BusinessLogDetailMapper businessLogDetailMapper;


    @Mock
    private GvCodeHelper codeHelper;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(BusinessLogDetails.class, new MapperHelper().getConfig());
    }



    @Test
    public void createBusinessLogDetail(){
        CreateBusinessLogDetailRequest request = new CreateBusinessLogDetailRequest();
        request.setBusinessCode("123");
        request.setReason("123");
        request.setDetailContentCode("123");
        businessLogDetail.createBusinessLogDetail(request);

    }


    @Test
    public void createBusinessLogDetailList(){

        CreateBusinessLogDetailRequest request = new CreateBusinessLogDetailRequest();
        request.setBusinessCode("123");
        request.setReason("123");
        request.setDetailContentCode("123");
        businessLogDetail.createBusinessLogDetailList(Lists.newArrayList(request));

    }



    @Test
    public void queryBusinessLogDetail(){
        QueryBusinessLogDetailRequest request = new QueryBusinessLogDetailRequest();
        request.setBusinessCode("123");
        businessLogDetail.queryBusinessLogDetail(request);
    }








}
