package com.gtech.gvcore.service;

import java.math.BigDecimal;
import java.util.Arrays;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.IgnoreCommonException;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.request.productcategory.CalculateDiscountAmountRequest;
import com.gtech.gvcore.dao.model.ProductCategoryDisscount;
import com.gtech.gvcore.dao.model.ProductCategoryDisscountDetails;
import com.gtech.gvcore.service.impl.ProductCategoryServiceImpl;

/**
 * <AUTHOR>
 * @Date 2022/2/15 10:29
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryServiceTest {


    @InjectMocks
	private ProductCategoryServiceImpl productCategoryService;

    @Mock
	private ProductCategoryDisscountService productCategoryDisscountService;

	@Mock
	private ProductCategoryDisscountDetailsService productCategoryDisscountDetailsService;

    @Test
	public void calculateDiscountAmountTest() {
		CalculateDiscountAmountRequest request = new CalculateDiscountAmountRequest();
		request.setAmount(BigDecimal.valueOf(1000l));
		request.setProductCategoryCode("123");
		IgnoreCommonException.executeV2((x) -> productCategoryService.calculateDiscountAmount(request));
		ProductCategoryDisscount productCategoryDisscount = new ProductCategoryDisscount();
		productCategoryDisscount.setStatus(1);
		Mockito.when(productCategoryDisscountService.queryByProductCategoryCode(Mockito.any())).thenReturn(productCategoryDisscount);
		ProductCategoryDisscountDetails detail = new ProductCategoryDisscountDetails();
		detail.setStatus(1);
		detail.setFromPurchaseValue(BigDecimal.valueOf(1000l));
		detail.setUptoPurchaseValue(BigDecimal.valueOf(1001l));
		detail.setDiscountType(ProductCategoryDiscountTypeEnum.PERCENTAGE.code());
		detail.setMaximumDiscountValue(BigDecimal.valueOf(1001l));
		detail.setDiscount(BigDecimal.valueOf(1l));
		Mockito.when(productCategoryDisscountDetailsService.queryByProductCategoryDisscountCode(Mockito.any(), Mockito.any()))
				.thenReturn(Arrays.asList(detail));
		productCategoryService.calculateDiscountAmount(request);
		Assert.assertTrue(true);

    }


}
