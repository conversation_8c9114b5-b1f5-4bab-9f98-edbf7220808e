
package com.gtech.gvcore.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.google.common.collect.Lists;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.basic.filecloud.exports.management.FileExportResult;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.impl.issuehandle.IssueHandlerReissueService;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class IssueHandlerReissueServiceTest {

	@InjectMocks
	IssueHandlerReissueService issueHandlerReissueService;
	
	@Mock
	private IssueHandlerReissueService reissueService;
	@Mock
	protected VoucherService voucherService;
	@Mock
	VoucherMapper voucherMapper;
	@Mock
	VoucherNumberHelper voucherNumberHelper;
	@Mock
	IssueHandlingDetailsMapper issueHandlingDetailsMapper;
	@Mock
	OssHelper ossHelper;
	@Mock
	TransactionDataService transactionDataService;
	@Mock
	GvCodeHelper gvCodeHelper;
	
	@Mock
	FileExportManager fileExportManager;
	
	@Mock
	MessageComponent messageComponent;
	@Mock
	CpgService cpgService;
	
	@Mock
	IssuerService issuerService;
	@Mock
	OutletService outletService;


	@Before
	public void before() {
		EntityHelper.initEntityNameMap(Voucher.class, new MapperHelper().getConfig());
		EntityHelper.initEntityNameMap(IssueHandlingDetails.class, new MapperHelper().getConfig());
	}
	
	@Test
	public void getIssueHandlingTypeTest() {
		issueHandlerReissueService.getIssueHandlingType();
	}

	@Test
	public void executeTest() {
		issueHandlerReissueService.execute(null, "1");
		IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
		issueHandlerReissueService.execute(Arrays.asList(issueHandlingDetails), "1");
		issueHandlingDetails.setVoucherCode("123");
		issueHandlerReissueService.execute(Arrays.asList(issueHandlingDetails), "1");
	}
	
	@Test
	public void checkIfExist() {
		List<IssueHandlingDetails> details = new ArrayList<>();
		IssueHandlingDetails a = new IssueHandlingDetails();
		details.add(a);
		a.setVoucherCode("v1");
		IssueHandlingDetails b = new IssueHandlingDetails();
		details.add(b);
		b.setVoucherCode("v2");
		b.setNewVoucherCode("11");
		IssueHandlingDetails c = new IssueHandlingDetails();
		details.add(c);
		c.setVoucherCode("v3");
		IssueHandlingDetails d = new IssueHandlingDetails();
		details.add(d);

		List<Voucher> voucherList = new ArrayList<>();
		Voucher voucher1 = new Voucher();
		voucher1.setVoucherCode("v1");
		voucher1.setStatus(1);
		voucher1.setVoucherStatus(1);
		voucherList.add(voucher1);
		Voucher voucher2 = new Voucher();
		voucher2.setVoucherCode("v2");
		voucher2.setStatus(1);
		voucher2.setVoucherStatus(1);
		voucherList.add(voucher2);
		Mockito.when(voucherService.queryByVoucherCodeList(Mockito.any(), Mockito.any())).thenReturn(voucherList);
		issueHandlerReissueService.checkIfExist(details, IssueHandlingTypeEnum.BULK_DEACTIVATE, "1");
		issueHandlerReissueService.checkIfExist(details, IssueHandlingTypeEnum.BULK_ACTIVATION, "1");
		issueHandlerReissueService.checkIfExist(details, IssueHandlingTypeEnum.BULK_REACTIVATE, "1");
		issueHandlerReissueService.checkIfExist(details, IssueHandlingTypeEnum.BULK_REDEEM, "1");
		issueHandlerReissueService.checkIfExist(details, IssueHandlingTypeEnum.CANCEL_REDEEM, "1");
		issueHandlerReissueService.checkIfExist(details, IssueHandlingTypeEnum.CANCEL_SALES, "1");
	}

	@Test
	public void makeIssueHandlingTest() {
		IssueHandlingDetails issueHandlingDetails = new IssueHandlingDetails();
		issueHandlingDetails.setVoucherCode("123");
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
		issueHandlerReissueService.makeIssueHandling(issueHandlingDetails, "1");
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		issueHandlerReissueService.makeIssueHandling(issueHandlingDetails, "1");
		Voucher voucher = new Voucher();
		voucher.setCpgCode("12");
		voucher.setIssuerCode("1");
		voucher.setVoucherOwnerType(VoucherOwnerTypeEnum.WAREHOUSE.code());
		Voucher voucher1 = new Voucher();
		voucher1.setCpgCode("12");
		voucher1.setIssuerCode("1");
		voucher1.setVoucherOwnerType(VoucherOwnerTypeEnum.OUTLET.code());
		voucher1.setMopCode(GvcoreConstants.MOP_CODE_VCR);



		Mockito.when(voucherService.getVoucherByCode(Mockito.any())).thenReturn(voucher, voucher1);
		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		Mockito.when(transactionDataService.queryTransactionDataByVoucherCode(Mockito.any())).thenReturn(Lists.newArrayList(
				TransactionData.builder()
						.transactionType(TransactionTypeEnum.GIFT_CARD_NEW_GENERATE.getCode())
						.build(),
				TransactionData.builder()
						.transactionType(TransactionTypeEnum.GIFT_CARD_SELL.getCode())
						.build(),
				TransactionData.builder()
						.transactionType(TransactionTypeEnum.GIFT_CARD_ACTIVATE.getCode())
						.build()));



		Mockito.when(voucherNumberHelper.voucherCodeElectronic(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn("1234");

		issueHandlerReissueService.makeIssueHandling(issueHandlingDetails, "1");

	}
	
	@Test
	public void afterExecuteTest(){
		IssueHandling issueHandling = new IssueHandling();
		issueHandling.setIssueHandlingCode("123");
		issueHandlerReissueService.afterExecute(issueHandling);
		IssueHandlingDetails details = new IssueHandlingDetails();
		details.setReceiverEmail("<EMAIL>");
		Mockito.when(issueHandlingDetailsMapper.queryDigitalByIssueHandlingCode(Mockito.any())).thenReturn(Arrays.asList(details));
		issueHandlerReissueService.afterExecute(issueHandling);
		Voucher voucher = new Voucher();
		voucher.setDenomination(BigDecimal.valueOf(12));
		Mockito.when(voucherService.queryByVoucherCodeList(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(voucher));
		FileExportResult result = new FileExportResult();
		CompletableFuture.supplyAsync(() -> result);
		Mockito.when(fileExportManager.export(Mockito.any())).thenReturn(CompletableFuture.supplyAsync(() -> result));
		Mockito.when(voucherNumberHelper.randomPassword(10)).thenReturn("123");
		issueHandlerReissueService.afterExecute(issueHandling);
	}
}
