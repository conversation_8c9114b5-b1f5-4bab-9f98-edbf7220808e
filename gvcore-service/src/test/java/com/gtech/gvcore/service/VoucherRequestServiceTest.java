package com.gtech.gvcore.service;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.OutletTypeEnum;
import com.gtech.gvcore.common.request.voucherrequest.ApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.BulkApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CancelVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestDetailsRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.GetVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.QueryVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestDetailsRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.OutletCpgMapper;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.mapper.VoucherRequestDetailsMapper;
import com.gtech.gvcore.dao.mapper.VoucherRequestMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.OutletCpg;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.impl.VoucherRequestServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 9:53
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class VoucherRequestServiceTest {

    @Mock
    private GTechCodeGenerator codeGenerator;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Mock
    private VoucherRequestMapper voucherRequestMapper;

    @Mock
    private VoucherRequestDetailsMapper voucherRequestDetailsMapper;


    @InjectMocks
    private VoucherRequestServiceImpl voucherRequestService;

    @Mock
    private VoucherAllocationService voucherAllocationService;

    @Mock
    private ApproveNodeRecordMapper approveNodeRecordMapper;

    @Mock
    private CpgMapper cpgMapper;

    @Mock
    private OutletCpgMapper outletCpgMapper;

    @Mock
    private ReleaseApproveService releaseApproveService;

    @Mock
    private OutletMapper outletMapper;

    @Mock
    private FlowNoticeService flowNoticeService;

    @Spy
    private Map<String, String> issuerWarehouseMap = new HashMap<>();

    @Spy
    private Map<String, String> issuerBusinessWarehouseMap = new HashMap<>();

    @Mock
    private GvUserAccountService userAccountService;

	@Mock
	private MessageComponent messageComponent;

    @Mock
    private PermissionHelper permissionHelper;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(VoucherRequest.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(OutletCpg.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(Cpg.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(VoucherRequestDetails.class, new MapperHelper().getConfig());
    }


    @Test
    public void addVoucherRequest() {

        List<CreateVoucherRequestDetailsRequest> createVoucherRequestDetailsRequests = Arrays.asList(
                CreateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(50000))
                        .voucherNum(50)
                        .voucherAmount(new BigDecimal(50000).multiply(new BigDecimal(50)))
                        .build(),
                CreateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(100000))
                        .voucherNum(50)
                        .voucherAmount(new BigDecimal(100000).multiply(new BigDecimal(50)))
                        .build()
        );

        CreateVoucherRequestRequest build = CreateVoucherRequestRequest.builder()
                .voucherOwnerCode("1122333")
                .issuerCode("1")
                .address1("New York Financial Tower")
                .cityCode("cityCode")
                .createUser("createUser")
                .currencyCode("currencyCode")
                .districtCode("districtCode")
                .email("<EMAIL>")
                .mobile("+01-8846664")
                .receiverCode("OU102203071410000010")
                .receiverName("MAP")
                .permissionCode("1122333")
                .phone("8846664")
                .requestRemarks("thank you")
                .stateCode("stateCode")
                .voucherAmount(new BigDecimal("7500000"))
                .detailsRequests(createVoucherRequestDetailsRequests)
                .voucherNum(100).build();

        Mockito.when(outletMapper.selectOne(Mockito.any())).thenReturn(Outlet.builder()
                .outletType(OutletTypeEnum.WAREHOUSE.code())
                .outletName("HO01")
                .build());

        issuerWarehouseMap.put("1", "HO01");
        issuerBusinessWarehouseMap.put("1", "HO01");
        //Mockito.when(outletMapper.ifExist(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(gvCodeHelper.generateVoucherRequestCode()).thenReturn("123");
        Assert.assertNotNull(voucherRequestService.addVoucherRequest(build,"request" ));
    }


    @Test
    public void updateVoucherRequest() {
        List<UpdateVoucherRequestDetailsRequest> updateVoucherRequestDetailsRequests = Arrays.asList(
                UpdateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(50000))
                        .voucherNum(100)
                        .voucherAmount(new BigDecimal(50000).multiply(new BigDecimal(50)))
                        .build(),
                UpdateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(100000))
                        .voucherNum(50)
                        .voucherAmount(new BigDecimal(100000).multiply(new BigDecimal(50)))
                        .build()
        );

        UpdateVoucherRequestRequest build = UpdateVoucherRequestRequest.builder()
                .voucherOwnerCode("1122333")
                .businessType("sales")
                .address1("New York Financial Tower")
                .cityCode("cityCode")
                .updateUser("updateUser")
                .issuerCode("1")
                .currencyCode("currencyCode")
                .districtCode("districtCode")
                .email("<EMAIL>")
                .mobile("+01-8846664")
                .receiverCode("OU102203071410000010")
                .receiverName("MAP")
                .permissionCode("1122333")
                .phone("8846664")
                .requestRemarks("thank you")
                .stateCode("stateCode")
                .voucherAmount(new BigDecimal("7500000"))
                .updateVoucherRequestDetailsRequests(updateVoucherRequestDetailsRequests)
                .voucherNum(150).build();


        Mockito.when(outletMapper.selectOne(Mockito.any())).thenReturn(Outlet.builder()
                .outletType(OutletTypeEnum.WAREHOUSE.code())
                .outletName("HO01")
                .build());
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(VoucherRequest.builder().status(1).build());

        issuerWarehouseMap.put("1", "HO01");
        issuerBusinessWarehouseMap.put("1", "HO01");

        //Mockito.when(outletMapper.ifExist(Mockito.any(), Mockito.any())).thenReturn(true);

        Assert.assertNotNull(voucherRequestService.updateVoucherRequest(build));
    }

    @Test
    public void updateRequestStatus() {
        Assert.assertNotNull(voucherRequestService.updateRequestStatus("VR102203091414000005", 2, "lisaa"));

    }

    @Test
    public void approveVoucherRequest() {
        ApproveVoucherRequestRequest build = ApproveVoucherRequestRequest.builder()
                .approveRemarks("test")
                .status(4)
                .updateUser("updateUserApprove")
                .voucherRequestCode("VR102203091414000005")
                .build();
        VoucherRequest voucherRequest = new VoucherRequest("xxx");
        voucherRequest.setStatus(1);
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(voucherRequest);
        Mockito.when(voucherRequestMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(voucherRequestService.approveVoucherRequest(build));
    }

    @Test
    public void queryVoucherRequest() {
        QueryVoucherRequestRequest queryVoucherRequestRequest = new QueryVoucherRequestRequest();
        queryVoucherRequestRequest.setStatus(1);
        queryVoucherRequestRequest.setBusinessType(GvcoreConstants.SALES);
        List<QueryVoucherRequestResponse> requestResponses = Arrays.asList(
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(1).build(),
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(1).build(),
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(1).build(),
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(2).build(),
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(1).build(),
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(1).build(),
                QueryVoucherRequestResponse.builder().voucherRequestCode("xxxx").status(4).build()
        );
        List<VoucherRequestDetails> voucherRequestDetails = Arrays.asList(
                VoucherRequestDetails.builder().denomination(new BigDecimal(5)).build(),
                VoucherRequestDetails.builder().denomination(new BigDecimal(5)).build(),
                VoucherRequestDetails.builder().denomination(new BigDecimal(5)).build(),
                VoucherRequestDetails.builder().denomination(new BigDecimal(5)).build()
        );

        final PermissionCodeResponse permissionCodeResponse = new PermissionCodeResponse();
        permissionCodeResponse.setIssuerCode(UUID.randomUUID().toString());
        permissionCodeResponse.setOutletCodeList(Collections.singletonList(UUID.randomUUID().toString()));

        Mockito.when(voucherRequestMapper.queryVoucherRequestList(Mockito.any())).thenReturn(requestResponses);
        Mockito.when(voucherRequestDetailsMapper.selectByCondition(Mockito.any())).thenReturn(voucherRequestDetails);
        Mockito.when(this.permissionHelper.getPermission(Mockito.any(), Mockito.any())).thenReturn(permissionCodeResponse);
        Assert.assertNotNull(voucherRequestService.queryVoucherRequest(queryVoucherRequestRequest));
    }

    @Test
    public void getVoucherRequest() {
        VoucherRequest voucherRequest = new VoucherRequest();
        voucherRequest.setBusinessType(GvcoreConstants.SALES);
        voucherRequest.setReceiverCode("xxx");
		voucherRequest.setStatus(1);
        Mockito.when(voucherRequestMapper.selectOne(Mockito.any())).thenReturn(voucherRequest);
        Mockito.when(voucherRequestDetailsMapper.select(Mockito.any())).thenReturn(Arrays.asList(new VoucherRequestDetails(), new VoucherRequestDetails()));

		GetVoucherRequestRequest getVoucherRequestRequest = new GetVoucherRequestRequest();
		getVoucherRequestRequest.setVoucherRequestCode("REQ2200006");
		Assert.assertNotNull(JSON.toJSONString(voucherRequestService.getVoucherRequest(getVoucherRequestRequest)));
		getVoucherRequestRequest.setUserCode("123");
		getVoucherRequestRequest.setRoleList("213");

		Mockito.when(releaseApproveService.approveAble(Mockito.any())).thenReturn(Result.ok(1));

		Assert.assertNotNull(JSON.toJSONString(voucherRequestService.getVoucherRequest(getVoucherRequestRequest)));
		Assert.assertNotNull(JSON.toJSONString(voucherRequestService.getVoucherRequestByCode("REQ2200006")));

    }

    @Test
    public void cancelVoucherRequest() {
        Assert.assertNotNull(voucherRequestService.cancelVoucherRequest(new CancelVoucherRequestRequest("lisa", "REQ2200006")));
    }

    @Test
    public void bulkGetVoucherRequests() {
        List<GetVoucherRequestRequest> getVoucherRequestRequestList = new ArrayList<>();
        Assert.assertNotNull(voucherRequestService.bulkGetVoucherRequests(getVoucherRequestRequestList));
    }

    @Test
    public void bulkApproveVoucherRequest() {
        BulkApproveVoucherRequestRequest bulkApproveVoucherRequestRequest = new BulkApproveVoucherRequestRequest();
        List<VoucherRequest> voucherRequests = new ArrayList<>();
        Mockito.when(voucherRequestMapper.queryByVoucherRequestCodeList(Mockito.any())).thenReturn(voucherRequests);
        Assert.assertNotNull(voucherRequestService.bulkApproveVoucherRequest(bulkApproveVoucherRequestRequest));
    }
}
