
package com.gtech.gvcore.service;

import com.github.pagehelper.Page;
import com.gtech.basic.idm.dao.entity.MenuEntity;
import com.gtech.basic.idm.dao.mapper.IMenuMapper;
import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.gvcore.IgnoreCommonException;
import com.gtech.gvcore.common.request.useraccount.GvCreateUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvUpdateUserAccountRequest;
import com.gtech.gvcore.dao.mapper.UserAccountMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.impl.GvUserAccountServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class GvUserAccountServiceTest {

	@InjectMocks
	GvUserAccountServiceImpl userAccountService;
	@Mock
	private CompanyService companyService;

	@Mock
	private MerchantService merchantService;

	@Mock
	private OutletService outletService;
	@Mock
	IMenuMapper menuMapper;
	@Mock
	UserAccountMapper userAccountMapper;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(UserAccount.class, new MapperHelper().getConfig());
		EntityHelper.initEntityNameMap(MenuEntity.class, new MapperHelper().getConfig());
	}

	@Test
	public void createUserAccountTest() {

		GvCreateUserAccountRequest param = new GvCreateUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		param.setAccount("zjxtest001");
		param.setPassword("123456");
		param.setUserType("2");
		param.setMobileCountry("123");
		userAccountService.createUserAccount(param);

		DuplicateKeyException e1 = new DuplicateKeyException("uidx_third_id");
		DuplicateKeyException e2 = new DuplicateKeyException("aaaa");
		Mockito.when(userAccountMapper.insertSelective(Mockito.any())).thenThrow(e1, e2);
		IgnoreCommonException.execute((x) -> userAccountService.createUserAccount(param));
		IgnoreCommonException.execute((x) -> userAccountService.createUserAccount(param));
		Assert.assertTrue(true);

	}

	@Test
	public void queryPerrmissionCodeListTest() {

		userAccountService.queryPerrmissionCodeList("123");
		UserAccount userAccount = new UserAccount();
		userAccount.setExtendParams(
				"[{\"issuerCode\":\"issuerCode\",\"isLeaf\":false,\"dataPermissionList\":[{\"isLeaf\": false, \"code\": \"companyCode\", \"type\": 2}, {\"isLeaf\": false, \"code\": \"merchantCode\", \"type\": 3}, {\"isLeaf\": false, \"code\": \"outletCode\", \"type\": 4}]},{\"issuerCode\":\"issuerCode\",\"isLeaf\":true},{\"issuerCode\":\"issuerCode\",\"isLeaf\":false,\"dataPermissionList\":[{\"isLeaf\": true, \"code\": \"companyCode\", \"type\": 2}, {\"isLeaf\": true, \"code\": \"merchantCode\", \"type\": 3}, {\"isLeaf\": true, \"code\": \"outletCode\", \"type\": 4}]}]");
		Mockito.when(userAccountMapper.selectOne(Mockito.any())).thenReturn(userAccount);
		userAccountService.queryPerrmissionCodeList("123");

		Company companyResponse = new Company();
		companyResponse.setCompanyCode("companyCode");
		//companyResponse.setIssuerCode("issuerCode");
		Mockito.when(companyService.queryAllCompany()).thenReturn(Arrays.asList(companyResponse));
		Merchant merchantResponse = new Merchant();
		merchantResponse.setCompanyCode("companyCode");
		merchantResponse.setMerchantCode("merchantCode");
		Mockito.when(merchantService.queryAllMerchant()).thenReturn(Arrays.asList(merchantResponse));
		Outlet outletRespons = new Outlet();
		outletRespons.setMerchantCode("merchantCode");
		outletRespons.setOutletCode("outletCode");
		Mockito.when(outletService.queryAllOutLet()).thenReturn(Arrays.asList(outletRespons));
		userAccountService.queryPerrmissionCodeList("123");
		Assert.assertTrue(true);
	}

	@Test
	public void updateUserAccountTest() {

		GvUpdateUserAccountRequest param = new GvUpdateUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		param.setAccount("zjxtest001");
		param.setUserType("2");
		param.setUserCode("123");
		userAccountService.updateUserAccount(param);
		DuplicateKeyException e1 = new DuplicateKeyException("uidx_usercode");
		DuplicateKeyException e2 = new DuplicateKeyException("uidx_tenant_account");
		DuplicateKeyException e3 = new DuplicateKeyException("uidx_tenant_email");
		DuplicateKeyException e4 = new DuplicateKeyException("uidx_tenant_mobile");

		Mockito.when(userAccountMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenThrow(e1, e2, e4, e3);
		IgnoreCommonException.execute((x) -> userAccountService.updateUserAccount(param));
		IgnoreCommonException.execute((x) -> userAccountService.updateUserAccount(param));
		IgnoreCommonException.execute((x) -> userAccountService.updateUserAccount(param));
		IgnoreCommonException.execute((x) -> userAccountService.updateUserAccount(param));
		Assert.assertTrue(true);
	}

	@Test
	public void getUserAccountTest() {

		GetUserAccountParamDto param = new GetUserAccountParamDto();
		param.setTenantCode("default");
		param.setDomainCode("default");
		UserAccount userAccount = new UserAccount();
		userAccountService.getUserAccount(param);
		param.setUserCode("123");
		userAccountService.getUserAccount(param);
		param.setUserCode(null);
		param.setAccount("zjxtest001");
		userAccountService.getUserAccount(param);
		param.setAccount(null);
		param.setMobile("123");
		userAccountService.getUserAccount(param);
		param.setMobile(null);
		param.setEmail("23");

		Mockito.when(userAccountMapper.selectOne(Mockito.any())).thenReturn(userAccount);
		userAccountService.getUserAccount(param);
		Assert.assertTrue(true);
	}

	@Test
	public void queryUserAccountTest() {

		GvQueryUserAccountRequest param = new GvQueryUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		UserAccount userAccount = new UserAccount();
		Page<UserAccount> pageList = new Page<>();
		pageList.add(userAccount);
		Mockito.when(userAccountMapper.queryUserAccountList(Mockito.any(), Mockito.any())).thenReturn(pageList);
		userAccountService.queryUserAccountList(param);
		param.setPageNum(-1);
		param.setPageSize(-1);
		userAccountService.queryUserAccountList(param);
		Assert.assertTrue(true);
	}

	@Test
	public void queryUserAccountByRoleCodeTest() {
		userAccountService.queryUserAccountByRoles(null);
		userAccountService.queryUserAccountByRoles(Arrays.asList("12"));
		GvQueryUserAccountRequest param = new GvQueryUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		UserAccount userAccount = new UserAccount();
		Page<UserAccount> pageList = new Page<>();
		pageList.add(userAccount);
		Mockito.when(userAccountMapper.queryUserAccountList(Mockito.any(), Mockito.any())).thenReturn(pageList);
		userAccountService.queryUserAccountByRoles(Arrays.asList("12"));

		userAccountService.queryUserByRolesAndDataPermissions(Arrays.asList("12"), null);
		userAccountService.queryUserByRolesAndDataPermissions(Arrays.asList("12"), "12");
		Assert.assertTrue(true);
	}

	@Test
	public void getMenuCodeListByRolesTest() {

		MenuEntity menuEntity = new MenuEntity();
		menuEntity.setMenuCode("111");
		Mockito.when(menuMapper.queryResourceMenuList(Mockito.any())).thenReturn(Arrays.asList(menuEntity));
		userAccountService.getMenuCodeListByRoles(Arrays.asList("123"));
		Assert.assertTrue(true);
	}
	

	@Test
	public void getUserEmailTest() {

		UserAccount userAccount = new UserAccount();
		Mockito.when(userAccountMapper.selectOne(Mockito.any())).thenReturn(userAccount);
		userAccountService.getUserEmail("123");
		Assert.assertTrue(true);
	}
	
	@Test
	public void queryFullNameByCodeListTest() {

		UserAccount userAccount = new UserAccount();
		userAccount.setUserCode("111");
        userAccount.setFullName("fullName123");
		Mockito.when(userAccountMapper.selectByCondition(Mockito.any())).thenReturn(Arrays.asList(userAccount));
		userAccountService.queryFullNameByCodeList(Arrays.asList("123"));
		Assert.assertTrue(true);
	}
}
