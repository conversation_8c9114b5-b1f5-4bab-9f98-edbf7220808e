package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.customerproductcategory.*;
import com.gtech.gvcore.dao.mapper.CustomerProductCategoryMapper;
import com.gtech.gvcore.dao.model.CustomerProductCategory;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.CustomerProductCategoryServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 18:19
 */
@RunWith(MockitoJUnitRunner.class)
public class CustomerProductCategoryServiceTest {

    @InjectMocks
    private CustomerProductCategoryServiceImpl customerProductCategoryService;
    
    @Mock
    private CustomerProductCategoryMapper customerProductCategoryMapper;
    
    @Mock
    private GvCodeHelper codeHelper;
    
    
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(CustomerProductCategory.class, new MapperHelper().getConfig());
    }



    @Test
    public void createCustomerProductCategory(){
        CreateCustomerProductCategoryRequest request = new CreateCustomerProductCategoryRequest();
        request.setCreateUser("user");

        request.setCreateUser("123");
        customerProductCategoryService.createCustomerProductCategory(request);
    }

    @Test
    public void updateCustomerProductCategory(){
        UpdateCustomerProductCategoryRequest request = new UpdateCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("code");
        request.setStatus(0);
        request.setUpdateUser("123");

        customerProductCategoryService.updateCustomerProductCategory(request);
    }


    @Test
    public void deleteCustomerProductCategory(){
        DeleteCustomerProductCategoryRequest request = new DeleteCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("123");

        customerProductCategoryService.deleteCustomerProductCategory(request);
    }


    @Test
    public void queryCustomerProductCategoryList(){

        QueryCustomerProductCategoryRequest request = new QueryCustomerProductCategoryRequest();
        ArrayList<CustomerProductCategory> objects = new ArrayList<>();
        List<CustomerProductCategory> gvCustomerProductCategoryEntities = new ArrayList<>();
        CustomerProductCategory CustomerProductCategory = new CustomerProductCategory();
        CustomerProductCategory.setId(0L);
        CustomerProductCategory.setCustomerProductCategoryCode("123");
        CustomerProductCategory.setStatus(0);
        CustomerProductCategory.setCreateUser("123");
        CustomerProductCategory.setCreateTime(new Date());
        CustomerProductCategory.setUpdateUser("123");
        CustomerProductCategory.setUpdateTime(new Date());
        gvCustomerProductCategoryEntities.add(CustomerProductCategory);



        customerProductCategoryService.queryCustomerProductCategoryList(request);

    }



    @Test
    public void getCustomerProductCategory(){

        GetCustomerProductCategoryRequest request = new GetCustomerProductCategoryRequest();
        request.setCustomerProductCategoryCode("123");

        customerProductCategoryService.getCustomerProductCategory(request);

    }
    
    
}
