package com.gtech.gvcore.external;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.request.customerorder.SapSalesPostingXmlRequest;
import com.gtech.gvcore.common.request.postingxml.QueryPostingxmlRequest;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.dto.VoucherReceiveDto;
import com.gtech.gvcore.dao.mapper.CancelVoucherReceiveMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.MeansOfPayment;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.MeansOfPaymentService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.VoucherReceiveRecordService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class PostingXmlServiceTest {

	@InjectMocks
	PostingXmlService postingXmlService;
	@Mock
	private VoucherReceiveRecordService voucherReceiveRecordService;
    @Mock
    private CancelVoucherReceiveMapper cancelVoucherReceiveMapper;
    @Mock
    private OutletService outletService;

    @Mock
    private CustomerOrderService customerOrderService;

    @Mock
    private MeansOfPaymentService meansOfPaymentService;

	@Test
	public void generatePostingTest() {
		ReflectionTestUtils.setField(postingXmlService, "sftpUrl", "ftp://47.88.236.77:21211/upload/Test");
		ReflectionTestUtils.setField(postingXmlService, "name", "titan_sta_map");
		ReflectionTestUtils.setField(postingXmlService, "pwd", "123titan_sta_map1");
		postingXmlService.generatePosting(new QueryPostingxmlRequest());
		List<VoucherReceiveDto> receiveList = JSON.parseArray(
				"[{\"articleCode\":\"11\",\"cpgCode\":\"8b4ccddc58404379bc54bee502c2a37e\",\"createTime\":1654071827000,\"denomination\":1000000.00,\"issuerCode\":\"IS102204161424001275\",\"purchaseOrderNo\":\"1111\",\"receivedNum\":50,\"sourceDataCode\":\"QA_Test-209\",\"sourceType\":\"generate\",\"voucherEndNo\":\"1004224220019250\",\"voucherReceiveCode\":\"REC2200555\",\"voucherStartNo\":\"1004224220019201\"}]",
				VoucherReceiveDto.class);
		Mockito.when(voucherReceiveRecordService.queryReceiveRecord(Mockito.any())).thenReturn(receiveList);
		postingXmlService.generatePosting(new QueryPostingxmlRequest());
	}

    @Test
    public void customerOrderXml() {

        SapSalesPostingXmlRequest request = new SapSalesPostingXmlRequest();
        request.setOutletCode("testOutletCode");
        request.setQueryDate(new Date());

        postingXmlService.customerOrderXml(request);

        Outlet outlet = new Outlet();
        //outlet.setIssuerCode("issuerCode");
        outlet.setOutletCode(request.getOutletCode());
        outlet.setOutletName("testoutletName");
        Mockito.when(outletService.queryByOutletCode(Mockito.any(String.class))).thenReturn(outlet);
        postingXmlService.customerOrderXml(request);

        List<SumCustomerOrderGroupByArticle> sumList = new ArrayList<>();
        SumCustomerOrderGroupByArticle article1 = new SumCustomerOrderGroupByArticle();
        article1.setArticleCode("articleCode1");
        article1.setAmount(new BigDecimal("80"));
        article1.setDiscount(new BigDecimal("20"));
        article1.setVoucherAmount(new BigDecimal("100"));
        article1.setVoucherNum(10);
        SumCustomerOrderGroupByArticle article2 = new SumCustomerOrderGroupByArticle();
        article2.setArticleCode("articleCode2");
        article2.setAmount(new BigDecimal("200"));
        article2.setDiscount(new BigDecimal("0"));
        article2.setVoucherAmount(new BigDecimal("200"));
        article2.setVoucherNum(100);
        sumList.add(article1);
        sumList.add(article2);
        Mockito.when(customerOrderService.sumCustomerOrderGroupByArticle(Mockito.any(CustomerOrderDto.class)))
                .thenReturn(sumList);

        List<SumCustomerOrderGroupByArticle> sumCancelList = new ArrayList<>();
        SumCustomerOrderGroupByArticle article3 = new SumCustomerOrderGroupByArticle();
        article3.setArticleCode(article1.getArticleCode());
        article3.setAmount(new BigDecimal("8"));
        article3.setDiscount(new BigDecimal("2"));
        article3.setVoucherAmount(new BigDecimal("10"));
        article3.setVoucherNum(1);
        sumCancelList.add(article3);
        Mockito.when(customerOrderService.sumCancelCustomerOrderGroupByArticle(Mockito.any(CustomerOrderDto.class)))
                .thenReturn(sumCancelList);

        List<CustomerOrder> sumOrderList = new ArrayList<>();
        CustomerOrder order1 = new CustomerOrder();
        order1.setMeansOfPaymentCode("meansOfPaymentCode1");
        order1.setAmount(new BigDecimal("100000"));
        CustomerOrder order2 = new CustomerOrder();
        order2.setMeansOfPaymentCode("meansOfPaymentCode2");
        order2.setAmount(new BigDecimal("900000"));
        sumOrderList.add(order1);
        sumOrderList.add(order2);
        Mockito.when(customerOrderService.sumGroupByMeansOfPaymentCode(Mockito.any(CustomerOrderDto.class)))
                .thenReturn(sumOrderList);

        Map<String, MeansOfPayment> mopMap = new HashMap<>();
        MeansOfPayment mop = new MeansOfPayment();
        mop.setMeansOfPaymentCode(order1.getMeansOfPaymentCode());
        mop.setMopName("mopName-test pay");
        mopMap.put(mop.getMeansOfPaymentCode(), mop);
        Mockito.when(meansOfPaymentService.queryByCodeList(Mockito.any(List.class))).thenReturn(mopMap);
        postingXmlService.customerOrderXml(request);

        Assert.assertTrue(true);
    }

}
