package com.gtech.gvcore;

import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 遇到common异常继续执行
 */
public class IgnoreCommonException {

    public static void execute(Function<Object, Object> bi) {
        try {
			bi.apply(null);
		} catch (Exception e) {
        }
    }

	public static void executeV2(Consumer<Object> bi) {
		try {
			bi.accept(null);
		} catch (Exception e) {
		}
	}
}
