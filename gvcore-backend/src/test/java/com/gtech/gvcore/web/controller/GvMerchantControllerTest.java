package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.merchant.*;
import com.gtech.gvcore.service.MerchantService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvMerchantControllerTest {



    @InjectMocks
    private GvMerchantController gvMerchantController;

    @Mock
    private MerchantService outletService;


    @Test
    public void createMerchant(){

        CreateMerchantRequest request = new CreateMerchantRequest();
        request.setMerchantName("Erwin Cummings");
        request.setCreateUser("1");
        request.setCompanyCode("123123");
        gvMerchantController.createMerchant(request);

    }

    @Test
    public void updateMerchant(){

        UpdateMerchantRequest request = new UpdateMerchantRequest();
        request.setMerchantCode("2");
        request.setMerchantName("1");
        request.setMerchantCode("2");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvMerchantController.updateMerchant(request);

    }

    @Test
    public void updateMerchantStatus(){

        UpdateMerchantStatusRequest request = new UpdateMerchantStatusRequest();
        request.setMerchantCode("2");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvMerchantController.updateMerchantStatus(request);

    }


    @Test
    public void deleteMerchant(){
        DeleteMerchantRequest request = new DeleteMerchantRequest();
        request.setMerchantCode("1");
        gvMerchantController.deleteMerchant(request);
    }


    @Test
    public void queryMerchantList(){
        QueryMerchantRequest request = new QueryMerchantRequest();
        request.setMerchantCode("");
        request.setMerchantName("");
        request.setMerchantCode("");
        request.setPageSize(0);
        request.setPageNum(0);

        gvMerchantController.queryMerchantList(request);


    }


    @Test
    public void getMerchant(){

        GetMerchantRequest request = new GetMerchantRequest();
        request.setMerchantCode("1");

        gvMerchantController.getMerchant(request);

    }






}
