package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.vouchertype.*;
import com.gtech.gvcore.service.VoucherTypeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvVoucherTypeControllerTest {



    @InjectMocks
    private GvVoucherTypeController gvVoucherTypeController;

    @Mock
    private VoucherTypeService outletService;


    @Test
    public void createVoucherType(){

        CreateVoucherTypeRequest request = new CreateVoucherTypeRequest();
        request.setState(0);
        request.setDdValue("test");
        request.setDdText("test");
        request.setCreateUser("test");

        request.setCreateUser("1");
        gvVoucherTypeController.createVoucherType(request);

    }

    @Test
    public void updateVoucherType(){

        UpdateVoucherTypeRequest request = new UpdateVoucherTypeRequest();
        request.setState(0);
        request.setDdValue("test");
        request.setDdText("test");
        request.setUpdateUser("test");

        request.setUpdateUser("1");
        gvVoucherTypeController.updateVoucherType(request);

    }


    @Test
    public void deleteVoucherType(){
        DeleteVoucherTypeRequest request = new DeleteVoucherTypeRequest();
        request.setDdValue("test");

        gvVoucherTypeController.deleteVoucherType(request);
    }


    @Test
    public void queryVoucherTypeList(){
        QueryVoucherTypeRequest request = new QueryVoucherTypeRequest();
        request.setDdValue("test");
        request.setDdText("test");


        gvVoucherTypeController.queryVoucherTypeList(request);


    }


    @Test
    public void getVoucherType(){

        GetVoucherTypeRequest request = new GetVoucherTypeRequest();
        request.setDdValue("test");


        gvVoucherTypeController.getVoucherType(request);

    }






}
