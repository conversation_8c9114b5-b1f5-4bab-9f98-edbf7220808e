package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.distribution.CreateDistributionEmailTemplateRequest;
import com.gtech.gvcore.service.distribution.DistributionEmailTemplateService;
import com.gtech.gvcore.web.controller.distribution.DistributionEmailTemplateController;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @ClassName GvDistributionEmailTemplateControllerTest
 * @Description GvDistributionEmailTemplateControllerTest
 * <AUTHOR>
 * @Date 2022/7/5 18:22
 * @Version V1.0
 **/
@RunWith(MockitoJUnitRunner.class)
public class GvDistributionEmailTemplateControllerTest {

    @InjectMocks
    private DistributionEmailTemplateController controller;

    @Mock
    private DistributionEmailTemplateService distributionEmailTemplateService;

    @Test
    public void createDistributionEmailTemplate() {
        Mockito.when(this.distributionEmailTemplateService.createEmailTemplate(Mockito.any())).thenReturn("");
        final CreateDistributionEmailTemplateRequest request = new CreateDistributionEmailTemplateRequest();
        Assert.assertNotNull(controller.createEmailTemplate(request));
    }

}