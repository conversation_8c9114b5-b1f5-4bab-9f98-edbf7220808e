
package com.gtech.gvcore.web.controller;

import java.util.Arrays;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.basic.idm.service.PageService;
import com.gtech.basic.idm.service.UserRoleMappingService;
import com.gtech.basic.idm.service.dto.UserRoleDto;
import com.gtech.basic.idm.web.helper.CodeHelper;
import com.gtech.basic.idm.web.vo.param.GetUserAccountParam;
import com.gtech.commons.page.PageData;
import com.gtech.gvcore.common.request.flow.GetFlowNoticeRequest;
import com.gtech.gvcore.common.request.releaseapprove.QueryApproveNodeRequest;
import com.gtech.gvcore.common.request.useraccount.GetUserPermissionCodeRequest;
import com.gtech.gvcore.common.request.useraccount.GvCreateUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvGetPageListRequest;
import com.gtech.gvcore.common.request.useraccount.GvQueryUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.GvUpdateUserAccountRequest;
import com.gtech.gvcore.common.request.useraccount.IssuerPermissionRequest;
import com.gtech.gvcore.common.response.useraccount.UserAccountResponse;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.ReleaseApproveService;
import com.gtech.gvcore.service.VoucherRequestService;
import com.gtech.gvcore.web.IgnoreCommonException;

@RunWith(MockitoJUnitRunner.class)
public class GvUserAccountControllerTest {

	@InjectMocks
	GvUserAccountController userAccountController;

	@Mock
	CodeHelper codeHelper;

	@Mock
	GvUserAccountService userAccountService;

	@Mock
	UserRoleMappingService userRoleMappingService;
	@Mock
	PageService pageService;

	@Mock
	private FlowNoticeService flowNoticeService;

	@Mock
	private ReleaseApproveService releaseApproveService;

	@Mock
	private CustomerOrderService customerOrderService;
	@Mock
	private VoucherRequestService voucherRequestService;

	@Test
	public void createUserAccountTest() {

		GvCreateUserAccountRequest param = new GvCreateUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		param.setAccount("zjxtest001");
		param.setPassword("123456");
		param.setUserType("2");
		userAccountController.createGvUserAccount(param);
		param.setRoleCodeList(Arrays.asList("123"));
		param.setUserCode("123");
		IssuerPermissionRequest issuerPermissionRequest = new IssuerPermissionRequest();
		param.setIssuerPermissionList(Arrays.asList(issuerPermissionRequest));
		userAccountController.createGvUserAccount(param);
		Assert.assertTrue(true);
	}

	@Test
	public void updateUserAccountTest() {

		GvUpdateUserAccountRequest param = new GvUpdateUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		param.setAccount("zjxtest001");
		param.setUserType("2");
		param.setUserCode("123");
		IgnoreCommonException.execute((x) -> userAccountController.updateGvUserAccount(param));
		Mockito.when(userAccountService.updateUserAccount(Mockito.any())).thenReturn(1);
		userAccountController.updateGvUserAccount(param);
		param.setRoleCodeList(Arrays.asList("123"));
		IssuerPermissionRequest issuerPermissionRequest = new IssuerPermissionRequest();
		param.setIssuerPermissionList(Arrays.asList(issuerPermissionRequest));
		userAccountController.updateGvUserAccount(param);
		Assert.assertTrue(true);
	}

	@Test
	public void getUserAccountTest() {

		GetUserAccountParam param = new GetUserAccountParam();
		param.setTenantCode("default");
		param.setDomainCode("default");
		param.setAccount("zjxtest001");
		param.setUserCode("123");
		IgnoreCommonException.execute((x) -> userAccountController.getGvUserAccount(param));
		UserAccountResponse response = new UserAccountResponse();
		Mockito.when(userAccountService.getUserAccount(Mockito.any())).thenReturn(response);
		userAccountController.getGvUserAccount(param);
		UserRoleDto userRoleDto = new UserRoleDto();
		Mockito.when(userRoleMappingService.queryUserRoles(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(userRoleDto));
		userAccountController.getGvUserAccount(param);
		GetUserPermissionCodeRequest request = new GetUserPermissionCodeRequest();
		userAccountController.getUserDataPermissionCode(request);
		Assert.assertTrue(true);
	}

	@Test
	public void queryUserAccountTest() {

		GvQueryUserAccountRequest param = new GvQueryUserAccountRequest();
		param.setTenantCode("default");
		param.setDomainCode("default");
		param.setUserCode("123");
		PageData<UserAccountResponse> data = new PageData<>();
		UserAccountResponse response = new UserAccountResponse();
		data.setList(Arrays.asList(response));
		Mockito.when(userAccountService.queryUserAccountList(Mockito.any())).thenReturn(data);
		userAccountController.queryGvUserAccountList(param);
		userAccountController.queryGvUserAccountList(param);
		UserRoleDto userRoleDto = new UserRoleDto();
		Mockito.when(userRoleMappingService.queryUserRoles(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(userRoleDto));
		userAccountController.queryGvUserAccountList(param);
		Assert.assertTrue(true);
	}
	
	@Test
	public void queryPageListByRoleTest() {
		GvGetPageListRequest param = new GvGetPageListRequest();
		userAccountController.queryPageListByRole(param);
		Mockito.when(userAccountService.getMenuCodeListByRoles(Mockito.any())).thenReturn(Arrays.asList("001"));
		userAccountController.queryPageListByRole(param);
		Assert.assertTrue(true);
	}

	@Test
	public void queryUserByFlowNoticeTest() {
		GetFlowNoticeRequest param = new GetFlowNoticeRequest();
		userAccountController.queryUserByFlowNotice(param);
		Assert.assertTrue(true);
	}

	@Test
	public void queryUserByApproveConfigTest() {
		QueryApproveNodeRequest param = new QueryApproveNodeRequest();
		userAccountController.queryUserByApproveConfig(param);
		Assert.assertTrue(true);
	}
}
