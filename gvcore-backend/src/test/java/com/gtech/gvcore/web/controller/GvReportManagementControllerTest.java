package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.reportmanagement.CreateReportManagementListRequest;
import com.gtech.gvcore.common.request.reportmanagement.CreateReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.DelReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.QueryReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.UpdateReportManagementRequest;
import com.gtech.gvcore.service.impl.ReportManagementServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class GvReportManagementControllerTest {

    @InjectMocks
    private GvReportManagementController controller;

    @Mock
    private ReportManagementServiceImpl reportManagementService;


    @Test
    public void createReportManagement() {
        Mockito.when(reportManagementService.createReportManagements(Mockito.any())).thenReturn(new Result<>());
        CreateReportManagementListRequest createReportManagementListRequest = new CreateReportManagementListRequest();
        createReportManagementListRequest.setCreateReportManagementRequests(new ArrayList<>(Arrays.asList(new CreateReportManagementRequest(), new CreateReportManagementRequest())));
        Assert.assertNotNull(controller.createReportManagement(createReportManagementListRequest));
    }

    @Test
    public void updatePermissions() {
        Mockito.when(reportManagementService.updatePermissions(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller.updatePermissions(new UpdateReportManagementRequest()));
    }

    @Test
    public void delReportManagement() {
        Mockito.when(reportManagementService.delReportManagement(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller.delReportManagement(new DelReportManagementRequest()));
    }

    @Test
    public void queryReportManagements() {
        Mockito.when(reportManagementService.queryReportManagements(Mockito.any())).thenReturn(new PageResult<>());
        Assert.assertNotNull(controller.queryReportManagements(new QueryReportManagementRequest()));
    }
}