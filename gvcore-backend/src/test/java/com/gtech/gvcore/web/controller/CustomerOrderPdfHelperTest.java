package com.gtech.gvcore.web.controller;

import com.gtech.basic.masterdata.core.utils.RandomUtil;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.GvcoreBackendApplication;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.dao.mapper.ArticleMopMapper;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.CustomerMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.CustomerOrderReceiver;
import com.gtech.gvcore.helper.CustomerOrderPdfHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;

/**
 * @ClassName CustomerOrderPdfHelperTest
 * @Description
 * <AUTHOR>
 * @Date 2023/1/29 20:05
 * @Version V1.0
 **/
@Slf4j
@Transactional
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {GvcoreBackendApplication.class})
public class CustomerOrderPdfHelperTest {

    @Autowired private CustomerOrderPdfHelper customerOrderPdfHelper;
    @Autowired private CpgMapper cpgMapper;
    @Autowired private ArticleMopMapper articleMopMapper;
    @Autowired private CustomerMapper customerMapper;

    @Test
    public void test01() {

        final String articleMopCode = "TEST_ARTICLE_MOP" + RandomUtil.randomNumber(8);
        final String cpgCode = "TEST_CPG" + RandomUtil.randomNumber(8);
        final String customerCode = "TEST_CUSTOMER" + RandomUtil.randomNumber(8);

        articleMopMapper.insertSelective(new ArticleMop()
                .setArticleMopCode(articleMopCode)
                .setArticleCodeName("ACN" + RandomUtil.randomNumber(8))
                .setArticleCode("test_" + RandomUtil.randomNumber(8))
                .setMopCode(GvcoreConstants.MOP_CODE_VCE)
                .setStatus(1)
                .setCreateUser("test_" + RandomUtil.randomNumber(8))
                .setUpdateUser("test_" + RandomUtil.randomNumber(8))
        );
        cpgMapper.insertSelective(new Cpg()
                .setCpgCode(cpgCode)
                .setCpgName("test_" + RandomUtil.randomNumber(8))
                .setIssuerCode("test_" + RandomUtil.randomNumber(8))
                .setCpgTypeCode("test_" + RandomUtil.randomNumber(8))
                .setGracePeriods(8888)
                .setEffectiveYears(Integer.parseInt(DateUtil.format(DateUtil.now(), "yyyy")))
                .setEffectiveMonth(Integer.parseInt(DateUtil.format(DateUtil.now(), "mm")))
                .setEffectiveDay(Integer.parseInt(DateUtil.format(DateUtil.now(), "dd")))
                .setEffectiveHour(Integer.parseInt(DateUtil.format(DateUtil.now(), "hh")))
                .setCurrencyCode("test_" + RandomUtil.randomNumber(8))
                .setDenomination(new BigDecimal("10000"))
                .setArticleMopCode(articleMopCode)
                .setStatus(1)
                .setCreateUser("test_" + RandomUtil.randomNumber(8))
                .setUpdateUser("test_" + RandomUtil.randomNumber(8))
        );

        customerMapper.insertSelective(new Customer()
                .setCustomerCode(customerCode)
                //.setIssuerCode("test_" + RandomUtil.randomNumber(8))
                .setOutletCode("test_" + RandomUtil.randomNumber(8))
                .setCustomerName("test_" + RandomUtil.randomNumber(8))
                .setCustomerType("test_" + RandomUtil.randomNumber(8))
                .setCompanyName("test_" + RandomUtil.randomNumber(8))
                .setContactFirstName("test_" + RandomUtil.randomNumber(8))
                .setContactLastName("test_" + RandomUtil.randomNumber(8))
                .setContactDivision("pdfDivision_" + "test_" + RandomUtil.randomNumber(8))
                .setContactPhone("test_" + RandomUtil.randomNumber(8))
                .setContactEmail("test_" + RandomUtil.randomNumber(8))
                .setShippingAddress1("test_" + RandomUtil.randomNumber(8))
                .setTransferAccount("test_" + RandomUtil.randomNumber(8))
                .setBankCardIssuer("test_" + RandomUtil.randomNumber(8))
                .setNote("test_" + RandomUtil.randomNumber(8))
                .setStatus(1)
                .setPph(1)
                .setBeneficiaryName("test_" + RandomUtil.randomNumber(8))
                .setBankName("test_" + RandomUtil.randomNumber(8))
                .setBranchName("test_" + RandomUtil.randomNumber(8))
                .setAccountNumber("test_" + RandomUtil.randomNumber(8))
                .setCreateUser("test_" + RandomUtil.randomNumber(8))
                .setUpdateUser("test_" + RandomUtil.randomNumber(8))
        );

        log.info("proformaInvoice ==> {}" , customerOrderPdfHelper.proformaInvoice(
                new CustomerOrder()
                        .setDiscount(new BigDecimal(50))
                        .setAmount(new BigDecimal(100))
                        .setCustomerName("customerName" + RandomUtil.randomNumber(8))
                        .setCustomerCode(customerCode)
                , Collections.singletonList(new CustomerOrderDetails()
                        .setCpgCode(cpgCode)
                        .setDenomination(new BigDecimal(10_000_0))
                        .setVoucherNum(200))
                , new CustomerOrderReceiver().setShippingAddress("shanghai")));

        log.info("salesOrder ==> {}" , customerOrderPdfHelper.salesOrder(
                new CustomerOrder()
                        .setDiscount(new BigDecimal(50))
                        .setAmount(new BigDecimal(100))
                        .setCustomerName("customerName" + RandomUtil.randomNumber(8))
                        .setCustomerCode(customerCode)
                , Collections.singletonList(new CustomerOrderDetails()
                        .setCpgCode(cpgCode)
                        .setDenomination(new BigDecimal(10_000_0))
                        .setVoucherNum(100))));

        log.info("quotation ==> {}" , customerOrderPdfHelper.quotation(
                new CustomerOrder()
                        .setDiscount(new BigDecimal(50))
                        .setAmount(new BigDecimal(100))
                        .setCustomerName("customerName" + RandomUtil.randomNumber(8))
                        .setCustomerCode(customerCode)
                , Collections.singletonList(new CustomerOrderDetails()
                        .setCpgCode(cpgCode)
                        .setDenomination(new BigDecimal(10_000_0))
                        .setVoucherNum(100))
                , new CustomerOrderReceiver().setShippingAddress("shanghai")));


    }



}
