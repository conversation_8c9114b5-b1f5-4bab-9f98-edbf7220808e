package com.gtech.gvcore.web.controller;

import com.google.common.collect.Lists;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.outlet.CreateOutletRequest;
import com.gtech.gvcore.common.request.outlet.DeleteOutletRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletByBusinessRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletRequest;
import com.gtech.gvcore.common.request.outlet.UpdateOutletRequest;
import com.gtech.gvcore.common.request.outlet.UpdateOutletStatusRequest;
import com.gtech.gvcore.service.OutletService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvOutletControllerTest {


    @InjectMocks
    private GvOutletController gvOutletController;

    @Mock
    private OutletService outletService;


    @Test
    public void createOutlet() {

        CreateOutletRequest request = new CreateOutletRequest();
        request.setOutletName("Erwin Cummings");
        request.setMerchantCode("2992811627607");
        request.setCpgCode(Lists.newArrayList("B000A70B9I"));
        request.setBusinessOutletCode("B000I6QSBG");
        request.setOutletType("1");
        request.setStateCode("B000AOMPAY");
        request.setCityCode("B000A15Y0K");
        request.setDistrictCode("B000CR7CP2");
        request.setAddress1("1");
        request.setAddress2("1");
        request.setPinCode("1");
        request.setFirstName("1");
        request.setLastName("1");
        request.setEmail("<EMAIL>");
        request.setPhone("***********");
        request.setMobile("1");
        request.setAlertnateEmail("<EMAIL>");
        request.setAlertnatePhone("***********");
        request.setDescriptive("1");
        request.setStatus(0);
        request.setCreateUser("1");
        gvOutletController.createOutlet(request);

    }

    @Test
    public void updateOutlet() {

        UpdateOutletRequest request = new UpdateOutletRequest();
        request.setOutletCode("2");
        request.setOutletName("1");
        request.setMerchantCode("2");
        request.setDistrictCode("1");
        request.setAddress1("1");
        request.setAddress2("1");
        request.setOutletType("1");
        request.setStateCode("1");
        request.setCityCode("1");
        request.setCpgCode(Lists.newArrayList("1"));
        request.setCpgCode(Lists.newArrayList("B000A70B9I"));
        request.setBusinessOutletCode("1");
        request.setPinCode("1");
        request.setEmail("<EMAIL>");
        request.setPhone("***********");
        request.setMobile("***********");
        request.setFirstName("1");
        request.setLastName("1");
        request.setAlertnateEmail("<EMAIL>");
        request.setDescriptive("1");
        request.setStatus(0);
        request.setAlertnatePhone("***********");
        request.setUpdateUser("1");
        gvOutletController.updateOutlet(request);

    }


    @Test
    public void updateOutletStatus() {

        UpdateOutletStatusRequest request = new UpdateOutletStatusRequest();
        request.setOutletCode("2");
        request.setStatus(0);
        request.setUpdateUser("1");
        gvOutletController.updateOutletStatus(request);

    }


    @Test
    public void deleteOutlet() {
        DeleteOutletRequest request = new DeleteOutletRequest();
        request.setOutletCode("1");
        gvOutletController.deleteOutlet(request);
    }


    @Test
    public void queryOutletList() {
        QueryOutletRequest request = new QueryOutletRequest();
        request.setOutletCode("");
        request.setOutletName("");
        request.setMerchantCodeList(Lists.newArrayList());
        request.setStatus(0);
        request.setPageSize(0);
        request.setPageNum(0);

        gvOutletController.queryOutletList(request);


    }


    @Test
    public void getOutlet() {

        GetOutletRequest request = new GetOutletRequest();
        request.setOutletCode("1");

        gvOutletController.getOutlet(request);

    }


    @Test
    public void queryOutletByBusinessType() {
        Mockito.when(outletService.queryOutletByBusinessType(new QueryOutletByBusinessRequest())).thenReturn(new Result<>());
        Assert.assertNotNull(gvOutletController.queryOutletByBusinessType(new QueryOutletByBusinessRequest()));
    }
}
