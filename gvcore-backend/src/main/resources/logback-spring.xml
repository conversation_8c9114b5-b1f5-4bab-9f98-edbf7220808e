<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <appender name="gvcore-reportlog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}_%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

	<logger name="gvcore-report_error" level="INFO">
        <appender-ref ref="gvcore-reportlog"/>
    </logger>

    <logger name="gvcore-report" level="INFO">
        <appender-ref ref="gvcore-reportlog"/>
    </logger>

    <logger name="gvcore-report_ts" level="INFO">
        <appender-ref ref="gvcore-reportlog"/>
    </logger>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="gvcore-reportlog"/>
    </root>
</configuration>