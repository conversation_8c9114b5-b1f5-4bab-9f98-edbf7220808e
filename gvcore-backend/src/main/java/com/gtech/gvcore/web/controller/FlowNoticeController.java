package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.flow.DeleteFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.FlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.GetFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.response.flow.FlowNoticeResponse;
import com.gtech.gvcore.service.FlowNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping(value = "/gv/flowNotice")
@Api(value = "Flow notice.", tags = { "GV Flow notice Api" })
public class FlowNoticeController {

	@Autowired
	private FlowNoticeService flowNoticeService;

	@ApiOperation(value = "Save flow notice ", notes = "Create flow notice.")
	@PostMapping(value = "/saveFlowNotice")
	public Result<String> saveFlowNotice(@RequestBody @Validated FlowNoticeRequest request) {

		return Result.ok(flowNoticeService.saveFlowNotice(request));
	}

	@ApiOperation(value = "Delete flow notice", notes = "Delete flow notice.")
	@PostMapping(value = "/deleteFlowNotice")
	public Result<Void> deleteFlowNotice(@RequestBody @Validated DeleteFlowNoticeRequest request) {
		flowNoticeService.deleteFlowNotice(request);
		return Result.ok();
	}
	
	@ApiOperation(value = "Get flow notice", notes = "Get flow notice.")
	@PostMapping(value = "/getFlowNotice")
	public Result<FlowNoticeResponse> getFlowNotice(@RequestBody @Validated GetFlowNoticeRequest request) {
		return Result.ok(flowNoticeService.getFlowNotice(request));
	}
	
	
	@ApiOperation(value = "Send ", notes = "Save flow node.")
	@PostMapping(value = "/send")
	@ApiIgnore
	public Result<Void> send(@RequestBody @Validated SendNoticeRequest request) {
		flowNoticeService.send(request);
		return Result.ok();
	}
}
