package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.oss.GetUrlByAccKeyRequest;
import com.gtech.gvcore.helper.OssHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/6/30 13:35
 */
@RestController
@RequestMapping(value = "/gv/oss")
@Api(value = "OSS maintenance.", tags = { "GV OSS Api" })
public class OSSController {

    @Autowired
    private OssHelper ossHelper;


    @ApiOperation(value = "Create pos account",notes = "Create pos information.")
    @PostMapping(value = "/queryUrlByKey")
    public Result<String> queryUrlByKey(@RequestBody @Validated GetUrlByAccKeyRequest param) {
        // Return result object
        String url = ossHelper.grantAccessUrl(param.getKey()).toString();
        return Result.ok(url);
    }


}
