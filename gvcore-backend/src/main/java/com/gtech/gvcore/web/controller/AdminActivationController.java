package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.selfactivation.ResendActivationEmailRequest;
import com.gtech.gvcore.service.SelfActivationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Admin activation controller for customer service operations
 * Provides admin functions for managing self-activation tasks
 */
@Slf4j
@RestController
@RequestMapping(value = "/admin-activation")
@Api(value = "Admin Activation", tags = {"Admin Activation Api"})
public class AdminActivationController {
    
    @Autowired
    private SelfActivationService selfActivationService;
    
    /**
     * Resend activation email for customer service
     * Allows customer service to manually resend activation emails to customers
     */
    @ApiOperation(value = "Resend activation email", notes = "Admin function to resend activation email to customer")
    @PostMapping(value = "/resendEmail")
    public Result<String> resendActivationEmail(@RequestBody @Validated ResendActivationEmailRequest request) {
        log.info("Admin resending activation email for order: {}", request.getCustomerOrderCode());
        return selfActivationService.resendActivationEmail(request);
    }
}
