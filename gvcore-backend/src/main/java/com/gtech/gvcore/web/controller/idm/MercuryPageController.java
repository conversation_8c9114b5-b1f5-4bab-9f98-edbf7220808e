package com.gtech.gvcore.web.controller.idm;

import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.basic.idm.service.PageService;
import com.gtech.basic.idm.service.dto.PageDto;
import com.gtech.basic.idm.web.helper.CodeHelper;
import com.gtech.basic.idm.web.vo.param.CreatePageParam;
import com.gtech.basic.idm.web.vo.result.CreatePageResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "Page")
@RestController
@RequestMapping(value = "/page")
public class MercuryPageController {
    @Autowired
    private PageService pageService;
    @Autowired
    private CodeHelper codeHelper;


    @ApiOperation(
            value = "Create page",
            notes = "Create page information."
    )
    @PostMapping({"/createPage"})
    public Result<CreatePageResult> createPage(@RequestBody CreatePageParam param) {
        param.validate();
        PageDto createDto = (PageDto) BeanCopyUtils.jsonCopyBean(param, PageDto.class);
        if (StringUtil.isBlank(param.getPageCode())) {
            String pageCode = this.codeHelper.generatePageCode();
            createDto.setPageCode(pageCode);
        }
        //createDto.setResourceCode(this.codeHelper.generateResourceCode());
        try {
            this.pageService.create(createDto);
        } catch (DuplicateKeyException var4) {
            throw Exceptions.fail(ErrorCodes.PAGE_CODE_DUPLICATE, new Object[]{createDto.getMenuCode()});
        }
        return Result.ok(CreatePageResult.builder().pageCode(createDto.getPageCode()).build());
    }




}
