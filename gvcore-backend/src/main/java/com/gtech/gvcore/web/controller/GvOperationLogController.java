package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.operatelog.CreateOperationLogRequest;
import com.gtech.gvcore.common.request.operatelog.QueryOperationLogRequest;
import com.gtech.gvcore.common.response.operatelog.GvOperateLogByPageResponse;
import com.gtech.gvcore.service.GvOperateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:49
 **/
@Slf4j
@RestController
@RequestMapping(value = "/gv/operationLog")
@Api(value = "operationLog", tags = {"operationLog Api"})
public class GvOperationLogController {

    @Autowired
    private GvOperateLogService gvOperateLogService;

    @ApiOperation(value = "create operationLog", notes = "create operationLog")
    @PostMapping(value = "/createOperationLog")
    public Result<String> createOperationLog(@RequestBody @Validated CreateOperationLogRequest createCpgTypeRequest) {
        log.info("CreateOperationLogRequest:{}", JSON.toJSONString(createCpgTypeRequest));
        return gvOperateLogService.createOperationLog(createCpgTypeRequest);
    }

    @ApiOperation(value = "query operationLog", notes = "query operationLog")
    @PostMapping(value = "/queryOperationLog")
    public PageResult<GvOperateLogByPageResponse> queryOperationLog(@RequestBody @Validated QueryOperationLogRequest logRequest) {
        log.info("QueryOperationLogRequest:{}", JSON.toJSONString(logRequest));
        return gvOperateLogService.queryOperationLog(logRequest);
    }
}
