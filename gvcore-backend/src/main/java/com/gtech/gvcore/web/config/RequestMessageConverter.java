package com.gtech.gvcore.web.config;

import com.alibaba.fastjson.JSON;
import com.google.api.client.util.Lists;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/17 11:19
 */
public class RequestMessageConverter implements HttpMessageConverter {

    @Override
    public boolean canRead(Class clazz, MediaType mediaType) {
        System.out.println("canRead");
        return true;
    }

    @Override
    public boolean canWrite(Class clazz, MediaType mediaType) {
        System.out.println("canWrite" + mediaType.getType());
        return true;
    }

    @Override
    public List<MediaType> getSupportedMediaTypes() {
        System.out.println("getSupportedMediaTypes");
//        ArrayList<MediaType> list = Lists.newArrayList();
//        list.add(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE));
//        list.add(MediaType.parseMediaType(MediaType.MULTIPART_FORM_DATA_VALUE));
        return Lists.newArrayList();
    }

    @Override
    public Object read(Class clazz, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        System.out.println("read");
        InputStream body = inputMessage.getBody();
        String json = StreamUtils.copyToString(body, Charset.forName("UTF-8"));
        return JSON.parseObject(json, clazz);
    }

    @Override
    public void write(Object o, MediaType contentType, HttpOutputMessage outputMessage) throws IOException, HttpMessageNotWritableException {
        System.out.println("write");
    }
}