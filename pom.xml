<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.0</version>
    </parent>

    <groupId>com.gtech.gvcore</groupId>
    <artifactId>gvcore-pom</artifactId>
    <packaging>pom</packaging>
    <version>1.3.0</version>
    <name>gvcore-pom</name>

    <modules>
        <module>gvcore-web</module>
        <module>gvcore-service</module>
        <module>gvcore-common</module>
        <module>SQL</module>
        <module>gvcore-report</module>
        <module>gvcore-backend</module>
    </modules>

    <properties>
        <gvcore-version>1.3.0</gvcore-version>
        <shardingsphere-version>4.1.1</shardingsphere-version>
        <profile.active>dev</profile.active>
        <filecloud.version>1.7.6</filecloud.version>
    </properties>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
        </profile>
        <profile>
            <id>junit</id>
            <properties>
                <profile.active>junit</profile.active>
            </properties>
        </profile>
    </profiles>
    <dependencyManagement>
        <dependencies>


            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.3</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>

            <dependency>
                <groupId>com.gtech.gvcore</groupId>
                <artifactId>gvcore-web</artifactId>
                <version>${gvcore-version}</version>
            </dependency>

            <dependency>
                <groupId>com.gtech.gvcore</groupId>
                <artifactId>gvcore-service</artifactId>
                <version>${gvcore-version}</version>
            </dependency>

            <dependency>
                <groupId>com.gtech.gvcore</groupId>
                <artifactId>gvcore-common</artifactId>
                <version>${gvcore-version}</version>
            </dependency>

            <!-- 读写分离 for spring boot -->



            <!-- for spring namespace -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-namespace</artifactId>
                <version>${shardingsphere-version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
                <version>${shardingsphere-version}</version>
            </dependency>


            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.7.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.mybatis.spring.boot/mybatis-spring-boot-starter -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.gtech.basic</groupId>
                <artifactId>filecloud-oss-web</artifactId>
                <version>${filecloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gtech.basic</groupId>
                <artifactId>filecloud-import-web</artifactId>
                <version>${filecloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gtech.basic</groupId>
                <artifactId>filecloud-export-web</artifactId>
                <version>${filecloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gtech.basic</groupId>
                <artifactId>filecloud-api-core</artifactId>
                <version>${filecloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gtech.basic</groupId>
                <artifactId>filecloud-import-export-management</artifactId>
                <version>${filecloud.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.gtech.basic</groupId>
                        <artifactId>filecloud-api-feign</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>e-iceblue</groupId>
                <artifactId>spire.xls.free</artifactId>
                <version>5.1.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.gtech.basic</groupId>
            <artifactId>filecloud-oss-core</artifactId>
            <version>${filecloud.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>https://nexus.gtech.asia/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>https://nexus.gtech.asia/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
        </plugins>
    </build>

</project>
