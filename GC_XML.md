Gift Card XML文件生成需求

●文件生成路径：与原GV路径要区分开。
●文件是否与GV隔离：与原GV文件要区分开。
●MV01 - GCPOS，MV03 - MAPCLUB.com，MV04 - mapgiftvoucher.com
●文件生成规则：请看以下说明

文件类型	业务场景	文件数量	文件命名规则
WPUBON_C	统计MV store（MV01，MV03，MV04）的净销售额(去掉折扣金额后)；
每日生成	MV01：1个文件
MV03：1个文件
MV04：多个文件，最多3个文件；一个总的文件，其他一个MOPCode一个文件
WPUBON_C +MV store名称+年月日+MOPCode

WPUBON	统计MV store（MV01，MV03，MV04）的总销售额；每日生成；MOP维度汇总
MV01：多个文件，每个MOP（Means of Payment）一个文件
MV03：多个文件，每个MOP（Means of Payment）一个文件
MV04：多个文件，每个（Means of Payment）一个文件	WPUBON +MV store名称+年月日+MOP 名称

WPUUMS	统计MV store（MV01，MV03，MV04）的总销售额、销售数量、折扣金额；每日生成；article code维度汇总
MV01：1个文件，按照article code进行汇总
MV03：1个文件，按照article code进行汇总
MV04：1个文件，按照article code进行汇总	WPUUMS +MV store名称+年月日


1.WPUBON_C
 
<WPUBON01>---Default value
<IDOC BEGIN="1">---Default value
<EDI_DC40 SEGMENT="1">---Default value
<TABNAM>EDI_DC40</TABNAM>---Default value
<MANDT>999</MANDT>---Default value
<DOCREL>701</DOCREL>---Default value
<DIRECT>2</DIRECT>---Default value
<IDOCTYP>WPUBON01</IDOCTYP>---Default value
<MESTYP>WPUBON</MESTYP>---Default value
<SNDPOR>XML_PORT</SNDPOR>---Default value
<SNDPRT>KU</SNDPRT>---Default value
<SNDPRN>MV01</SNDPRN>---Parameterized, it can be MV01,MV03,MV04. In this case for MV01
<RCVPOR>SAPMGP</RCVPOR>---Default value
<RCVPRT>LS</RCVPRT>---Default value
<RCVPRN>MGPCLNT999</RCVPRN>---Default value
<CREDAT>20250220</CREDAT>---Parameterized, Creation date of this file. YearMonthDate YYYYMMDD
<CRETIM>094700</CRETIM>---Parameterized, Creation time of this file. Time HHMMSS
<ARCKEY>MV0120250220</ARCKEY>---Parameterized, {OutletCode}-{Date(YYYYMMDD)}
</EDI_DC40>---Default value
<E1WPB01 SEGMENT="1">---Default value
<VORGDATUM>20250217</VORGDATUM>---Parameterized, transaction date. YearMonthDate YYYYMMDD
<BONNUMMER>POS CLEARING</BONNUMMER>---Default value
<BELEGWAERS>IDR</BELEGWAERS>—-Default value, Currency
<E1WPB02 SEGMENT="1">---Default value
<VORGANGART>ZMOP</VORGANGART>---Default value
<QUALARTNR>ARTN</QUALARTNR>---Default value
<ARTNR>POS_CLEARING</ARTNR>---Default value
</E1WPB02>---Default value
<E1WPB06 SEGMENT="1">---Default value
<ZAHLART>ZPOS</ZAHLART>---Default value
<SUMME>25000000.00</SUMME>---Parameterized, Total nett sales amount of Gift Card at the store. In this case for MV01
</E1WPB06>---Default value
</E1WPB01>---Default value
</IDOC>---Default value
</WPUBON01>---Default value
 
2.WPUBON
 
<WPUBON01>---Default value
<IDOC BEGIN="1">---Default value
<EDI_DC40 SEGMENT="1">---Default value
<TABNAM>EDI_DC40</TABNAM>---Default value
<MANDT>999</MANDT>---Default value
<DOCREL>701</DOCREL>---Default value
<DIRECT>2</DIRECT>---Default value
<IDOCTYP>WPUBON01</IDOCTYP>---Default value
<MESTYP>WPUBON</MESTYP>---Default value
<SNDPOR>XML_PORT</SNDPOR>---Default value
<SNDPRT>KU</SNDPRT>---Default value
<SNDPRN>MV01</SNDPRN>---Parameterized, it can be MV01,MV03,MV04. In this case for MV01
<RCVPOR>SAPMGP</RCVPOR>---Default value
<RCVPRT>LS</RCVPRT>---Default value
<RCVPRN>MGPCLNT999</RCVPRN>---Default value
<CREDAT>20250220</CREDAT>—-Parameterized, Creation date of this file. YearMonthDate YYYYMMDD
<CRETIM>094700</CRETIM>—-Parameterized, Creation time of this file. HHMMSS
<ARCKEY>MV0120250220</ARCKEY>---Parameterized, {OutletCode}-{Date(YYYYMMDD)}
</EDI_DC40>---Default value
<E1WPB01 SEGMENT="1">---Default value
<VORGDATUM>20250217</VORGDATUM>---Parameterized, transacation date YearMonthDate YYYYMMDD
<BONNUMMER>CASMV01</BONNUMMER>---Parameterized, Means of payment (MOP)
<KUNDNR>CASMV01</KUNDNR>---Parameterized, Means of payment (MOP)
<BELEGWAERS>IDR</BELEGWAERS>---Default value , Currency
<E1WPB02 SEGMENT="1">---Default value
<VORGANGART>ZMOP</VORGANGART>---Default value
<QUALARTNR>ARTN</QUALARTNR>---Default value
<ARTNR>MEANS_OF_PAYMENT</ARTNR>---Default value
</E1WPB02>---Default value
<E1WPB06 SEGMENT="1">---Default value
<VORZEICHEN>-</VORZEICHEN>---Default value
<ZAHLART>ZMOP</ZAHLART>---Default value
<SUMME>25000000.00</SUMME>---Parameterized, Total sales amount of Gift Card based on MOP. In this case for MV01
</E1WPB06>---Default value
</E1WPB01>---Default value
</IDOC>---Default value
</WPUBON01>---Default value
 
3.WPUUMS
 
<WPUUMS01>---Default value
<IDOC BEGIN="1">---Default value
<EDI_DC40 SEGMENT="1">---Default value
<TABNAM>EDI_DC40</TABNAM>---Default value
<MANDT>999</MANDT>---Default value
<DOCREL>620</DOCREL>---Default value
<DIRECT>2</DIRECT>---Default value
<IDOCTYP>WPUUMS01</IDOCTYP>---Default value
<MESTYP>WPUUMS</MESTYP>---Default value
<SNDPOR>XML_PORT</SNDPOR>---Default value
<SNDPRT>KU</SNDPRT>---Default value
<SNDPRN>MV01</SNDPRN>---Parameterized, it can be MV01,MV03,MV04. In this case for MV01
<RCVPOR>SAPMGP</RCVPOR>---Default value
<RCVPRT>LS</RCVPRT>---Default value
<RCVPRN>MGPCLNT999</RCVPRN>---Default value
<CREDAT>20250220</CREDAT>---Parameterized, Creation date of this file. YearMonthDate YYYYMMDD
<CRETIM>094700</CRETIM>---Parameterized, Creation time of this file. HHMMSS
<ARCKEY>MV0120250220</ARCKEY>---Parameterized, {OutletCode}-{Date(YYYYMMDD)}
</EDI_DC40>---Default value
<E1WPU01 SEGMENT="1">---Default value
<BELEGDATUM>20250217</BELEGDATUM>---Parameterized, transaction date. YearMonthDate YYYYMMDD
<BELEGWAERS>IDR</BELEGWAERS>---Default value, Currency
<E1WPU02 SEGMENT="1">---Default value
<ARTNR>100014135582</ARTNR>---Parameterized, SAP article code that mapped with GCPG
<VORZMENGE>-</VORZMENGE>---Default value
<UMSMENGE>1</UMSMENGE>---Parameterized, Gift Card sales quantity
<UMSWERT>1000000.00</UMSWERT>---Parameterized, Total sales amount
</E1WPU02>---Default value
<E1WPU02 SEGMENT="1">---Default value
<ARTNR>100014135581</ARTNR>---Parameterized, SAP article code that mapped with GCPG
<VORZMENGE>-</VORZMENGE>---Default value
<UMSMENGE>4</UMSMENGE>---Parameterized, Gift Card sales quantity
<UMSWERT>8000000.00</UMSWERT>---Parameterized, Total sales amount
<E1WPU03 SEGMENT="1">—-Default value
<VORZEICHEN>-</VORZEICHEN>—-Default value
<KONDITION>ZDIS</KONDITION>—-Default value
<KONDVALUE>7000000.00</KONDVALUE>—-Parameterized, Discount amount (based on product category setup)
</E1WPU03>—-Default value
</E1WPU02>---Default value
</E1WPU01>---Default value
</IDOC>---Default value
</WPUUMS01>---Default value
