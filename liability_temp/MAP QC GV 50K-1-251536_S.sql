 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '9c1a2be6-da43-45f8-8e4f-a24a2176cb8f' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = '687befb7-331e-49b0-809a-c6688f74c205' AND cpg_code = 'a32472099c964c9ea48d7d543e590846'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 50000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '4baca109-b982-4234-b510-ce2fa062d933' AND outlet_code = '12b21b85-157c-4169-9dc3-e2d9106c7e7c' AND cpg_code = 'a32472099c964c9ea48d7d543e590846';
