 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = 'MAP' AND merchant_code = 'ME10240528140953000007' AND outlet_code = 'OU10240528141811000013' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410 SET activated_amount = activated_amount + 100000.00, purchased_amount = purchased_amount + 0, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53'; 
UPDATE gv_report_temp_liability_s_2410  SET activated_amount = activated_amount - 100000, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 0,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = 'MAP' AND merchant_code = '3852035a-ff19-45a3-8a1a-dd3a43263240' AND outlet_code = 'c9ae41e2-3b32-44ce-9ccd-5891cf286b78' AND cpg_code = '2be5416a47b24a7d858f14b1ee86ec53';
