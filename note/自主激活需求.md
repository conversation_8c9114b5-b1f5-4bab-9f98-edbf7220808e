
---

### **延迟自助激活功能 - 后台开发交接文档**

#### 1. 功能概述 (Overview)

本功能旨在为特定渠道（如 MAPCLUB_COM）的订单提供一种“延迟激活”机制。当订单完成后，客户不会立即收到已激活的实体券，而是会收到一封包含“激活链接”的邮件。客户需要通过该链接，完成邮箱OTP验证，才能手动将订单内的所有券激活。

**核心流程**: 订单完成 -> 发送激活邮件 -> 用户点击链接 -> 验证OTP -> 激活成功。

#### 2. 核心设计思想 (Core Design Principles)

1.  **性能与负载分离**: 使用 Redis 处理高频、临时的验证类请求（如Token查询、OTP验证），避免对核心业务数据库造成不必要的压力。
2.  **状态的临时性与持久性分离**:
    *   **Redis**: 管理激活流程中的临时状态（如Token有效性、OTP码），并利用其 TTL 机制自动处理过期，简化逻辑。
    *   **数据库 (`gv_self_activation_log`)**: 作为永久性审计日志，记录每个激活任务的核心信息和最终结果，用于排查和对账。
3.  **最小化侵入**: 仅在订单 `Receive` 节点增加判断逻辑，不改变现有券表 (`gv_voucher`) 的结构，仅在激活成功后更新其状态。

#### 3. 数据库与 Redis 设计 (Data Structure)

**3.1. 数据库表 (Database)**

*   **表名**: `gv_self_activation_log`
*   **用途**: 记录激活任务的生命周期。
*   **SQL 定义**:
    ```sql
    CREATE TABLE gv_self_activation_log (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        customer_order_code VARCHAR(100) NOT NULL COMMENT '客户订单号, 业务核心关联键',
        activation_token VARCHAR(255) NOT NULL UNIQUE COMMENT '激活URL中的唯一令牌',
        token_status VARCHAR(20) NOT NULL COMMENT '令牌状态: PENDING, ACTIVATED, EXPIRED',
        token_expiry_at DATETIME NOT NULL COMMENT '令牌的过期时间 (例如: 30天)',
        customer_email VARCHAR(255) NOT NULL COMMENT '接收激活邮件的客户邮箱',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer_order_code (customer_order_code)
    ) COMMENT '自助激活任务日志表';
    ```

**3.2. Redis 键值设计 (Redis Keys)**

1.  **Token 到订单号的映射**
    *   **用途**: 通过 `token` 快速查找 `customerOrderCode`，作为激活会话的入口。
    *   **Key**: `activation:token:{token}`
    *   **Value (String)**: `{customerOrderCode}` (例如: "CUST-ORD-12345")
    *   **TTL**: 30天 (与 `gv_self_activation_log` 表的 `token_expiry_at` 保持一致)

2.  **OTP (一次性密码) 存储**
    *   **用途**: 存储待验证的OTP，并自动过期。
    *   **Key**: `activation:otp:{token}`
    *   **Value (String)**: `{otp_code}` (例如: "12345678")
    *   **TTL**: 5分钟

#### 4. 核心业务流程 (Core Business Logic Flow)

**阶段一：订单处理 (`Receive` 节点)**

1.  **触发**: 订单状态流转到 `Receive`。
2.  **条件**: 获取订单的渠道信息 (e.g., `outletCode`)，检查是否在系统配置的 `delayed-channels` 列表中。
3.  **执行逻辑 (如果匹配)**:
    *   **券状态不变**: 保持该订单 (`CustomerOrderCode`) 下所有实体券的状态为 **`CREATED`**。
    *   **生成 Token**: 创建一个唯一的、高安全性的激活令牌 (`activation_token`)，例如使用 `UUID.randomUUID().toString()`。
    *   **持久化日志**: 在 `gv_self_activation_log` 表中插入一条记录，包含 `customer_order_code`, `activation_token`, `customer_email`，并将 `token_status` 设为 `PENDING`。
    *   **写入 Redis**: `SET activation:token:{token} {customerOrderCode} EX {30_days_in_seconds}`。
    *   **发送邮件**: **【异步任务 @Async】** 调用邮件服务，发送“激活通知邮件”。邮件内容包含一个链接，格式为 `https://your-frontend.com/activate?token={activation_token}`。

**阶段二：用户自助激活 (通过API接口实现)**

这一阶段由用户与前端页面交互触发后台 API 调用，详见下一章节。

#### 5. API 接口定义 (Self-Activation API Endpoints)

`@RestController`
`@RequestMapping("/self-activation")`

---

**5.1. 获取激活页面信息 (Get Activation Info)**

*   **用途**: 前端页面加载时，通过 Token 获取订单信息用于展示。
*   **接口**: `POST /self-activation/getInfo`
*   **请求体 (Request Body)**:
    ```json
    {
      "token": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8"
    }
    ```
*   **成功响应 (Success Response 200 OK)**:
    ```json
    {
      "customerOrderCode": "CUST-ORD-12345",
      "email": "<EMAIL>",
      "voucherCount": 5
    }
    ```
*   **核心处理逻辑**:
    1.  从请求体中获取 `token`。
    2.  根据 `token` 查询 Redis: `GET activation:token:{token}`。
    3.  **如果 Redis 中不存在**: 返回错误“激活链接不存在或已过期”。
    4.  **如果存在**: 从 Redis 获取 `customerOrderCode`。
    5.  使用 `customerOrderCode` 查询数据库，获取订单信息（如邮箱、关联的券数量）组装成 DTO 并返回。

---

**5.2. 请求发送OTP (Request OTP)**

*   **用途**: 用户点击“发送验证码”按钮时触发。
*   **接口**: `POST /self-activation/requestOtp`
*   **请求体 (Request Body)**:
    ```json
    {
      "token": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8"
    }
    ```
*   **成功响应 (Success Response 200 OK)**:
    ```json
    {
      "success": true,
      "message": "OTP has been sent to your email."
    }
    ```
*   **核心处理逻辑**:
    1.  验证 `token` 在 Redis 中是否依然有效。如果无效，返回错误。
    2.  生成一个8位数字 `otp`。
    3.  将 OTP 存入 Redis: `SET activation:otp:{token} {otp_code} EX 300` (5分钟过期)。
    4.  从 `gv_self_activation_log` 表中查询 `customer_email`。
    5.  **【异步任务 @Async】** 调用邮件服务，向该邮箱发送 OTP。
    6.  返回成功响应。

---

**5.3. 执行激活 (Activate Vouchers)**

*   **用途**: 用户输入OTP后，点击“立即激活”按钮。这是核心事务操作。
*   **接口**: `POST /self-activation/activate`
*   **请求体 (Request Body)**:
    ```json
    {
      "token": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
      "otp": "12345678"
    }
    ```
*   **成功响应 (Success Response 200 OK)**:
    ```json
    {
      "success": true,
      "message": "Vouchers activated successfully."
    }
    ```
*   **核心处理逻辑 (`@Transactional`)**:
    1.  从 Redis 查询 `token` 对应的 `customerOrderCode` 和 `otp`。
    2.  **验证**:
        *   `customerOrderCode` 是否存在？
        *   请求的 `otp` 是否与 Redis 中的 `otp` 一致？
        *   如果不一致或不存在，返回“OTP错误或已过期”。
    3.  **执行激活 (数据库操作)**:
        *   使用 `customerOrderCode`，将 `gv_voucher` 表中所有关联的、状态为 `CREATED` 的券，批量更新其状态为 `ACTIVATED`。
        *   更新 `gv_self_activation_log` 表中对应记录的 `token_status` 为 `ACTIVATED`。
    4.  **清理 Redis**:
        *   `DEL activation:token:{token}`
        *   `DEL activation:otp:{token}`
    5.  **【异步任务 @Async】** 发送“激活成功”通知邮件。
    6.  返回成功响应。

---

#### 6. 后台管理功能 (Admin Panel Feature)

*   **用途**: 客服人员手动为客户重发激活邮件。
*   **接口**: `POST /admin-activation/resendEmail`
*   **权限**: 需要后台管理员权限。
*   **请求体 (Request Body)**:
    ```json
    {
      "customerOrderCode": "CUST-ORD-12345",
      "newEmail": "<EMAIL>" // 可选字段
    }
    ```
*   **核心处理逻辑**:
    1.  使用 `customerOrderCode` 从 `gv_self_activation_log` 表查找 `PENDING` 状态的记录。如果找不到，返回错误。
    2.  从记录中获取 `activation_token` 和旧的 `customer_email`。
    3.  如果请求中包含了 `newEmail`，则将 `gv_self_activation_log` 表中的 `customer_email` 更新为新邮箱。
    4.  使用 `activation_token` 和最新的邮箱地址，**【异步任务 @Async】** 重新调用邮件服务发送激活邮件。

#### 7. 系统配置 (System Configuration)

在 `application.yml` 中配置需要走延迟激活流程的渠道列表。

```yaml
app:
  activation:
    delayed-channels:
      - "MAPCLUB_COM"
      - "MAPGV_COM"
```