# CLAUDE.md

本文档为 Claude Code (claude.ai/code) 提供在此代码库中工作时的指导。

## 项目概述

GVCore 是一个基于 Spring Boot 2.7.0 的多模块礼品券管理系统，使用 Maven 构建。系统主要处理礼品券的创建、分发、激活和核销等业务流程。

## 变更记录 (Changelog)

### 2025-01-15
- **新增自主激活功能 (Self-Activation)**：实现延迟激活机制，支持特定渠道通过邮件链接激活券
- **Redis 集成增强**：新增基于 Redis 的 Token 和 OTP 管理
- **数据库扩展**：新增 `gv_self_activation_log` 表记录激活任务生命周期

## 核心架构

### 模块结构图

```mermaid
graph TD
    A["(根) GVCore 礼品券管理系统"] --> B["gvcore-web"];
    A --> C["gvcore-service"];
    A --> D["gvcore-common"];
    A --> E["gvcore-backend"];
    A --> F["gvcore-report"];
    A --> G["SQL"];
    
    B --> B1["REST API 层"];
    B --> B2["Web 控制器"];
    B --> B3["Swagger 文档"];
    
    C --> C1["业务逻辑层"];
    C --> C2["自主激活服务"];
    C --> C3["问题处理模块"];
    
    D --> D1["公共组件"];
    D --> D2["分片配置"];
    D --> D3["通用工具"];
    
    E --> E1["后台服务"];
    E --> E2["异步任务"];
    E --> E3["管理接口"];
    
    F --> F1["报表服务"];
    F --> F2["数据统计"];
    
    G --> G1["数据库脚本"];
    G --> G2["迁移工具"];

    click B "./gvcore-web/CLAUDE.md" "查看 Web 模块文档"
    click C "./gvcore-service/CLAUDE.md" "查看 Service 模块文档"
    click D "./gvcore-common/CLAUDE.md" "查看 Common 模块文档"
    click E "./gvcore-backend/CLAUDE.md" "查看 Backend 模块文档"
    click F "./gvcore-report/CLAUDE.md" "查看 Report 模块文档"
    click G "./SQL/CLAUDE.md" "查看 SQL 模块文档"
```

### 模块索引

| 模块 | 路径 | 端口 | 职责描述 | 主要功能 |
|------|------|------|----------|----------|
| **gvcore-web** | `./gvcore-web/` | 8080 | Web API 层，提供 REST 接口和前端交互 | 券管理、分发、自主激活API |
| **gvcore-service** | `./gvcore-service/` | - | 业务逻辑层，处理核心业务逻辑 | 券生命周期管理、问题处理、自主激活 |
| **gvcore-common** | `./gvcore-common/` | - | 公共组件层，包含通用工具、配置和模型 | 分片配置、枚举定义、异常处理 |
| **gvcore-backend** | `./gvcore-backend/` | 8081 | 后台服务层，处理异步任务和后台管理 | 后台管理、异步处理、管理员功能 |
| **gvcore-report** | `./gvcore-report/` | 8082 | 报表模块，处理数据统计和报表生成 | 业务报表、数据分析、统计导出 |
| **SQL** | `./SQL/` | - | 数据库脚本和工具模块 | 数据库迁移、表结构管理 |

### 技术栈

#### 核心框架
- **Spring Boot**: 2.7.0
- **Spring Cloud**: 2021.0.3
- **Maven**: 多模块构建
- **Java**: 8+

#### 数据存储
- **MySQL**: 8.0+ (主数据库，支持读写分离)
- **ShardingSphere**: 4.1.1 (数据分片中间件)
- **Redis**: 5.0+ (缓存、会话管理、自主激活Token存储)
- **MongoDB**: FileCloud 功能数据存储

#### 消息与通信
- **RocketMQ**: 4.9+ (异步消息处理)
- **Spring Boot Admin**: 2.7.0 (应用监控)

#### 服务发现与配置
- **Eureka**: 服务注册发现
- **Apollo**: 生产环境配置中心

#### 文档与监控
- **Swagger**: 2.7.0 (API文档)
- **MyBatis**: 2.2.0 (ORM框架)
- **Druid**: 1.2.3 (连接池)

#### 文件存储
- **阿里云 OSS**: 文件上传下载
- **FileCloud**: 1.7.6 (文件管理服务)

### 数据库分片策略

系统使用 ShardingSphere 进行数据水平分片，核心分片表包括：

#### 分片表配置
| 表名 | 分片数量 | 分片键 | 分片算法类 |
|------|----------|--------|------------|
| `gv_voucher` | 64张表 | `voucher_code` | `VoucherShardingConfig` |
| `gv_transaction_data` | 64张表 | `voucher_code` | `TransactionDataShardingConfig` |
| `gv_voucher_log` | 64张表 | `voucher_code` | `VoucherLogShardingConfig` |

#### 分片算法
- **精确分片**：基于券码 (`voucher_code`) 进行哈希取模分片
- **范围分片**：支持范围查询的分片策略 (`VoucherRangeShardingConfig`)
- **绑定表**：三个核心表为绑定表，确保关联查询效率

## 运行与开发

### 环境依赖

确保以下服务已启动并正确配置：

#### 必需服务
```bash
# 数据库 (读写分离配置)
MySQL 8.0+ 

# 缓存服务
Redis 5.0+ (Database: 5)

# 消息队列
RocketMQ 4.9+ (NameServer: *************:9876)

# 文档数据库 (FileCloud 功能)
MongoDB 3.4+

# 服务注册中心 (开发环境)
Eureka (http://eureka-dev.gtech.asia/eureka/)
```

### 应用启动

#### 单模块启动 (开发环境)
```bash
# Web 服务 (主要API服务)
cd gvcore-web
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Backend 服务 (后台管理)
cd gvcore-backend  
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Report 服务 (报表服务)
cd gvcore-report
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### 服务端口
- **gvcore-web**: 8080 (主API服务)
- **gvcore-backend**: 8081 (后台管理服务)
- **gvcore-report**: 8082 (报表服务)

### 构建命令

```bash
# 清理并编译所有模块
mvn clean compile

# 运行所有测试
mvn test

# 打包 (开发环境)
mvn clean package -Pdev

# 跳过测试打包 (快速构建)
mvn clean package -DskipTests

# 特定模块操作
mvn test -pl gvcore-service
mvn clean package -pl gvcore-web -DskipTests
```

### 环境配置

#### Profile 说明
- **dev**: 开发环境，连接开发数据库和中间件
- **junit**: 单元测试环境
- **生产环境**: 使用 Apollo 配置中心管理配置

## 核心业务特性

### 1. 自主激活功能 (Self-Activation) ⭐

延迟自助激活功能，允许特定渠道的订单通过邮件链接激活券，这是当前分支的核心新功能。

#### 业务流程
```mermaid
sequenceDiagram
    participant C as Customer
    participant E as Email
    participant F as Frontend
    participant API as Self-Activation API
    participant R as Redis
    participant DB as Database
    participant MQ as Message Queue

    Note over C,MQ: 1. 订单处理阶段
    DB->>API: 订单完成 (特定渠道)
    API->>DB: 创建激活日志
    API->>R: 存储 Token (30天TTL)
    API->>MQ: 异步发送激活邮件
    MQ->>E: 邮件发送
    E->>C: 激活邮件

    Note over C,MQ: 2. 用户激活阶段
    C->>F: 点击激活链接
    F->>API: getInfo(token)
    API->>R: 验证 Token
    R-->>API: 返回订单信息
    API-->>F: 显示激活页面

    F->>API: requestOtp(token)
    API->>R: 生成并存储 OTP (5分钟TTL)
    API->>MQ: 异步发送 OTP 邮件
    MQ->>E: OTP 邮件发送
    E->>C: OTP 验证码

    C->>F: 输入 OTP
    F->>API: activate(token, otp)
    API->>R: 验证 OTP
    API->>DB: 批量激活券状态
    API->>DB: 更新激活日志
    API->>R: 清理 Redis 数据
    API->>MQ: 异步发送成功通知
```

#### 关键组件

**API 接口** (`/self-activation/*`):
- `POST /self-activation/getInfo` - 获取激活页面信息
- `POST /self-activation/requestOtp` - 请求 OTP 验证码  
- `POST /self-activation/activate` - 执行激活操作

**核心服务类**:
- `SelfActivationServiceImpl` - 自主激活业务逻辑
- `SelfActivationController` - REST API 控制器
- `AdminActivationController` - 后台管理接口

**数据存储**:
- **数据库表**: `gv_self_activation_log` - 激活任务生命周期记录
- **Redis 键设计**:
  - `activation:token:{token}` → `{customerOrderCode}` (TTL: 30天)
  - `activation:otp:{token}` → `{otp_code}` (TTL: 5分钟)

#### 配置管理

**渠道配置** (`application-self-activation-config.yml`):
```yaml
app:
  activation:
    delayed-channels:
      - MAPCLUB_COM
      - MAPGV_COM
      # 需要延迟激活的渠道代码列表
```

### 2. 券生命周期管理

券的完整生命周期状态流转：

```
CREATED (新建) → ACTIVATED (已激活) → USED (已使用) → EXPIRED (已过期)
                      ↓
              CANCELLED (已取消) / SUSPENDED (暂停)
```

### 3. 分发管理

支持多种券分发方式：
- **邮件分发**: 直接发送激活券到客户邮箱
- **API 分发**: 通过 API 接口批量分发
- **手动分发**: 后台管理员手动操作分发
- **自助激活**: 延迟激活，用户通过邮件链接激活

### 4. 问题处理 (Issue Handling)

完整的券异常处理流程，包括：
- **券重发** (`IssueHandlerReissueService`)
- **券取消** (`IssueHandlerCancelSalesService`) 
- **有效期修改** (`IssueHandlerChangeExpiryService`)
- **批量激活** (`IssueHandlerBulkActiveService`)
- **数据验证** (`IssueHandlerValidateService`)

## 测试策略

### 单元测试

```bash
# 运行所有测试
mvn test

# 运行特定模块测试  
mvn test -pl gvcore-service

# 运行特定测试类
mvn test -Dtest=VoucherServiceTest

# 运行特定测试方法
mvn test -Dtest=SelfActivationServiceTest#testCreateActivationTask
```

### 测试环境配置

测试类位于 `src/test/java` 目录，使用：
- Spring Boot Test 框架
- JUnit 测试框架  
- 内嵌 H2 数据库 (单元测试)
- TestContainers (集成测试)

## 编码规范

### 包结构约定

```
com.gtech.gvcore
├── common/                 # 公共组件
│   ├── config/            # 配置类 (分片配置、Redis配置等)
│   ├── enums/             # 枚举定义 (状态枚举、错误码枚举)
│   ├── constants/         # 常量定义
│   ├── exception/         # 异常处理
│   ├── request/           # 请求模型 (包含 selfactivation 包)
│   └── response/          # 响应模型
├── service/               # 业务逻辑层
│   ├── impl/              # 服务实现类
│   │   ├── issuehandle/   # 问题处理模块
│   │   └── SelfActivationServiceImpl # 自主激活服务
│   └── report/            # 报表服务
├── dao/                   # 数据访问层
│   ├── mapper/            # MyBatis 映射器接口
│   └── model/             # 数据模型 (实体类)
└── web/                   # Web 控制器层
    └── controller/        # REST 控制器
```

### 关键注解使用

- `@TrackObjects` - 用于方法级别对象监控和审计
- `@EnableAsync` - 异步任务支持 (邮件发送等)
- `@Transactional` - 事务管理 (特别是券激活操作)
- `@Slf4j` - 日志记录
- `@Api`/`@ApiOperation` - Swagger API 文档

### 通用响应格式

所有 API 统一返回 `Result<T>` 格式：

```json
{
  "code": "200",           
  "message": "操作成功",    
  "data": {               
    
  }
}
```

### 错误处理

使用 `ResultErrorCodeEnum` 定义统一错误码：
- `PARAMTER_ERROR` - 参数错误
- `FAILED` - 操作失败  
- `SUCCESS` - 操作成功

## AI 使用指引

### 代码分析要点

1. **分片逻辑**: 重点关注 `VoucherShardingConfig` 等分片算法的实现
2. **事务边界**: 券激活、状态变更等操作的事务处理
3. **异步处理**: `@Async` 标注的邮件发送等异步操作
4. **Redis 设计**: Token 和 OTP 的存储策略和 TTL 设置
5. **业务状态**: 券状态流转的业务规则

### 常见任务指导

**添加新的券状态**:
1. 在 `VoucherStatusEnum` 中添加新状态
2. 更新数据库约束和默认值
3. 修改相关业务逻辑和状态流转

**扩展自主激活功能**:
1. 检查 `SelfActivationServiceImpl` 的现有逻辑
2. 关注 Redis 键设计和 TTL 策略
3. 确保事务边界正确处理

**问题排查**:
1. 查看 `application-dev.properties` 中的配置
2. 检查日志中的 `trace_id` 进行链路跟踪
3. 验证分片配置和数据库连接

### 重要配置项

#### 数据库分片配置
```properties
# 券表分片 (64张表)
spring.shardingsphere.sharding.tables.gv_voucher.actual-data-nodes=ds.gv_voucher_$->{0..63}
spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.sharding-column=voucher_code
spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.precise-algorithm-class-name=com.gtech.gvcore.common.config.VoucherShardingConfig
```

#### Redis 配置
```properties
spring.redis.database=5
spring.redis.host=*************
spring.redis.port=6379
spring.redis.lettuce.pool.max-active=8
```

#### RocketMQ 配置  
```properties
rocketmq.name-server=*************:9876
rocketmq.producer.group=titan_gvcore
rocketmq.producer.topic=gvcore_router_engine_dev
```

#### 监控和日志
```properties
# 日志格式包含分布式追踪信息
logging.pattern.level = trace_id=%mdc{trace_id} span_id=%mdc{span_id} trace_flags=%mdc{trace_flags} %5p
```

## 常见问题

### 分片配置问题
**症状**: 查询或插入数据时报找不到表的错误
**排查**:
1. 检查 `VoucherShardingConfig` 分片算法实现
2. 验证分片键 (`voucher_code`) 是否正确传入
3. 确认分片表是否已在数据库中创建 (0-63 共64张表)

### Redis 连接问题  
**症状**: 自主激活功能中 Token 验证失败
**排查**:
1. 验证 Redis 连接配置和 database 编号 (database=5)
2. 检查连接池配置和超时设置
3. 确认 Redis 服务状态和内存使用情况

### 消息队列问题
**症状**: 邮件发送异步任务失败
**排查**:  
1. 确认 RocketMQ NameServer 地址配置正确
2. 检查 Topic 和 Producer Group 是否已创建
3. 验证消息发送方和消费方的配置一致性

### 文件上传问题
**症状**: OSS 文件操作失败
**排查**:
1. 检查阿里云 OSS 配置和权限设置
2. 确保 `access-key` 和 `endpoint` 配置正确
3. 验证 bucket 权限和网络连接

### 自主激活功能问题
**症状**: Token 验证失败或 OTP 验证不通过
**排查**:
1. 检查 Redis 中 Token 和 OTP 的存储状态
2. 验证 TTL 设置是否符合业务需求 (Token: 30天, OTP: 5分钟)  
3. 确认邮件模板配置和异步任务执行状态
4. 检查 `gv_self_activation_log` 表中的记录状态