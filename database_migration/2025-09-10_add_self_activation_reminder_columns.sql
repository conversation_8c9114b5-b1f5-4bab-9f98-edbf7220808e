-- Adds reminder tracking columns for self-activation reminders
ALTER TABLE `gv_self_activation_log`
  ADD COLUMN `reminder_count` INT NOT NULL DEFAULT 0 COMMENT 'Number of reminder emails sent' AFTER `updated_at`,
  ADD COLUMN `last_reminded_at` DATETIME NULL DEFAULT NULL COMMENT 'Last reminder sent time' AFTER `reminder_count`;

-- Index to accelerate reminder queries by status and time
CREATE INDEX `idx_token_status_last_reminded` ON `gv_self_activation_log` (`token_status`, `last_reminded_at`);
